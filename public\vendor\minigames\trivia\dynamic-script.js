// --- DYNAMIC TRIVIA GAME SCRIPT ---
// This script uses server-provided configuration and questions

// --- DOM & UI ELEMENTS ---
const screens = {
    welcome: document.getElementById("welcome-screen"),
    ready: document.getElementById("get-ready-screen"),
    game: document.getElementById("game-screen"),
    end: document.getElementById("end-screen"),
};

const startBtn = document.getElementById("start-btn");
const restartBtn = document.getElementById("restart-btn");
const nameInput = document.getElementById("name-input");
const guestIdInput = document.getElementById("guest-id");
const guestNameInput = document.getElementById("guest-name");
const nameError = document.getElementById("name-error");
const countdownNumberEl = document.getElementById("countdown-number");

const playerNameEl = document.getElementById("player-name");
const scoreEl = document.getElementById("score");
const finalScoreEl = document.getElementById("final-score");
const questionEl = document.getElementById("question");
const answersEl = document.getElementById("answers");
const questionNumberEl = document.getElementById("question-number");
const totalQuestionsEl = document.getElementById("total-questions");
const timerBar = document.getElementById("timer-bar");
const scorePopupEl = document.getElementById("score-popup");

// Answer button styles
const answerShapes = [
    {
        color: "bg-pink-400",
        shape: `<svg viewBox="0 0 100 100"><polygon points="50,10 90,90 10,90" fill="white"/></svg>`,
    },
    {
        color: "bg-purple-400",
        shape: `<svg viewBox="0 0 100 100"><polygon points="50,10 90,50 50,90 10,50" fill="white"/></svg>`,
    },
    {
        color: "bg-rose-400",
        shape: `<svg viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="white"/></svg>`,
    },
    {
        color: "bg-fuchsia-400",
        shape: `<svg viewBox="0 0 100 100"><rect x="10" y="10" width="80" height="80" fill="white"/></svg>`,
    },
];

// --- GAME STATE ---
let currentQuestionIndex = 0;
let score = 0;
let timer;
let timeLeft;
let audioReady = false;
let questionStartTime;
let gameAnswers = []; // Store all answers for submission
let timeTaken = []; // Store time taken for each question

// Get questions from server configuration
const triviaQuestions = window.gameConfig.questions;
const totalTimeForQuestion = triviaQuestions[0]?.timeLimit || 30;

// --- AUDIO & HAPTICS ---
let sounds;
function setupSounds() {
    sounds = {
        lobby: new Tone.Synth({
            oscillator: { type: "pulse", width: 0.6 },
            envelope: { attack: 0.01, decay: 0.1, sustain: 0.2, release: 0.3 },
        }).toDestination(),
        click: new Tone.Synth({
            oscillator: { type: "triangle" },
            envelope: {
                attack: 0.005,
                decay: 0.1,
                sustain: 0.05,
                release: 0.1,
            },
        }).toDestination(),
        correct: new Tone.Synth({
            oscillator: { type: "sine" },
            envelope: { attack: 0.01, decay: 0.2, sustain: 0.2, release: 0.2 },
        }).toDestination(),
        incorrect: new Tone.Synth({
            oscillator: { type: "sawtooth" },
            envelope: { attack: 0.01, decay: 0.2, sustain: 0, release: 0.2 },
        }).toDestination(),
        fanfare: new Tone.Synth({
            oscillator: { type: "fatsawtooth", count: 3, spread: 30 },
            envelope: { attack: 0.01, decay: 0.4, sustain: 0.4, release: 0.4 },
        }).toDestination(),
    };
    audioReady = true;
    playLobbyMusic();
}

function playLobbyMusic() {
    if (!audioReady || !sounds.lobby) return;
    const notes = ["C4", "E4", "G4", "C5"];
    let i = 0;
    Tone.Transport.scheduleRepeat((time) => {
        sounds.lobby.triggerAttackRelease(
            notes[i++ % notes.length],
            "8n",
            time
        );
    }, "8n");
    Tone.Transport.start();
}

function playSound(soundName) {
    if (!audioReady) return;
    const now = Tone.now();
    switch (soundName) {
        case "click":
            sounds.click.triggerAttackRelease("C5", "16n", now);
            break;
        case "correct":
            sounds.correct.triggerAttackRelease("C5", "16n", now);
            sounds.correct.triggerAttackRelease("E5", "16n", now + 0.1);
            sounds.correct.triggerAttackRelease("G5", "16n", now + 0.2);
            break;
        case "incorrect":
            sounds.incorrect.triggerAttackRelease("G#3", "8n", now);
            sounds.incorrect.triggerAttackRelease("G3", "8n", now + 0.1);
            break;
        case "fanfare":
            const fanfareNotes = ["C4", "E4", "G4", "C5", "G5", "C5", "G5"];
            fanfareNotes.forEach((note, i) => {
                sounds.fanfare.triggerAttackRelease(note, "16n", now + i * 0.1);
            });
            break;
    }
}

function triggerVibration(duration = 50) {
    if (window.navigator && window.navigator.vibrate) {
        window.navigator.vibrate(duration);
    }
}

// --- GAME FLOW ---
function switchScreen(screenName) {
    Object.values(screens).forEach((s) => s.classList.remove("active"));
    if (screens[screenName]) screens[screenName].classList.add("active");
}

async function startGame() {
    let playerName;

    // Get player name from guest data (automatically provided) or input (legacy)
    if (window.gameConfig.guestName) {
        playerName = window.gameConfig.guestName;
    } else if (guestIdInput && guestNameInput) {
        playerName = guestNameInput.value;
    } else {
        playerName = nameInput.value.trim();
        if (playerName === "") {
            nameError.classList.remove("hidden");
            return;
        }
    }

    if (Tone.context.state !== "running") {
        await Tone.start();
        setupSounds();
    }

    Tone.Transport.stop();
    Tone.Transport.cancel();
    nameError.classList.add("hidden");
    playerNameEl.textContent = playerName;

    // Initialize game state
    currentQuestionIndex = 0;
    score = 0;
    gameAnswers = [];
    timeTaken = [];

    switchScreen("ready");
    let countdown = 3;
    countdownNumberEl.textContent = countdown;

    const countdownInterval = setInterval(() => {
        countdown--;
        if (countdown > 0) {
            countdownNumberEl.textContent = countdown;
        } else {
            clearInterval(countdownInterval);
            switchScreen("game");
            updateScoreDisplay();
            loadQuestion();
        }
    }, 1000);
}

function restartGame() {
    switchScreen("welcome");
    if (nameInput) nameInput.value = "";
    playLobbyMusic();
}

function loadQuestion() {
    clearAnswerStyles();

    if (currentQuestionIndex >= triviaQuestions.length) {
        submitGameResults();
        return;
    }

    const q = triviaQuestions[currentQuestionIndex];
    questionEl.textContent = q.question;
    questionNumberEl.textContent = currentQuestionIndex + 1;
    totalQuestionsEl.textContent = triviaQuestions.length;

    answersEl.innerHTML = "";
    const answers = q.options || q.answers; // Support both new (options) and legacy (answers) format
    answers.forEach((ans, i) => {
        const s = answerShapes[i % answerShapes.length];
        const btn = document.createElement("button");
        btn.className = `answer-btn ${s.color}`;
        btn.dataset.index = i;
        btn.innerHTML = `<div class="answer-shape">${s.shape}</div><span class="answer-text">${ans}</span>`;
        btn.addEventListener("click", selectAnswer);
        answersEl.appendChild(btn);
    });

    startTimer();
}

function startTimer() {
    questionStartTime = Date.now();
    const currentQuestion = triviaQuestions[currentQuestionIndex];
    const questionTimeLimit = currentQuestion.timeLimit || totalTimeForQuestion;

    timeLeft = questionTimeLimit;
    timerBar.style.transition = "none";
    timerBar.style.width = "100%";
    void timerBar.offsetWidth;
    timerBar.style.transition = `width ${questionTimeLimit}s linear`;
    timerBar.style.width = "0%";

    clearInterval(timer);
    timer = setInterval(() => {
        timeLeft--;
        if (timeLeft <= 0) {
            clearInterval(timer);
            handleAnswer(-1); // Timeout
        }
    }, 1000);
}

function selectAnswer(e) {
    triggerVibration();
    playSound("click");
    clearInterval(timer);
    handleAnswer(parseInt(e.currentTarget.dataset.index));
}

function handleAnswer(selectedIndex) {
    const q = triviaQuestions[currentQuestionIndex];
    const buttons = answersEl.querySelectorAll("button");
    buttons.forEach((b) => (b.disabled = true));

    const answerTime = Date.now();
    const questionTime = (answerTime - questionStartTime) / 1000;

    // Store answer and time
    gameAnswers[currentQuestionIndex] = selectedIndex;
    timeTaken[currentQuestionIndex] = questionTime;

    let points = 0;
    const correctAnswer =
        q.correct_answer !== undefined ? q.correct_answer : q.correct; // Support both formats
    if (selectedIndex === correctAnswer) {
        const questionTimeLimit =
            q.time_limit || q.timeLimit || totalTimeForQuestion;
        if (questionTime < questionTimeLimit) {
            const timeRatio = 1 - questionTime / questionTimeLimit;
            points = Math.floor((q.points || 100) * (0.5 + 0.5 * timeRatio)); // Base 50% + time bonus 50%
            score += points;
        }

        scorePopupEl.textContent = `+${points}`;
        scorePopupEl.classList.add("show");
        playSound("correct");
        triggerVibration(200);
        updateScoreDisplay();
    } else {
        playSound("incorrect");
        triggerVibration([100, 50, 100]);
        if (selectedIndex !== -1) {
            buttons[selectedIndex].classList.add("incorrect-answer-selected");
        }
    }

    buttons.forEach((b, i) => {
        b.classList.add(
            i === correctAnswer ? "correct-answer" : "incorrect-answer"
        );
    });

    setTimeout(() => {
        currentQuestionIndex++;
        loadQuestion();
    }, 2500);
}

function updateScoreDisplay() {
    scoreEl.textContent = score;
}

function clearAnswerStyles() {
    answersEl.querySelectorAll("button").forEach((b) => {
        b.disabled = false;
        b.classList.remove(
            "correct-answer",
            "incorrect-answer",
            "incorrect-answer-selected"
        );
    });
    scorePopupEl.classList.remove("show");
}

async function submitGameResults() {
    try {
        // Create form data for submission
        const formData = new FormData();

        // Add answers as individual form fields
        triviaQuestions.forEach((question, index) => {
            if (gameAnswers[index] !== undefined) {
                formData.append(`answers[${question.id}]`, gameAnswers[index]);
            }
        });

        // Add CSRF token
        formData.append("_token", window.gameConfig.csrfToken);

        const response = await fetch(window.gameConfig.submitUrl, {
            method: "POST",
            body: formData,
        });

        if (response.ok) {
            // Redirect to leaderboard (the controller handles this)
            window.location.href = response.url;
        } else {
            const result = await response.json();
            alert(
                "Error submitting game: " + (result.error || "Unknown error")
            );
            showEndScreen();
        }
    } catch (error) {
        console.error("Error submitting game:", error);
        alert("Error submitting game. Please try again.");
        showEndScreen();
    }
}

function showEndScreen(result = null) {
    switchScreen("end");
    finalScoreEl.textContent = score;

    if (result) {
        // Add result information to end screen
        const endScreen = screens.end;
        const existingResult = endScreen.querySelector(".game-result");
        if (existingResult) existingResult.remove();

        const resultDiv = document.createElement("div");
        resultDiv.className = "game-result mt-4 p-4 bg-pink-50 rounded-lg";
        resultDiv.innerHTML = `
            <p class="text-lg font-bold text-pink-800">Your Rank: #${result.rank}</p>
            <p class="text-sm text-pink-600">${result.correct_answers}/${result.total_questions} correct answers</p>
            <a href="${result.leaderboard_url}" class="inline-block mt-3 px-6 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors">
                View Leaderboard
            </a>
        `;
        endScreen.appendChild(resultDiv);
    }

    playSound("fanfare");
}

// --- INITIALIZATION ---
function createFloralAnimation() {
    const container = document.querySelector(".background-container");
    if (!container) return;

    const flowersList = document.createElement("ul");
    flowersList.classList.add("flowers");
    container.appendChild(flowersList);

    const flowerCount = 20;
    const flowerSvgs = [
        `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12,16.2C11.3,16.2 10.6,16 10,15.7C9.4,15.4 9,14.9 8.7,14.4C8.4,13.8 8.2,13.1 8.2,12.5C8.2,11.8 8.4,11.2 8.7,10.6C9,10 9.4,9.6 10,9.3C10.6,9 11.3,8.8 12,8.8C12.7,8.8 13.4,9 14,9.3C14.6,9.6 15,10 15.3,10.6C15.6,11.2 15.8,11.8 15.8,12.5C15.8,13.1 15.6,13.8 15.3,14.4C15,14.9 14.6,15.4 14,15.7C13.4,16 12.7,16.2 12,16.2M12,2C11.3,2 10.6,2.2 10,2.5C9.4,2.8 8.9,3.2 8.4,3.7C8,4.2 7.6,4.8 7.3,5.5C7.1,6.1 6.9,6.8 6.9,7.5C6.9,8.2 7.1,8.9 7.3,9.5C7.6,10.2 8,10.8 8.4,11.3C8.9,11.8 9.4,12.2 10,12.5C10.6,12.8 11.3,13 12,13C12.7,13 13.4,12.8 14,12.5C14.6,12.2 15.1,11.8 15.6,11.3C16,10.8 16.4,10.2 16.7,9.5C16.9,8.9 17.1,8.2 17.1,7.5C17.1,6.8 16.9,6.1 16.7,5.5C16.4,4.8 16,4.2 15.6,3.7C15.1,3.2 14.6,2.8 14,2.5C13.4,2.2 12.7,2 12,2M19,12C19,12.7 18.8,13.4 18.5,14C18.2,14.6 17.8,15.1 17.3,15.6C16.8,16 16.2,16.4 15.5,16.7C14.9,16.9 14.2,17.1 13.5,17.1C12.8,17.1 12.1,16.9 11.5,16.7C10.8,16.4 10.2,16 9.7,15.6C9.2,15.1 8.8,14.6 8.5,14C8.2,13.4 8,12.7 8,12C8,11.3 8.2,10.6 8.5,10C8.8,9.4 9.2,8.9 9.7,8.4C10.2,8 10.8,7.6 11.5,7.3C12.1,7.1 12.8,6.9 13.5,6.9C14.2,6.9 14.9,7.1 15.5,7.3C16.2,7.6 16.8,8 17.3,8.4C17.8,8.9 18.2,9.4 18.5,10C18.8,10.6 19,11.3 19,12Z" /></svg>`,
    ];
    const colors = ["#fbcfe8", "#fce7f3", "#f3e8ff", "#fdf2f8"];

    for (let i = 0; i < flowerCount; i++) {
        const li = document.createElement("li");
        const size = Math.random() * 40 + 20;
        li.style.width = `${size}px`;
        li.style.height = `${size}px`;
        li.style.left = `${Math.random() * 100}%`;
        li.style.animationDuration = `${Math.random() * 15 + 10}s`;
        li.style.animationDelay = `${Math.random() * 10}s`;
        li.style.color = colors[Math.floor(Math.random() * colors.length)];
        li.innerHTML =
            flowerSvgs[Math.floor(Math.random() * flowerSvgs.length)];
        flowersList.appendChild(li);
    }
}

// --- EVENT LISTENERS ---
if (startBtn) {
    startBtn.addEventListener("click", () => {
        triggerVibration();
        startGame();
    });
}

if (restartBtn) {
    restartBtn.addEventListener("click", () => {
        triggerVibration();
        restartGame();
    });
}

// --- INITIALIZATION ---
setupSounds();
createFloralAnimation();
