// --- DYNAMIC TRIVIA GAME SCRIPT ---
// This script uses server-provided configuration and questions

// --- DOM & UI ELEMENTS ---
const screens = {
    welcome: document.getElementById("welcome-screen"),
    ready: document.getElementById("get-ready-screen"),
    game: document.getElementById("game-screen"),
    end: document.getElementById("end-screen"),
};

const startBtn = document.getElementById("start-btn");
const restartBtn = document.getElementById("restart-btn");
const nameInput = document.getElementById("name-input");
const guestIdInput = document.getElementById("guest-id");
const guestNameInput = document.getElementById("guest-name");
const nameError = document.getElementById("name-error");
const countdownNumberEl = document.getElementById("countdown-number");

const playerNameEl = document.getElementById("player-name");
const scoreEl = document.getElementById("score");
const finalScoreEl = document.getElementById("final-score");
const questionEl = document.getElementById("question");
const answersEl = document.getElementById("answers");
const questionNumberEl = document.getElementById("question-number");
const totalQuestionsEl = document.getElementById("total-questions");
const timerBar = document.getElementById("timer-bar");
const scorePopupEl = document.getElementById("score-popup");

// Answer button styles
const answerShapes = [
    {
        color: "bg-pink-400",
        shape: `<svg viewBox="0 0 100 100"><polygon points="50,10 90,90 10,90" fill="white"/></svg>`,
    },
    {
        color: "bg-purple-400",
        shape: `<svg viewBox="0 0 100 100"><polygon points="50,10 90,50 50,90 10,50" fill="white"/></svg>`,
    },
    {
        color: "bg-rose-400",
        shape: `<svg viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="white"/></svg>`,
    },
    {
        color: "bg-fuchsia-400",
        shape: `<svg viewBox="0 0 100 100"><rect x="10" y="10" width="80" height="80" fill="white"/></svg>`,
    },
];

// --- GAME STATE ---
let currentQuestionIndex = 0;
let score = 0;
let timer;
let timeLeft;
let audioReady = false;
let questionStartTime;
let gameAnswers = []; // Store all answers for submission
let timeTaken = []; // Store time taken for each question

// Get questions from server configuration
const triviaQuestions = window.gameConfig.questions;
const totalTimeForQuestion =
    triviaQuestions[0]?.time_limit || triviaQuestions[0]?.timeLimit || 20;

// --- AUDIO & HAPTICS ---
let sounds;
function setupSounds() {
    sounds = {
        lobby: new Tone.Synth({
            oscillator: { type: "pulse", width: 0.6 },
            envelope: { attack: 0.01, decay: 0.1, sustain: 0.2, release: 0.3 },
        }).toDestination(),
        click: new Tone.Synth({
            oscillator: { type: "triangle" },
            envelope: {
                attack: 0.005,
                decay: 0.1,
                sustain: 0.05,
                release: 0.1,
            },
        }).toDestination(),
        correct: new Tone.Synth({
            oscillator: { type: "sine" },
            envelope: { attack: 0.01, decay: 0.2, sustain: 0.2, release: 0.2 },
        }).toDestination(),
        incorrect: new Tone.Synth({
            oscillator: { type: "sawtooth" },
            envelope: { attack: 0.01, decay: 0.2, sustain: 0, release: 0.2 },
        }).toDestination(),
        fanfare: new Tone.Synth({
            oscillator: { type: "fatsawtooth", count: 3, spread: 30 },
            envelope: { attack: 0.01, decay: 0.4, sustain: 0.4, release: 0.4 },
        }).toDestination(),
    };
    audioReady = true;
    playLobbyMusic();
}

function playLobbyMusic() {
    if (!audioReady || !sounds.lobby) return;
    const notes = ["C4", "E4", "G4", "C5"];
    let i = 0;
    Tone.Transport.scheduleRepeat((time) => {
        sounds.lobby.triggerAttackRelease(
            notes[i++ % notes.length],
            "8n",
            time
        );
    }, "8n");
    Tone.Transport.start();
}

function playSound(soundName) {
    if (!audioReady) return;
    const now = Tone.now();
    switch (soundName) {
        case "click":
            sounds.click.triggerAttackRelease("C5", "16n", now);
            break;
        case "correct":
            sounds.correct.triggerAttackRelease("C5", "16n", now);
            sounds.correct.triggerAttackRelease("E5", "16n", now + 0.1);
            sounds.correct.triggerAttackRelease("G5", "16n", now + 0.2);
            break;
        case "incorrect":
            sounds.incorrect.triggerAttackRelease("G#3", "8n", now);
            sounds.incorrect.triggerAttackRelease("G3", "8n", now + 0.1);
            break;
        case "fanfare":
            const fanfareNotes = ["C4", "E4", "G4", "C5", "G5", "C5", "G5"];
            fanfareNotes.forEach((note, i) => {
                sounds.fanfare.triggerAttackRelease(note, "16n", now + i * 0.1);
            });
            break;
    }
}

function triggerVibration(duration = 50) {
    if (window.navigator && window.navigator.vibrate) {
        window.navigator.vibrate(duration);
    }
}

// --- GAME FLOW ---
function switchScreen(screenName) {
    Object.values(screens).forEach((s) => s.classList.remove("active"));
    if (screens[screenName]) screens[screenName].classList.add("active");
}

async function startGame() {
    let playerName;

    // Get player name from guest data (automatically provided) or input (legacy)
    if (window.gameConfig.guestName) {
        playerName = window.gameConfig.guestName;
    } else if (guestIdInput && guestNameInput) {
        playerName = guestNameInput.value;
    } else {
        playerName = nameInput.value.trim();
        if (playerName === "") {
            nameError.classList.remove("hidden");
            return;
        }
    }

    if (Tone.context.state !== "running") {
        await Tone.start();
        setupSounds();
    }

    Tone.Transport.stop();
    Tone.Transport.cancel();
    nameError.classList.add("hidden");
    playerNameEl.textContent = playerName;

    // Initialize game state
    currentQuestionIndex = 0;
    score = 0;
    gameAnswers = [];
    timeTaken = [];

    switchScreen("ready");
    let countdown = 3;
    countdownNumberEl.textContent = countdown;

    const countdownInterval = setInterval(() => {
        countdown--;
        if (countdown > 0) {
            countdownNumberEl.textContent = countdown;
        } else {
            clearInterval(countdownInterval);
            switchScreen("game");
            updateScoreDisplay();
            loadQuestion();
        }
    }, 1000);
}

function restartGame() {
    switchScreen("welcome");
    if (nameInput) nameInput.value = "";
    playLobbyMusic();
}

function loadQuestion() {
    clearAnswerStyles();

    if (currentQuestionIndex >= triviaQuestions.length) {
        submitGameResults();
        return;
    }

    const q = triviaQuestions[currentQuestionIndex];
    questionEl.textContent = q.question;
    questionNumberEl.textContent = currentQuestionIndex + 1;
    totalQuestionsEl.textContent = triviaQuestions.length;

    answersEl.innerHTML = "";
    const answers = q.options || q.answers; // Support both new (options) and legacy (answers) format
    answers.forEach((ans, i) => {
        const s = answerShapes[i % answerShapes.length];
        const btn = document.createElement("button");
        btn.className = `answer-btn ${s.color}`;
        btn.dataset.index = i;
        btn.innerHTML = `<div class="answer-shape">${s.shape}</div><span class="answer-text">${ans}</span>`;
        btn.addEventListener("click", selectAnswer);
        answersEl.appendChild(btn);
    });

    startTimer();
}

function startTimer() {
    questionStartTime = Date.now();
    const currentQuestion = triviaQuestions[currentQuestionIndex];
    const questionTimeLimit =
        currentQuestion.time_limit ||
        currentQuestion.timeLimit ||
        totalTimeForQuestion;

    timeLeft = questionTimeLimit;
    timerBar.style.transition = "none";
    timerBar.style.width = "100%";
    void timerBar.offsetWidth;
    timerBar.style.transition = `width ${questionTimeLimit}s linear`;
    timerBar.style.width = "0%";

    clearInterval(timer);
    timer = setInterval(() => {
        timeLeft--;
        if (timeLeft <= 0) {
            clearInterval(timer);
            handleAnswer(-1); // Timeout
        }
    }, 1000);
}

function selectAnswer(e) {
    triggerVibration();
    playSound("click");
    clearInterval(timer);
    handleAnswer(parseInt(e.currentTarget.dataset.index));
}

function handleAnswer(selectedIndex) {
    const q = triviaQuestions[currentQuestionIndex];
    const buttons = answersEl.querySelectorAll("button");
    buttons.forEach((b) => (b.disabled = true));

    const answerTime = Date.now();
    const questionTime = (answerTime - questionStartTime) / 1000;

    // Store answer and time
    gameAnswers[currentQuestionIndex] = selectedIndex;
    timeTaken[currentQuestionIndex] = questionTime;

    let points = 0;
    const correctAnswer =
        q.correct_answer !== undefined ? q.correct_answer : q.correct; // Support both formats
    if (selectedIndex === correctAnswer) {
        const questionTimeLimit =
            q.time_limit || q.timeLimit || totalTimeForQuestion;

        // Time-based scoring: Maximum 1000 points per question
        const maxPoints = 1000;

        if (questionTime < questionTimeLimit) {
            // Calculate points based on time remaining
            // Faster answers get higher scores
            const timeRatio = 1 - questionTime / questionTimeLimit;
            points = Math.floor(maxPoints * timeRatio);
            points = Math.max(100, points); // Minimum 100 points for correct answer
        } else {
            // If timeout, give minimum points
            points = 100;
        }

        score += points;

        scorePopupEl.textContent = `+${points}`;
        scorePopupEl.classList.add("show");
        playSound("correct");
        triggerVibration(200);
        updateScoreDisplay();
    } else {
        playSound("incorrect");
        triggerVibration([100, 50, 100]);
        if (selectedIndex !== -1) {
            buttons[selectedIndex].classList.add("incorrect-answer-selected");
        }
    }

    buttons.forEach((b, i) => {
        b.classList.add(
            i === correctAnswer ? "correct-answer" : "incorrect-answer"
        );
    });

    setTimeout(() => {
        currentQuestionIndex++;
        loadQuestion();
    }, 2500);
}

function updateScoreDisplay() {
    scoreEl.textContent = score;
}

function clearAnswerStyles() {
    answersEl.querySelectorAll("button").forEach((b) => {
        b.disabled = false;
        b.classList.remove(
            "correct-answer",
            "incorrect-answer",
            "incorrect-answer-selected"
        );
    });
    scorePopupEl.classList.remove("show");
}

async function submitGameResults() {
    // Check if this is demo mode
    if (window.gameConfig.demoMode) {
        handleDemoResults();
        return;
    }

    try {
        // Create form data for submission
        const formData = new FormData();

        // Add answers and time data as individual form fields
        triviaQuestions.forEach((question, index) => {
            if (gameAnswers[index] !== undefined) {
                formData.append(`answers[${question.id}]`, gameAnswers[index]);
            }
            if (timeTaken[index] !== undefined) {
                formData.append(`time_taken[${question.id}]`, timeTaken[index]);
            }
        });

        // Add CSRF token
        formData.append("_token", window.gameConfig.csrfToken);

        console.log("Submitting to URL:", window.gameConfig.submitUrl);
        console.log("Form data:", Array.from(formData.entries()));

        const response = await fetch(window.gameConfig.submitUrl, {
            method: "POST",
            body: formData,
        });

        if (response.ok) {
            // Check if it's a redirect response
            if (response.redirected) {
                window.location.href = response.url;
            } else {
                // Handle JSON response
                const result = await response.json();
                if (result.redirect_url) {
                    window.location.href = result.redirect_url;
                } else {
                    showEndScreen(result);
                }
            }
        } else {
            const result = await response.json();
            alert(
                "Error submitting game: " + (result.error || "Unknown error")
            );
            showEndScreen();
        }
    } catch (error) {
        console.error("Error submitting game:", error);
        alert("Error submitting game. Please try again.");
        showEndScreen();
    }
}

function handleDemoResults() {
    // Calculate demo results
    let correctAnswers = 0;
    triviaQuestions.forEach((question, index) => {
        if (gameAnswers[index] === question.correct_answer) {
            correctAnswers++;
        }
    });

    const percentage = Math.round(
        (correctAnswers / triviaQuestions.length) * 100
    );

    // Show demo end screen with results
    showDemoEndScreen({
        score: score,
        correctAnswers: correctAnswers,
        totalQuestions: triviaQuestions.length,
        percentage: percentage,
        playerName: playerNameEl.textContent,
    });
}

function showDemoEndScreen(results) {
    switchScreen("end");
    finalScoreEl.textContent = results.score;

    // Create demo results display
    const endScreen = screens.end;
    const existingResult = endScreen.querySelector(".demo-result");
    if (existingResult) existingResult.remove();

    const resultDiv = document.createElement("div");
    resultDiv.className =
        "demo-result mt-6 p-6 bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl border-2 border-pink-200";

    let performanceMessage = "";
    let performanceColor = "";
    if (results.percentage >= 80) {
        performanceMessage = "🌟 Excellent! You're a wedding expert!";
        performanceColor = "text-green-600";
    } else if (results.percentage >= 60) {
        performanceMessage = "👏 Great job! You know your wedding traditions!";
        performanceColor = "text-blue-600";
    } else if (results.percentage >= 40) {
        performanceMessage = "😊 Not bad! Keep learning about weddings!";
        performanceColor = "text-yellow-600";
    } else {
        performanceMessage = "💕 Every expert started somewhere!";
        performanceColor = "text-pink-600";
    }

    resultDiv.innerHTML = `
        <div class="text-center">
            <div class="text-6xl mb-4">${results.percentage}%</div>
            <h3 class="text-2xl font-bold text-pink-800 mb-2">Demo Complete!</h3>
            <p class="${performanceColor} text-lg font-semibold mb-4">${performanceMessage}</p>
            <p class="text-gray-600 mb-4">
                You answered <strong>${
                    results.correctAnswers
                }</strong> out of <strong>${
        results.totalQuestions
    }</strong> questions correctly
            </p>

            <div class="bg-white p-4 rounded-lg mb-4">
                <h4 class="font-bold text-pink-800 mb-2">🏆 Demo Leaderboard</h4>
                <div class="text-sm space-y-1">
                    ${window.gameConfig.demoLeaderboard
                        .slice(0, 5)
                        .map(
                            (entry, index) => `
                        <div class="flex justify-between items-center ${
                            index < 3 ? "font-bold" : ""
                        }">
                            <span>${index + 1}. ${entry.name}</span>
                            <span>${entry.score} pts</span>
                        </div>
                    `
                        )
                        .join("")}
                </div>
            </div>

            <div class="space-y-3">
                <p class="text-sm text-gray-600">Ready to create your own wedding trivia?</p>
                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    <a href="/auth/login" class="inline-block px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all transform hover:scale-105 font-semibold">
                        🚀 Get Started
                    </a>
                    <button onclick="restartGame()" class="px-6 py-3 bg-white border-2 border-pink-300 text-pink-600 rounded-lg hover:bg-pink-50 transition-colors font-semibold">
                        🔄 Try Again
                    </button>
                    <a href="/" class="inline-block px-6 py-3 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors font-semibold">
                        🏠 Home
                    </a>
                </div>
            </div>
        </div>
    `;

    endScreen.appendChild(resultDiv);
    playSound("fanfare");

    // Add confetti effect for high scores
    if (results.percentage >= 80) {
        createConfettiEffect();
    }
}

function createConfettiEffect() {
    const colors = ["#ec4899", "#f8d7da", "#fdf2f8", "#be185d"];
    const confettiCount = 50;

    for (let i = 0; i < confettiCount; i++) {
        const confetti = document.createElement("div");
        confetti.style.position = "fixed";
        confetti.style.width = "10px";
        confetti.style.height = "10px";
        confetti.style.backgroundColor =
            colors[Math.floor(Math.random() * colors.length)];
        confetti.style.left = Math.random() * 100 + "vw";
        confetti.style.top = "-10px";
        confetti.style.zIndex = "9999";
        confetti.style.pointerEvents = "none";
        confetti.style.borderRadius = "50%";

        document.body.appendChild(confetti);

        const animation = confetti.animate(
            [
                { transform: "translateY(0) rotate(0deg)", opacity: 1 },
                {
                    transform: `translateY(100vh) rotate(${
                        Math.random() * 360
                    }deg)`,
                    opacity: 0,
                },
            ],
            {
                duration: Math.random() * 3000 + 2000,
                easing: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
            }
        );

        animation.onfinish = () => confetti.remove();
    }
}

function showEndScreen(result = null) {
    switchScreen("end");
    finalScoreEl.textContent = score;

    if (result) {
        // Add result information to end screen
        const endScreen = screens.end;
        const existingResult = endScreen.querySelector(".game-result");
        if (existingResult) existingResult.remove();

        const resultDiv = document.createElement("div");
        resultDiv.className = "game-result mt-4 p-4 bg-pink-50 rounded-lg";
        resultDiv.innerHTML = `
            <p class="text-lg font-bold text-pink-800">Your Rank: #${result.rank}</p>
            <p class="text-sm text-pink-600">${result.correct_answers}/${result.total_questions} correct answers</p>
            <a href="${result.leaderboard_url}" class="inline-block mt-3 px-6 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors">
                View Leaderboard
            </a>
        `;
        endScreen.appendChild(resultDiv);
    }

    playSound("fanfare");
}

// --- INITIALIZATION ---
function createFloralAnimation() {
    const container = document.querySelector(".background-container");
    if (!container) return;

    const flowersList = document.createElement("ul");
    flowersList.classList.add("flowers");
    container.appendChild(flowersList);

    const flowerCount = 20;
    const flowerSvgs = [
        `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12,16.2C11.3,16.2 10.6,16 10,15.7C9.4,15.4 9,14.9 8.7,14.4C8.4,13.8 8.2,13.1 8.2,12.5C8.2,11.8 8.4,11.2 8.7,10.6C9,10 9.4,9.6 10,9.3C10.6,9 11.3,8.8 12,8.8C12.7,8.8 13.4,9 14,9.3C14.6,9.6 15,10 15.3,10.6C15.6,11.2 15.8,11.8 15.8,12.5C15.8,13.1 15.6,13.8 15.3,14.4C15,14.9 14.6,15.4 14,15.7C13.4,16 12.7,16.2 12,16.2M12,2C11.3,2 10.6,2.2 10,2.5C9.4,2.8 8.9,3.2 8.4,3.7C8,4.2 7.6,4.8 7.3,5.5C7.1,6.1 6.9,6.8 6.9,7.5C6.9,8.2 7.1,8.9 7.3,9.5C7.6,10.2 8,10.8 8.4,11.3C8.9,11.8 9.4,12.2 10,12.5C10.6,12.8 11.3,13 12,13C12.7,13 13.4,12.8 14,12.5C14.6,12.2 15.1,11.8 15.6,11.3C16,10.8 16.4,10.2 16.7,9.5C16.9,8.9 17.1,8.2 17.1,7.5C17.1,6.8 16.9,6.1 16.7,5.5C16.4,4.8 16,4.2 15.6,3.7C15.1,3.2 14.6,2.8 14,2.5C13.4,2.2 12.7,2 12,2M19,12C19,12.7 18.8,13.4 18.5,14C18.2,14.6 17.8,15.1 17.3,15.6C16.8,16 16.2,16.4 15.5,16.7C14.9,16.9 14.2,17.1 13.5,17.1C12.8,17.1 12.1,16.9 11.5,16.7C10.8,16.4 10.2,16 9.7,15.6C9.2,15.1 8.8,14.6 8.5,14C8.2,13.4 8,12.7 8,12C8,11.3 8.2,10.6 8.5,10C8.8,9.4 9.2,8.9 9.7,8.4C10.2,8 10.8,7.6 11.5,7.3C12.1,7.1 12.8,6.9 13.5,6.9C14.2,6.9 14.9,7.1 15.5,7.3C16.2,7.6 16.8,8 17.3,8.4C17.8,8.9 18.2,9.4 18.5,10C18.8,10.6 19,11.3 19,12Z" /></svg>`,
    ];
    const colors = ["#fbcfe8", "#fce7f3", "#f3e8ff", "#fdf2f8"];

    for (let i = 0; i < flowerCount; i++) {
        const li = document.createElement("li");
        const size = Math.random() * 40 + 20;
        li.style.width = `${size}px`;
        li.style.height = `${size}px`;
        li.style.left = `${Math.random() * 100}%`;
        li.style.animationDuration = `${Math.random() * 15 + 10}s`;
        li.style.animationDelay = `${Math.random() * 10}s`;
        li.style.color = colors[Math.floor(Math.random() * colors.length)];
        li.innerHTML =
            flowerSvgs[Math.floor(Math.random() * flowerSvgs.length)];
        flowersList.appendChild(li);
    }
}

// --- EVENT LISTENERS ---
if (startBtn) {
    startBtn.addEventListener("click", () => {
        triggerVibration();
        startGame();
    });
}

if (restartBtn) {
    restartBtn.addEventListener("click", () => {
        triggerVibration();
        restartGame();
    });
}

// --- INITIALIZATION ---
setupSounds();
createFloralAnimation();
