<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\Guest;
use App\Models\GuestScore;
use App\Models\TriviaQuestion;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class MinigameSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $client;
    protected $game;
    protected $gameConfig;
    protected $guest;
    protected $adminUser;
    protected $clientUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->adminUser = User::factory()->create(['role' => 'admin']);
        $this->clientUser = User::factory()->create(['role' => 'client']);
        
        // Create test client
        $this->client = Client::factory()->create([
            'user_id' => $this->clientUser->id,
            'is_have_minigame' => true,
            'minigame_options' => ['trivia'],
        ]);
        
        // Create test game
        $this->game = Game::create([
            'minigame_name' => 'trivia',
            'view_path' => 'minigame.trivia.index',
            'display_name' => 'Wedding Trivia',
            'description' => 'Test trivia game',
            'is_active' => true,
        ]);
        
        // Create test game config
        $this->gameConfig = GameConfig::create([
            'client_id' => $this->client->id,
            'game_id' => $this->game->id,
            'game_title' => 'Test Wedding Trivia',
            'top_players' => 10,
            'is_starting' => false,
            'is_done' => false,
        ]);
        
        // Create test guest
        $this->guest = Guest::factory()->create([
            'client_id' => $this->client->id,
            'name' => 'Test Guest',
            'unique_url' => 'test-guest-123',
        ]);
        
        // Create test trivia questions
        for ($i = 1; $i <= 3; $i++) {
            TriviaQuestion::create([
                'game_config_id' => $this->gameConfig->id,
                'question' => "Test question {$i}?",
                'options' => ['Option A', 'Option B', 'Option C', 'Option D'],
                'correct_answer' => 0,
                'points' => 100,
                'time_limit' => 30,
                'order' => $i,
                'is_active' => true,
            ]);
        }
    }

    /** @test */
    public function admin_can_view_client_list()
    {
        $response = $this->actingAs($this->adminUser)
                         ->get('/admin/clients');
        
        $response->assertStatus(200);
        $response->assertSee($this->client->client_name);
    }

    /** @test */
    public function admin_can_manage_minigame_settings()
    {
        $response = $this->actingAs($this->adminUser)
                         ->get("/admin/clients/{$this->client->id}/minigame-settings");
        
        $response->assertStatus(200);
        $response->assertSee('Minigame Settings');
    }

    /** @test */
    public function client_can_access_dashboard()
    {
        $response = $this->actingAs($this->clientUser)
                         ->get('/client/dashboard');
        
        $response->assertStatus(200);
        $response->assertSee('Welcome');
    }

    /** @test */
    public function client_can_start_game()
    {
        $response = $this->actingAs($this->clientUser)
                         ->post("/client/games/{$this->gameConfig->id}/start");
        
        $response->assertRedirect();
        
        $this->gameConfig->refresh();
        $this->assertTrue($this->gameConfig->is_starting);
        $this->assertFalse($this->gameConfig->is_done);
    }

    /** @test */
    public function guest_can_access_active_game()
    {
        $this->gameConfig->update(['is_starting' => true]);
        
        $response = $this->get("/minigame/trivia?client_id={$this->client->id}&guest_url={$this->guest->unique_url}");
        
        $response->assertStatus(200);
        $response->assertSee($this->gameConfig->game_title);
        $response->assertSee($this->guest->name);
    }

    /** @test */
    public function guest_cannot_access_inactive_game()
    {
        $this->gameConfig->update(['is_starting' => false]);
        
        $response = $this->get("/minigame/trivia?client_id={$this->client->id}");
        
        $response->assertStatus(200);
        $response->assertSee('Game Not Active');
    }

    /** @test */
    public function guest_can_submit_trivia_answers()
    {
        $this->gameConfig->update(['is_starting' => true]);
        
        $response = $this->postJson('/minigame/trivia/submit', [
            'client_id' => $this->client->id,
            'game_config_id' => $this->gameConfig->id,
            'guest_id' => $this->guest->id,
            'answers' => [0, 1, 0], // Answers for 3 questions
            'time_taken' => [15, 20, 25], // Time taken for each question
        ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        // Check if score was recorded
        $this->assertDatabaseHas('guest_scores', [
            'client_id' => $this->client->id,
            'guest_id' => $this->guest->id,
            'game_config_id' => $this->gameConfig->id,
            'is_completed' => true,
        ]);
    }

    /** @test */
    public function guest_cannot_replay_completed_game()
    {
        // Create a completed score for the guest
        GuestScore::create([
            'client_id' => $this->client->id,
            'guest_id' => $this->guest->id,
            'game_config_id' => $this->gameConfig->id,
            'score' => 250,
            'rank' => 1,
            'is_completed' => true,
            'completed_at' => now(),
        ]);
        
        $this->gameConfig->update(['is_starting' => true]);
        
        $response = $this->get("/minigame/trivia?client_id={$this->client->id}&guest_url={$this->guest->unique_url}");
        
        $response->assertRedirect();
        $response->assertRedirectContains('leaderboard');
    }

    /** @test */
    public function leaderboard_displays_correctly()
    {
        // Create some test scores
        for ($i = 1; $i <= 3; $i++) {
            $guest = Guest::factory()->create([
                'client_id' => $this->client->id,
                'name' => "Test Player {$i}",
            ]);
            
            GuestScore::create([
                'client_id' => $this->client->id,
                'guest_id' => $guest->id,
                'game_config_id' => $this->gameConfig->id,
                'score' => 300 - ($i * 50), // Decreasing scores
                'rank' => $i,
                'is_completed' => true,
                'completed_at' => now(),
            ]);
        }
        
        $response = $this->get("/minigame/leaderboard?client_id={$this->client->id}&game_config_id={$this->gameConfig->id}");
        
        $response->assertStatus(200);
        $response->assertSee('Test Player 1'); // Top player
        $response->assertSee('Leaderboard');
    }

    /** @test */
    public function middleware_blocks_access_without_client_id()
    {
        $response = $this->get('/minigame/trivia');
        
        $response->assertStatus(400);
    }

    /** @test */
    public function middleware_blocks_access_for_disabled_minigames()
    {
        $this->client->update(['is_have_minigame' => false]);
        
        $response = $this->get("/minigame/trivia?client_id={$this->client->id}");
        
        $response->assertStatus(403);
    }

    /** @test */
    public function scoring_system_calculates_correctly()
    {
        $this->gameConfig->update(['is_starting' => true]);
        
        // Submit answers with different time taken
        $response = $this->postJson('/minigame/trivia/submit', [
            'client_id' => $this->client->id,
            'game_config_id' => $this->gameConfig->id,
            'guest_id' => $this->guest->id,
            'answers' => [0, 0, 0], // All correct answers
            'time_taken' => [10, 20, 30], // Different response times
        ]);
        
        $response->assertStatus(200);
        
        $guestScore = GuestScore::where('guest_id', $this->guest->id)->first();
        
        // Score should be greater than 0 for correct answers
        $this->assertGreaterThan(0, $guestScore->score);
        $this->assertTrue($guestScore->is_completed);
        $this->assertEquals(1, $guestScore->rank); // Should be rank 1 as only player
    }
}
