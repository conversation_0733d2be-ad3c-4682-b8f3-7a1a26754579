<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\GuestScore;
use App\Models\TriviaQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check client role
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'client') {
                abort(403, 'Access Denied. Only client role is allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display the client dashboard
     */
    public function index()
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)
            ->with(['gameConfigs.game', 'gameConfigs.guestScores', 'guests'])
            ->first();

        if (!$client) {
            abort(404, 'Client profile not found.');
        }

        // Enhanced statistics
        $stats = [
            'total_guests' => $client->guests->count(),
            'total_games' => $client->gameConfigs->count(),
            'active_games' => $client->gameConfigs->where('is_starting', true)->count(),
            'completed_games' => $client->gameConfigs->where('is_done', true)->count(),
            'total_participants' => $client->gameConfigs->sum(function($config) {
                return $config->guestScores->where('is_completed', true)->count();
            }),
            'pending_games' => $client->gameConfigs->where('is_starting', false)->where('is_done', false)->count(),
        ];

        // Get recent activity (last 10 completed scores)
        $recentActivity = GuestScore::where('client_id', $client->id)
            ->where('is_completed', true)
            ->with(['guest', 'gameConfig.game'])
            ->orderBy('completed_at', 'desc')
            ->limit(10)
            ->get();

        // Get game configurations with leaderboard data
        $gameConfigs = $client->gameConfigs()
            ->with(['game', 'guestScores' => function($query) {
                $query->where('is_completed', true)->orderBy('score', 'desc')->limit(3);
            }])
            ->get();

        return view('client.dashboard.index', compact('client', 'stats', 'recentActivity', 'gameConfigs'));
    }

    /**
     * Display minigame management page
     */
    public function minigames()
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->with(['gameConfigs.game', 'gameConfigs.guestScores.guest'])->first();

        if (!$client || !$client->is_have_minigame) {
            abort(403, 'Minigames are not enabled for this client.');
        }

        return view('client.dashboard.minigames', compact('client'));
    }

    /**
     * Start a specific game
     */
    public function startGame(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        // Additional validation for trivia games
        if ($gameConfig->game->minigame_name === 'trivia') {
            $questionCount = TriviaQuestion::where('game_config_id', $gameConfig->id)
                ->where('is_active', true)
                ->count();

            if ($questionCount === 0) {
                return redirect()->back()
                    ->with('error', 'Cannot start trivia game: No active questions found. Please add questions first.');
            }
        }

        $gameConfig->update([
            'is_starting' => true,
            'is_done' => false,
        ]);

        return redirect()->back()
            ->with('success', 'Game started successfully! Guests can now play.');
    }

    /**
     * Stop a specific game
     */
    public function stopGame(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $gameConfig->update([
            'is_starting' => false,
            'is_done' => true,
        ]);

        // Update ranks for all completed scores
        $this->updateGameRanks($gameConfig);

        alert()->success('Success', 'Game stopped and completed successfully!');
        return redirect()->back();
    }

    /**
     * Restart a specific game (clear all scores)
     */
    public function restartGame(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        // Delete all existing scores for this game
        GuestScore::where('game_config_id', $gameConfig->id)->delete();

        $gameConfig->update([
            'is_starting' => false,
            'is_done' => false,
        ]);

        // Refresh the relationship to ensure UI shows correct state
        $gameConfig->load('guestScores');

        alert()->success('Success', 'Game restarted successfully! All scores have been cleared.');
        return redirect()->back();
    }

    /**
     * View leaderboard for a specific game
     */
    public function leaderboard(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this leaderboard.');
        }

        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        return view('client.dashboard.leaderboard', compact('gameConfig', 'leaderboard', 'client'));
    }

    /**
     * Show detailed game configuration view
     */
    public function gameConfig(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $gameConfig->load(['game', 'guestScores.guest']);

        // Get leaderboard
        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        // Get trivia questions if it's a trivia game
        $triviaQuestions = null;
        if ($gameConfig->game->minigame_name === 'trivia') {
            $triviaQuestions = TriviaQuestion::where('game_config_id', $gameConfig->id)
                ->orderBy('created_at')
                ->get();
        }

        // Get participation statistics
        $participationStats = [
            'total_participants' => $gameConfig->guestScores->count(),
            'completed_participants' => $gameConfig->guestScores->where('is_completed', true)->count(),
            'in_progress' => $gameConfig->guestScores->where('is_completed', false)->count(),
            'average_score' => round($gameConfig->guestScores->where('is_completed', true)->avg('score') ?? 0, 1),
            'highest_score' => $gameConfig->guestScores->where('is_completed', true)->max('score') ?? 0,
        ];

        return view('client.dashboard.game-config', compact(
            'gameConfig',
            'leaderboard',
            'triviaQuestions',
            'participationStats',
            'client'
        ));
    }

    /**
     * Get live leaderboard data (AJAX)
     */
    public function liveLeaderboard(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access.');
        }

        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        return response()->json([
            'leaderboard' => $leaderboard->map(function ($score) {
                return [
                    'id' => $score->id,
                    'guest_name' => $score->guest->name,
                    'score' => $score->score,
                    'rank' => $score->rank,
                    'completed_at' => $score->completed_at->format('M d, Y H:i'),
                    'is_winner' => $score->rank <= 3,
                ];
            }),
            'total_participants' => $gameConfig->guestScores()->where('is_completed', true)->count(),
            'game_status' => [
                'is_starting' => $gameConfig->is_starting,
                'is_done' => $gameConfig->is_done,
            ]
        ]);
    }



    /**
     * Show question management interface
     */
    public function questions(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        // Only allow question management for trivia games
        if ($gameConfig->game->minigame_name !== 'trivia') {
            abort(404, 'Question management is only available for trivia games.');
        }

        $questions = TriviaQuestion::where('game_config_id', $gameConfig->id)
            ->orderBy('order')
            ->orderBy('created_at')
            ->get();

        return view('client.dashboard.questions', compact('client', 'gameConfig', 'questions'));
    }

    /**
     * Store a new question
     */
    public function storeQuestion(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        // Check question limit
        $currentQuestionCount = TriviaQuestion::where('game_config_id', $gameConfig->id)->count();
        if ($currentQuestionCount >= 10) {
            return redirect()->route('client.dashboard.questions', $gameConfig)
                ->with('error', 'Maximum of 10 questions allowed per game.');
        }

        $request->validate([
            'question' => 'required|string|max:1000',
            'options' => 'required|array|min:2|max:4',
            'options.*' => 'required|string|max:255',
            'correct_answer' => 'required|integer|min:0|max:3',
        ]);

        // Filter out empty options
        $options = array_filter($request->options, function($option) {
            return !empty(trim($option));
        });

        // Ensure correct answer index is valid for the provided options
        if ($request->correct_answer >= count($options)) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Correct answer selection is invalid for the provided options.');
        }

        TriviaQuestion::create([
            'game_config_id' => $gameConfig->id,
            'question' => $request->question,
            'options' => array_values($options), // Re-index array
            'correct_answer' => $request->correct_answer,
            'points' => 100, // Default points handled by game engine
            'time_limit' => 30, // Default time limit handled by game engine
            'order' => $currentQuestionCount + 1,
            'is_active' => true,
        ]);

        return redirect()->route('client.dashboard.questions', $gameConfig)
            ->with('success', 'Question added successfully!');
    }

    /**
     * Update a question
     */
    public function updateQuestion(Request $request, TriviaQuestion $question)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $question->gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this question.');
        }

        $request->validate([
            'question' => 'required|string|max:1000',
            'options' => 'required|array|min:2|max:4',
            'options.*' => 'required|string|max:255',
            'correct_answer' => 'required|integer|min:0|max:3',
            'is_active' => 'boolean',
        ]);

        // Filter out empty options
        $options = array_filter($request->options, function($option) {
            return !empty(trim($option));
        });

        // Ensure correct answer index is valid for the provided options
        if ($request->correct_answer >= count($options)) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Correct answer selection is invalid for the provided options.');
        }

        $question->update([
            'question' => $request->question,
            'options' => array_values($options), // Re-index array
            'correct_answer' => $request->correct_answer,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('client.dashboard.questions', $question->gameConfig)
            ->with('success', 'Question updated successfully!');
    }

    /**
     * Delete a question
     */
    public function deleteQuestion(TriviaQuestion $question)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $question->gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this question.');
        }

        $gameConfig = $question->gameConfig;
        $question->delete();

        return redirect()->route('client.dashboard.questions', $gameConfig)
            ->with('success', 'Question deleted successfully!');
    }

    /**
     * Bulk import questions
     */
    public function bulkImportQuestions(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $request->validate([
            'questions_json' => 'required|string',
        ]);

        try {
            $questionsData = json_decode($request->questions_json, true);

            if (!is_array($questionsData)) {
                throw new \Exception('Invalid JSON format');
            }

            $imported = 0;
            $maxOrder = TriviaQuestion::where('game_config_id', $gameConfig->id)->max('order') ?? 0;

            foreach ($questionsData as $questionData) {
                if (!isset($questionData['question']) || !isset($questionData['options']) || !isset($questionData['correct_answer'])) {
                    continue;
                }

                TriviaQuestion::create([
                    'game_config_id' => $gameConfig->id,
                    'question' => $questionData['question'],
                    'options' => $questionData['options'],
                    'correct_answer' => $questionData['correct_answer'],
                    'points' => $questionData['points'] ?? 100,
                    'time_limit' => $questionData['time_limit'] ?? 30,
                    'order' => ++$maxOrder,
                    'is_active' => $questionData['is_active'] ?? true,
                ]);

                $imported++;
            }

            return redirect()->route('client.dashboard.questions', $gameConfig)
                ->with('success', "Successfully imported {$imported} questions!");

        } catch (\Exception $e) {
            return redirect()->route('client.dashboard.questions', $gameConfig)
                ->with('error', 'Error importing questions: ' . $e->getMessage());
        }
    }

    /**
     * Excel import questions
     */
    public function excelImportQuestions(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $request->validate([
            'excel_file' => 'required|file|mimes:xlsx,xls|max:2048',
        ]);

        try {
            $file = $request->file('excel_file');
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
            $spreadsheet = $reader->load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();

            $questions = [];
            $row = 2; // Start from row 2 (skip header)
            $maxRows = 12; // Header + 10 questions max

            while ($row <= $maxRows) {
                $question = trim($worksheet->getCell('A' . $row)->getValue());

                if (empty($question)) {
                    break; // Stop if no more questions
                }

                $optionA = trim($worksheet->getCell('B' . $row)->getValue());
                $optionB = trim($worksheet->getCell('C' . $row)->getValue());
                $optionC = trim($worksheet->getCell('D' . $row)->getValue());
                $optionD = trim($worksheet->getCell('E' . $row)->getValue());
                $correctAnswer = strtoupper(trim($worksheet->getCell('F' . $row)->getValue()));

                // Validate required fields
                if (empty($optionA) || empty($optionB) || !in_array($correctAnswer, ['A', 'B', 'C', 'D'])) {
                    return redirect()->route('client.dashboard.questions', $gameConfig)
                        ->with('error', "Invalid data in row {$row}. Please check the format.");
                }

                // Build options array
                $options = [$optionA, $optionB];
                if (!empty($optionC)) $options[] = $optionC;
                if (!empty($optionD)) $options[] = $optionD;

                // Convert correct answer letter to index
                $correctAnswerIndex = ord($correctAnswer) - ord('A');

                // Validate correct answer index
                if ($correctAnswerIndex >= count($options)) {
                    return redirect()->route('client.dashboard.questions', $gameConfig)
                        ->with('error', "Correct answer '{$correctAnswer}' is invalid for question in row {$row}.");
                }

                $questions[] = [
                    'question' => $question,
                    'options' => $options,
                    'correct_answer' => $correctAnswerIndex,
                ];

                $row++;
            }

            if (empty($questions)) {
                return redirect()->route('client.dashboard.questions', $gameConfig)
                    ->with('error', 'No valid questions found in the Excel file.');
            }

            if (count($questions) > 10) {
                return redirect()->route('client.dashboard.questions', $gameConfig)
                    ->with('error', 'Maximum 10 questions allowed. Your file contains ' . count($questions) . ' questions.');
            }

            // Delete existing questions and import new ones
            TriviaQuestion::where('game_config_id', $gameConfig->id)->delete();

            foreach ($questions as $index => $questionData) {
                TriviaQuestion::create([
                    'game_config_id' => $gameConfig->id,
                    'question' => $questionData['question'],
                    'options' => $questionData['options'],
                    'correct_answer' => $questionData['correct_answer'],
                    'points' => 100,
                    'time_limit' => 30,
                    'order' => $index + 1,
                    'is_active' => true,
                ]);
            }

            return redirect()->route('client.dashboard.questions', $gameConfig)
                ->with('success', "Successfully imported " . count($questions) . " questions from Excel file!");

        } catch (\Exception $e) {
            return redirect()->route('client.dashboard.questions', $gameConfig)
                ->with('error', 'Error importing Excel file: ' . $e->getMessage());
        }
    }

    /**
     * Download Excel template
     */
    public function downloadTemplate(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        try {
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $worksheet = $spreadsheet->getActiveSheet();

            // Set headers
            $worksheet->setCellValue('A1', 'Question');
            $worksheet->setCellValue('B1', 'Option A');
            $worksheet->setCellValue('C1', 'Option B');
            $worksheet->setCellValue('D1', 'Option C');
            $worksheet->setCellValue('E1', 'Option D');
            $worksheet->setCellValue('F1', 'Correct Answer');

            // Add sample data
            $worksheet->setCellValue('A2', 'What is the capital of France?');
            $worksheet->setCellValue('B2', 'London');
            $worksheet->setCellValue('C2', 'Berlin');
            $worksheet->setCellValue('D2', 'Paris');
            $worksheet->setCellValue('E2', 'Madrid');
            $worksheet->setCellValue('F2', 'C');

            $worksheet->setCellValue('A3', 'Which planet is closest to the Sun?');
            $worksheet->setCellValue('B3', 'Venus');
            $worksheet->setCellValue('C3', 'Mercury');
            $worksheet->setCellValue('D3', 'Earth');
            $worksheet->setCellValue('E3', 'Mars');
            $worksheet->setCellValue('F3', 'B');

            // Style headers
            $headerStyle = [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'f8d7da']
                ]
            ];
            $worksheet->getStyle('A1:F1')->applyFromArray($headerStyle);

            // Auto-size columns
            foreach (range('A', 'F') as $column) {
                $worksheet->getColumnDimension($column)->setAutoSize(true);
            }

            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

            $filename = 'trivia_questions_template.xlsx';
            $tempFile = tempnam(sys_get_temp_dir(), $filename);
            $writer->save($tempFile);

            return response()->download($tempFile, $filename)->deleteFileAfterSend(true);

        } catch (\Exception $e) {
            return redirect()->route('client.dashboard.questions', $gameConfig)
                ->with('error', 'Error generating template: ' . $e->getMessage());
        }
    }

    /**
     * Update game configuration settings
     */
    public function updateGameConfig(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $request->validate([
            'game_title' => 'required|string|max:255',
            'top_players' => 'required|integer|min:1|max:100',
            'config_data' => 'nullable|array',
        ]);

        $gameConfig->update([
            'game_title' => $request->game_title,
            'top_players' => $request->top_players,
            'config_data' => $request->config_data ?? [],
        ]);

        return redirect()->route('client.dashboard.game-config', $gameConfig)
            ->with('success', 'Game configuration updated successfully!');
    }

    /**
     * Update ranks for all completed scores in a game
     */
    private function updateGameRanks(GameConfig $gameConfig)
    {
        $scores = GuestScore::where('game_config_id', $gameConfig->id)
                           ->where('is_completed', true)
                           ->orderBy('score', 'desc')
                           ->orderBy('completed_at', 'asc')
                           ->get();

        foreach ($scores as $index => $score) {
            $score->update(['rank' => $index + 1]);
        }
    }
}
