<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\GuestScore;
use App\Models\TriviaQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check client role
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'client') {
                abort(403, 'Access Denied. Only client role is allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display the client dashboard
     */
    public function index()
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)
            ->with(['gameConfigs.game', 'gameConfigs.guestScores', 'guests'])
            ->first();

        if (!$client) {
            abort(404, 'Client profile not found.');
        }

        // Enhanced statistics
        $stats = [
            'total_guests' => $client->guests->count(),
            'total_games' => $client->gameConfigs->count(),
            'active_games' => $client->gameConfigs->where('is_starting', true)->count(),
            'completed_games' => $client->gameConfigs->where('is_done', true)->count(),
            'total_participants' => $client->gameConfigs->sum(function($config) {
                return $config->guestScores->where('is_completed', true)->count();
            }),
            'pending_games' => $client->gameConfigs->where('is_starting', false)->where('is_done', false)->count(),
        ];

        // Get recent activity (last 10 completed scores)
        $recentActivity = GuestScore::where('client_id', $client->id)
            ->where('is_completed', true)
            ->with(['guest', 'gameConfig.game'])
            ->orderBy('completed_at', 'desc')
            ->limit(10)
            ->get();

        // Get game configurations with leaderboard data
        $gameConfigs = $client->gameConfigs()
            ->with(['game', 'guestScores' => function($query) {
                $query->where('is_completed', true)->orderBy('score', 'desc')->limit(3);
            }])
            ->get();

        return view('client.dashboard.index', compact('client', 'stats', 'recentActivity', 'gameConfigs'));
    }

    /**
     * Display minigame management page
     */
    public function minigames()
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->with(['gameConfigs.game', 'gameConfigs.guestScores.guest'])->first();

        if (!$client || !$client->is_have_minigame) {
            abort(403, 'Minigames are not enabled for this client.');
        }

        return view('client.dashboard.minigames', compact('client'));
    }

    /**
     * Start a specific game
     */
    public function startGame(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $gameConfig->update([
            'is_starting' => true,
            'is_done' => false,
        ]);

        alert()->success('Success', 'Game started successfully! Guests can now play.');
        return redirect()->back();
    }

    /**
     * Stop a specific game
     */
    public function stopGame(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $gameConfig->update([
            'is_starting' => false,
            'is_done' => true,
        ]);

        // Update ranks for all completed scores
        $this->updateGameRanks($gameConfig);

        alert()->success('Success', 'Game stopped and completed successfully!');
        return redirect()->back();
    }

    /**
     * Restart a specific game (clear all scores)
     */
    public function restartGame(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        // Delete all existing scores for this game
        GuestScore::where('game_config_id', $gameConfig->id)->delete();

        $gameConfig->update([
            'is_starting' => false,
            'is_done' => false,
        ]);

        alert()->success('Success', 'Game restarted successfully! All scores have been cleared.');
        return redirect()->back();
    }

    /**
     * View leaderboard for a specific game
     */
    public function leaderboard(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this leaderboard.');
        }

        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        return view('client.dashboard.leaderboard', compact('gameConfig', 'leaderboard', 'client'));
    }

    /**
     * Show detailed game configuration view
     */
    public function gameConfig(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $gameConfig->load(['game', 'guestScores.guest']);

        // Get leaderboard
        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        // Get trivia questions if it's a trivia game
        $triviaQuestions = null;
        if ($gameConfig->game->minigame_name === 'trivia') {
            $triviaQuestions = TriviaQuestion::where('game_config_id', $gameConfig->id)
                ->orderBy('created_at')
                ->get();
        }

        // Get participation statistics
        $participationStats = [
            'total_participants' => $gameConfig->guestScores->count(),
            'completed_participants' => $gameConfig->guestScores->where('is_completed', true)->count(),
            'in_progress' => $gameConfig->guestScores->where('is_completed', false)->count(),
            'average_score' => round($gameConfig->guestScores->where('is_completed', true)->avg('score') ?? 0, 1),
            'highest_score' => $gameConfig->guestScores->where('is_completed', true)->max('score') ?? 0,
        ];

        return view('client.dashboard.game-config', compact(
            'gameConfig',
            'leaderboard',
            'triviaQuestions',
            'participationStats',
            'client'
        ));
    }

    /**
     * Get live leaderboard data (AJAX)
     */
    public function liveLeaderboard(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access.');
        }

        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        return response()->json([
            'leaderboard' => $leaderboard->map(function ($score) {
                return [
                    'id' => $score->id,
                    'guest_name' => $score->guest->name,
                    'score' => $score->score,
                    'rank' => $score->rank,
                    'completed_at' => $score->completed_at->format('M d, Y H:i'),
                    'is_winner' => $score->rank <= 3,
                ];
            }),
            'total_participants' => $gameConfig->guestScores()->where('is_completed', true)->count(),
            'game_status' => [
                'is_starting' => $gameConfig->is_starting,
                'is_done' => $gameConfig->is_done,
            ]
        ]);
    }



    /**
     * Update ranks for all completed scores in a game
     */
    private function updateGameRanks(GameConfig $gameConfig)
    {
        $scores = GuestScore::where('game_config_id', $gameConfig->id)
                           ->where('is_completed', true)
                           ->orderBy('score', 'desc')
                           ->orderBy('completed_at', 'asc')
                           ->get();

        foreach ($scores as $index => $score) {
            $score->update(['rank' => $index + 1]);
        }
    }
}
