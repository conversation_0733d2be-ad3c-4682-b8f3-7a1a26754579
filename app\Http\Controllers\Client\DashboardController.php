<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\GuestScore;
use App\Models\TriviaQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check client role
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'client') {
                abort(403, 'Access Denied. Only client role is allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display the client dashboard
     */
    public function index()
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)
            ->with(['gameConfigs.game', 'gameConfigs.guestScores', 'guests'])
            ->first();

        if (!$client) {
            abort(404, 'Client profile not found.');
        }

        // Enhanced statistics
        $stats = [
            'total_guests' => $client->guests->count(),
            'total_games' => $client->gameConfigs->count(),
            'active_games' => $client->gameConfigs->where('is_starting', true)->count(),
            'completed_games' => $client->gameConfigs->where('is_done', true)->count(),
            'total_participants' => $client->gameConfigs->sum(function($config) {
                return $config->guestScores->where('is_completed', true)->count();
            }),
            'pending_games' => $client->gameConfigs->where('is_starting', false)->where('is_done', false)->count(),
        ];

        // Get recent activity (last 10 completed scores)
        $recentActivity = GuestScore::where('client_id', $client->id)
            ->where('is_completed', true)
            ->with(['guest', 'gameConfig.game'])
            ->orderBy('completed_at', 'desc')
            ->limit(10)
            ->get();

        // Get game configurations with leaderboard data
        $gameConfigs = $client->gameConfigs()
            ->with(['game', 'guestScores' => function($query) {
                $query->where('is_completed', true)->orderBy('score', 'desc')->limit(3);
            }])
            ->get();

        return view('client.dashboard.index', compact('client', 'stats', 'recentActivity', 'gameConfigs'));
    }

    /**
     * Display minigame management page
     */
    public function minigames()
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->with(['gameConfigs.game', 'gameConfigs.guestScores.guest'])->first();

        if (!$client || !$client->is_have_minigame) {
            abort(403, 'Minigames are not enabled for this client.');
        }

        return view('client.dashboard.minigames', compact('client'));
    }

    /**
     * Start a specific game
     */
    public function startGame(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        // Additional validation for trivia games
        if ($gameConfig->game->minigame_name === 'trivia') {
            $questionCount = TriviaQuestion::where('game_config_id', $gameConfig->id)
                ->where('is_active', true)
                ->count();

            if ($questionCount === 0) {
                return redirect()->back()
                    ->with('error', 'Cannot start trivia game: No active questions found. Please add questions first.');
            }
        }

        $gameConfig->update([
            'is_starting' => true,
            'is_done' => false,
        ]);

        return redirect()->back()
            ->with('success', 'Game started successfully! Guests can now play.');
    }

    /**
     * Stop a specific game
     */
    public function stopGame(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $gameConfig->update([
            'is_starting' => false,
            'is_done' => true,
        ]);

        // Update ranks for all completed scores
        $this->updateGameRanks($gameConfig);

        alert()->success('Success', 'Game stopped and completed successfully!');
        return redirect()->back();
    }

    /**
     * Restart a specific game (clear all scores)
     */
    public function restartGame(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        // Delete all existing scores for this game
        GuestScore::where('game_config_id', $gameConfig->id)->delete();

        $gameConfig->update([
            'is_starting' => false,
            'is_done' => false,
        ]);

        alert()->success('Success', 'Game restarted successfully! All scores have been cleared.');
        return redirect()->back();
    }

    /**
     * View leaderboard for a specific game
     */
    public function leaderboard(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this leaderboard.');
        }

        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        return view('client.dashboard.leaderboard', compact('gameConfig', 'leaderboard', 'client'));
    }

    /**
     * Show detailed game configuration view
     */
    public function gameConfig(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $gameConfig->load(['game', 'guestScores.guest']);

        // Get leaderboard
        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        // Get trivia questions if it's a trivia game
        $triviaQuestions = null;
        if ($gameConfig->game->minigame_name === 'trivia') {
            $triviaQuestions = TriviaQuestion::where('game_config_id', $gameConfig->id)
                ->orderBy('created_at')
                ->get();
        }

        // Get participation statistics
        $participationStats = [
            'total_participants' => $gameConfig->guestScores->count(),
            'completed_participants' => $gameConfig->guestScores->where('is_completed', true)->count(),
            'in_progress' => $gameConfig->guestScores->where('is_completed', false)->count(),
            'average_score' => round($gameConfig->guestScores->where('is_completed', true)->avg('score') ?? 0, 1),
            'highest_score' => $gameConfig->guestScores->where('is_completed', true)->max('score') ?? 0,
        ];

        return view('client.dashboard.game-config', compact(
            'gameConfig',
            'leaderboard',
            'triviaQuestions',
            'participationStats',
            'client'
        ));
    }

    /**
     * Get live leaderboard data (AJAX)
     */
    public function liveLeaderboard(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access.');
        }

        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        return response()->json([
            'leaderboard' => $leaderboard->map(function ($score) {
                return [
                    'id' => $score->id,
                    'guest_name' => $score->guest->name,
                    'score' => $score->score,
                    'rank' => $score->rank,
                    'completed_at' => $score->completed_at->format('M d, Y H:i'),
                    'is_winner' => $score->rank <= 3,
                ];
            }),
            'total_participants' => $gameConfig->guestScores()->where('is_completed', true)->count(),
            'game_status' => [
                'is_starting' => $gameConfig->is_starting,
                'is_done' => $gameConfig->is_done,
            ]
        ]);
    }



    /**
     * Show question management interface
     */
    public function questions(GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        // Only allow question management for trivia games
        if ($gameConfig->game->minigame_name !== 'trivia') {
            abort(404, 'Question management is only available for trivia games.');
        }

        $questions = TriviaQuestion::where('game_config_id', $gameConfig->id)
            ->orderBy('order')
            ->orderBy('created_at')
            ->get();

        return view('client.dashboard.questions', compact('client', 'gameConfig', 'questions'));
    }

    /**
     * Store a new question
     */
    public function storeQuestion(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $request->validate([
            'question' => 'required|string|max:1000',
            'options' => 'required|array|min:2|max:4',
            'options.*' => 'required|string|max:255',
            'correct_answer' => 'required|integer|min:0|max:3',
            'points' => 'required|integer|min:1|max:1000',
            'time_limit' => 'required|integer|min:5|max:300',
            'order' => 'nullable|integer|min:0',
        ]);

        // Get the next order if not provided
        $order = $request->order;
        if ($order === null) {
            $order = TriviaQuestion::where('game_config_id', $gameConfig->id)->max('order') + 1;
        }

        TriviaQuestion::create([
            'game_config_id' => $gameConfig->id,
            'question' => $request->question,
            'options' => $request->options,
            'correct_answer' => $request->correct_answer,
            'points' => $request->points,
            'time_limit' => $request->time_limit,
            'order' => $order,
            'is_active' => true,
        ]);

        return redirect()->route('client.dashboard.questions', $gameConfig)
            ->with('success', 'Question added successfully!');
    }

    /**
     * Update a question
     */
    public function updateQuestion(Request $request, TriviaQuestion $question)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $question->gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this question.');
        }

        $request->validate([
            'question' => 'required|string|max:1000',
            'options' => 'required|array|min:2|max:4',
            'options.*' => 'required|string|max:255',
            'correct_answer' => 'required|integer|min:0|max:3',
            'points' => 'required|integer|min:1|max:1000',
            'time_limit' => 'required|integer|min:5|max:300',
            'order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $question->update([
            'question' => $request->question,
            'options' => $request->options,
            'correct_answer' => $request->correct_answer,
            'points' => $request->points,
            'time_limit' => $request->time_limit,
            'order' => $request->order ?? $question->order,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('client.dashboard.questions', $question->gameConfig)
            ->with('success', 'Question updated successfully!');
    }

    /**
     * Delete a question
     */
    public function deleteQuestion(TriviaQuestion $question)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $question->gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this question.');
        }

        $gameConfig = $question->gameConfig;
        $question->delete();

        return redirect()->route('client.dashboard.questions', $gameConfig)
            ->with('success', 'Question deleted successfully!');
    }

    /**
     * Bulk import questions
     */
    public function bulkImportQuestions(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $request->validate([
            'questions_json' => 'required|string',
        ]);

        try {
            $questionsData = json_decode($request->questions_json, true);

            if (!is_array($questionsData)) {
                throw new \Exception('Invalid JSON format');
            }

            $imported = 0;
            $maxOrder = TriviaQuestion::where('game_config_id', $gameConfig->id)->max('order') ?? 0;

            foreach ($questionsData as $questionData) {
                if (!isset($questionData['question']) || !isset($questionData['options']) || !isset($questionData['correct_answer'])) {
                    continue;
                }

                TriviaQuestion::create([
                    'game_config_id' => $gameConfig->id,
                    'question' => $questionData['question'],
                    'options' => $questionData['options'],
                    'correct_answer' => $questionData['correct_answer'],
                    'points' => $questionData['points'] ?? 100,
                    'time_limit' => $questionData['time_limit'] ?? 30,
                    'order' => ++$maxOrder,
                    'is_active' => $questionData['is_active'] ?? true,
                ]);

                $imported++;
            }

            return redirect()->route('client.dashboard.questions', $gameConfig)
                ->with('success', "Successfully imported {$imported} questions!");

        } catch (\Exception $e) {
            return redirect()->route('client.dashboard.questions', $gameConfig)
                ->with('error', 'Error importing questions: ' . $e->getMessage());
        }
    }

    /**
     * Update game configuration settings
     */
    public function updateGameConfig(Request $request, GameConfig $gameConfig)
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client || $gameConfig->client_id !== $client->id) {
            abort(403, 'Unauthorized access to this game.');
        }

        $request->validate([
            'game_title' => 'required|string|max:255',
            'top_players' => 'required|integer|min:1|max:100',
            'config_data' => 'nullable|array',
        ]);

        $gameConfig->update([
            'game_title' => $request->game_title,
            'top_players' => $request->top_players,
            'config_data' => $request->config_data ?? [],
        ]);

        return redirect()->route('client.dashboard.game-config', $gameConfig)
            ->with('success', 'Game configuration updated successfully!');
    }

    /**
     * Update ranks for all completed scores in a game
     */
    private function updateGameRanks(GameConfig $gameConfig)
    {
        $scores = GuestScore::where('game_config_id', $gameConfig->id)
                           ->where('is_completed', true)
                           ->orderBy('score', 'desc')
                           ->orderBy('completed_at', 'asc')
                           ->get();

        foreach ($scores as $index => $score) {
            $score->update(['rank' => $index + 1]);
        }
    }
}
