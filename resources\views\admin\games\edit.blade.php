@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-edit me-2"></i>Edit Game: {{ $game->display_name }}
                </h5>
                <small class="text-muted">Update game configuration and settings</small>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.games.update', $game) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="minigame_name" class="form-label">Game Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('minigame_name') is-invalid @enderror" 
                                   id="minigame_name" name="minigame_name" value="{{ old('minigame_name', $game->minigame_name) }}" 
                                   placeholder="e.g., trivia, memory_game" required>
                            <small class="form-text text-muted">Unique identifier for the game (lowercase, underscores allowed)</small>
                            @error('minigame_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="display_name" class="form-label">Display Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('display_name') is-invalid @enderror" 
                                   id="display_name" name="display_name" value="{{ old('display_name', $game->display_name) }}" 
                                   placeholder="e.g., Wedding Trivia, Memory Challenge" required>
                            <small class="form-text text-muted">Human-readable name shown to users</small>
                            @error('display_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="view_path" class="form-label">View Path <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('view_path') is-invalid @enderror" 
                                   id="view_path" name="view_path" value="{{ old('view_path', $game->view_path) }}" 
                                   placeholder="e.g., minigame.trivia.index" required>
                            <small class="form-text text-muted">Blade view path for the game interface</small>
                            @error('view_path')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="is_active" class="form-label">Status</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       value="1" {{ old('is_active', $game->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active (available for use)
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" 
                                  placeholder="Describe the game mechanics, rules, or special features...">{{ old('description', $game->description) }}</textarea>
                        <small class="form-text text-muted">Optional description for admin reference</small>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Game Statistics -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0 heading-font">
                                <i class="fas fa-chart-bar me-2"></i>Game Statistics
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="heading-font text-primary">{{ $game->gameConfigs->count() }}</h4>
                                        <small class="text-muted">Total Configurations</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="heading-font text-success">{{ $game->gameConfigs->where('is_starting', true)->count() }}</h4>
                                        <small class="text-muted">Active Configs</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="heading-font text-info">{{ $game->gameConfigs->sum(function($config) { return $config->guestScores->count(); }) }}</h4>
                                        <small class="text-muted">Total Plays</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="heading-font text-warning">{{ $game->created_at->format('M Y') }}</h4>
                                        <small class="text-muted">Created</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.games.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Games
                        </a>
                        <div>
                            <a href="{{ route('admin.games.show', $game) }}" class="btn btn-info me-2">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Game
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
/* Form specific styles */
.form-check-input:checked {
    background-color: var(--primary-pink) !important;
    border-color: var(--primary-pink) !important;
}

.form-check-input:focus {
    border-color: var(--primary-pink) !important;
    box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25) !important;
}

.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    display: block;
}
</style>
@endsection
