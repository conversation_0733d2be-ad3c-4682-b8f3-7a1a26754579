@extends('client.layout.master')

@section('title', $gameConfig->game_title . ' - Leaderboard')
@section('page-title', $gameConfig->game_title . ' - Leaderboard')
@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('client.dashboard.minigames') }}">Minigames</a></li>
<li class="breadcrumb-item"><a href="{{ route('client.dashboard.game-config', $gameConfig) }}">{{
        $gameConfig->game_title }}</a></li>
<li class="breadcrumb-item active">Leaderboard</li>
@endsection

@section('content')
<div class="row">
    <!-- Header -->
    <div class="col-12 mb-4">
        <div class="card"
            style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border: none; border-radius: 15px;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 style="color: #721c24; font-weight: 700; margin-bottom: 5px;">
                            <i class="fas fa-trophy me-2" style="color: #ffd700;"></i>
                            {{ $gameConfig->game_title }} - Leaderboard
                        </h2>
                        <p style="color: #721c24; margin-bottom: 0;">
                            Top {{ $gameConfig->top_players }} winners • {{ $leaderboard->count() }} participants
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('client.minigames') }}" class="btn btn-outline-dark"
                            style="border-radius: 20px;">
                            <i class="fas fa-arrow-left me-1"></i>Back to Games
                        </a>
                        <button onclick="refreshLeaderboard()" class="btn"
                            style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); color: white; border: none; border-radius: 20px;">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Status -->
    <div class="col-12 mb-4">
        <div class="card" style="border: 2px solid #f8d7da; border-radius: 15px;">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3 text-center">
                        <div class="p-3">
                            @if($gameConfig->is_starting)
                            <i class="fas fa-play-circle fa-3x text-success mb-2"></i>
                            <h5 class="text-success">Game Active</h5>
                            <small class="text-muted">Players can join now</small>
                            @elseif($gameConfig->is_done)
                            <i class="fas fa-check-circle fa-3x text-info mb-2"></i>
                            <h5 class="text-info">Game Completed</h5>
                            <small class="text-muted">Final results</small>
                            @else
                            <i class="fas fa-pause-circle fa-3x text-secondary mb-2"></i>
                            <h5 class="text-secondary">Game Inactive</h5>
                            <small class="text-muted">Waiting to start</small>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <h3 style="color: #d63384; font-weight: 700;">{{ $leaderboard->count() }}</h3>
                                <p class="text-muted mb-0">Total Players</p>
                            </div>
                            <div class="col-md-4 text-center">
                                <h3 style="color: #d63384; font-weight: 700;">{{ $leaderboard->max('score') ?? 0 }}</h3>
                                <p class="text-muted mb-0">Highest Score</p>
                            </div>
                            <div class="col-md-4 text-center">
                                <h3 style="color: #d63384; font-weight: 700;">{{ $leaderboard->avg('score') ?
                                    number_format($leaderboard->avg('score'), 1) : 0 }}</h3>
                                <p class="text-muted mb-0">Average Score</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaderboard -->
    <div class="col-12">
        <div class="card" style="border: 2px solid #f8d7da; border-radius: 15px;">
            <div class="card-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 13px 13px 0 0;">
                <h5 class="mb-0" style="color: #721c24; font-weight: 600;">
                    <i class="fas fa-list-ol me-2"></i>Rankings
                </h5>
            </div>
            <div class="card-body p-0">
                @if($leaderboard->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead style="background: linear-gradient(135deg, #fdf8f9 0%, #f8f9fa 100%);">
                            <tr>
                                <th style="color: #721c24; font-weight: 600; border: none;">Rank</th>
                                <th style="color: #721c24; font-weight: 600; border: none;">Player</th>
                                <th style="color: #721c24; font-weight: 600; border: none;">Score</th>
                                <th style="color: #721c24; font-weight: 600; border: none;">Completed At</th>
                                <th style="color: #721c24; font-weight: 600; border: none;">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($leaderboard as $index => $score)
                            <tr class="{{ $index < 3 ? 'winner-row' : '' }}" style="border: none;">
                                <td style="border: none;">
                                    <div class="d-flex align-items-center">
                                        @if($index == 0)
                                        <div class="rank-badge gold">
                                            <i class="fas fa-crown"></i>
                                            <span>1</span>
                                        </div>
                                        @elseif($index == 1)
                                        <div class="rank-badge silver">
                                            <i class="fas fa-medal"></i>
                                            <span>2</span>
                                        </div>
                                        @elseif($index == 2)
                                        <div class="rank-badge bronze">
                                            <i class="fas fa-award"></i>
                                            <span>3</span>
                                        </div>
                                        @else
                                        <div class="rank-badge regular">
                                            <span>{{ $index + 1 }}</span>
                                        </div>
                                        @endif
                                    </div>
                                </td>
                                <td style="border: none;">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-3"
                                            style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                            <span style="color: #721c24; font-weight: 600;">{{
                                                substr($score->guest->name, 0, 1) }}</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-0" style="color: #495057; font-weight: 600;">{{
                                                $score->guest->name }}</h6>
                                            @if($index < 3) <small class="text-warning">
                                                <i class="fas fa-star me-1"></i>Winner
                                                </small>
                                                @endif
                                        </div>
                                    </div>
                                </td>
                                <td style="border: none;">
                                    <span class="score-badge"
                                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); color: white; padding: 8px 15px; border-radius: 20px; font-weight: 600;">
                                        {{ $score->score }} pts
                                    </span>
                                </td>
                                <td style="border: none;">
                                    <span class="text-muted">{{ $score->completed_at->format('M d, Y H:i') }}</span>
                                </td>
                                <td style="border: none;">
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Completed
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-5">
                    <i class="fas fa-trophy fa-4x mb-3" style="color: #d63384; opacity: 0.3;"></i>
                    <h4 style="color: #721c24;">No Players Yet</h4>
                    <p class="text-muted">Waiting for guests to play the game...</p>
                    @if($gameConfig->is_starting)
                    <div class="mt-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="text-muted mt-2">Game is active - players can join now!</p>
                    </div>
                    @endif
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script>
    function refreshLeaderboard() {
    location.reload();
}

// Auto-refresh every 30 seconds if game is active
@if($gameConfig->is_starting)
setInterval(function() {
    refreshLeaderboard();
}, 30000);
@endif
</script>

<style>
    .winner-row {
        background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%) !important;
    }

    .rank-badge {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        position: relative;
    }

    .rank-badge.gold {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #8b6914;
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    }

    .rank-badge.silver {
        background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
        color: #666;
        box-shadow: 0 4px 15px rgba(192, 192, 192, 0.3);
    }

    .rank-badge.bronze {
        background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
        color: #5d4e37;
        box-shadow: 0 4px 15px rgba(205, 127, 50, 0.3);
    }

    .rank-badge.regular {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .rank-badge i {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 12px;
    }

    .score-badge {
        font-size: 14px;
        box-shadow: 0 2px 10px rgba(214, 51, 132, 0.2);
    }

    .table-hover tbody tr:hover {
        background-color: #fdf2f3 !important;
    }

    .avatar {
        transition: all 0.3s ease;
    }

    .avatar:hover {
        transform: scale(1.1);
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(214, 51, 132, 0.15);
    }
</style>
@endsection