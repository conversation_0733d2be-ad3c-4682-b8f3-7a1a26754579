<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ isset($demoMode) && $demoMode ? 'Wedding Trivia Demo' : ($gameConfig->game_title ?? 'Wedding Trivia') }}!
    </title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.7.77/Tone.js"></script>
    <!-- Jakarta Sans Font -->
    <link rel="stylesheet" href="{{ asset('vendor/dashboard') }}/assets/css/jakarta-sans.css" />
    <link rel="stylesheet" href="{{ asset('vendor/minigames/trivia/style.css') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body {
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #f3e8ff 100%);
            font-family: 'Jakarta Sans', sans-serif;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(251, 207, 232, 0.3);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(251, 207, 232, 0.2);
        }

        .btn {
            background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
        }

        /* Demo Mode Styles */
        .demo-badge {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 193, 7, 0.9);
            color: #721c24;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            z-index: 1000;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 193, 7, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }
        }

        .demo-watermark {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.1);
            color: rgba(114, 28, 36, 0.6);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 999;
            backdrop-filter: blur(5px);
        }

        .heading-font {
            font-family: 'Jakarta Sans', sans-serif;
            font-weight: 700;
        }
    </style>
</head>

<body class="min-h-screen flex items-center justify-center p-4">

    @if(isset($demoMode) && $demoMode)
    <!-- Demo Mode Indicators -->
    <div class="demo-badge">
        <i class="fas fa-play"></i> DEMO MODE
    </div>
    <div class="demo-watermark">
        Demo Experience - No Data Saved
    </div>
    @endif

    <!-- Animated background will be created here by JS -->
    <div class="background-container"></div>

    <div id="main-container" class="w-full max-w-2xl mx-auto">

        <!-- Welcome Screen -->
        <div id="welcome-screen" class="screen active card p-8 md:p-12 text-center">
            @if(isset($demoMode) && $demoMode)
            <!-- Demo Mode Welcome -->
            <div class="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-4 mb-6">
                <h2 class="text-lg font-bold text-yellow-800 mb-2">🎮 Demo Experience</h2>
                <p class="text-sm text-yellow-700">Try our interactive wedding trivia with sample questions!</p>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-pink-600 mb-4 heading-font">
                Wedding Trivia Demo
            </h1>
            <p class="text-lg text-gray-600 mb-2">Experience Interactive Wedding Entertainment</p>
            <p class="text-md text-gray-500 mb-8">Test your knowledge with our sample wedding questions.</p>
            @else
            <!-- Regular Game Welcome -->
            <h1 class="text-4xl md:text-5xl font-bold text-pink-600 mb-4 heading-font">
                Welcome to {{ $gameConfig->game_title }}!
            </h1>
            <p class="text-lg text-gray-600 mb-2">{{ $client->client_name }} Wedding Celebration</p>
            <p class="text-md text-gray-500 mb-8">Let's see how well you know the happy couple.</p>
            @endif

            @if(isset($demoMode) && $demoMode)
            <!-- Demo Mode - Always show name input -->
            <div class="mb-6">
                <input type="text" id="name-input" placeholder="Enter your name for demo"
                    class="w-full max-w-sm mx-auto p-4 border-2 border-pink-200 rounded-lg text-lg focus:ring-2 focus:ring-pink-400 focus:outline-none focus:border-pink-400">
            </div>
            @elseif(isset($guest) && $guest)
            <!-- Guest is pre-identified -->
            <div class="mb-6">
                <div class="bg-pink-50 border-2 border-pink-200 rounded-lg p-4">
                    <p class="text-lg font-semibold text-pink-800">Welcome, {{ $guest->name }}!</p>
                    <p class="text-sm text-pink-600">You're ready to play</p>
                </div>
                <input type="hidden" id="guest-id" value="{{ $guest->id }}">
                <input type="hidden" id="guest-name" value="{{ $guest->name }}">
            </div>
            @else
            <!-- Manual name entry -->
            <div class="mb-6">
                <input type="text" id="name-input" placeholder="Enter your name"
                    class="w-full max-w-sm mx-auto p-4 border-2 border-pink-200 rounded-lg text-lg focus:ring-2 focus:ring-pink-400 focus:outline-none focus:border-pink-400">
            </div>
            @endif

            <button id="start-btn" class="btn text-white font-bold py-3 px-12 rounded-full text-xl">
                @if(isset($demoMode) && $demoMode)
                Start Demo
                @else
                Start Game
                @endif
            </button>
            <p id="name-error" class="text-red-500 mt-4 hidden">Please enter your name to start!</p>

            <div class="mt-6 text-sm text-gray-500">
                @if(isset($demoMode) && $demoMode)
                <p>🎮 8 sample wedding questions</p>
                <p>💡 Experience the full game features</p>
                <p>🚀 <a href="{{ route('auth.page') }}" class="text-pink-600 hover:text-pink-800 underline">Create your
                        own game</a></p>
                @else
                <p>🏆 Top {{ $gameConfig->top_players }} players will be winners!</p>
                @if(isset($questions) && $questions->count() > 0)
                <p>📝 {{ $questions->count() }} questions waiting for you</p>
                @endif
                @endif
            </div>
        </div>

        <!-- Get Ready Screen -->
        <div id="get-ready-screen" class="screen text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-8"
                style="text-shadow: 2px 2px 4px rgba(0,0,0,0.2);">Get Ready...</h2>
            <div id="countdown-number">3</div>
        </div>

        <!-- Game Screen -->
        <div id="game-screen" class="screen w-full">
            <div id="score-popup"></div>
            <div
                class="flex justify-between items-center mb-4 text-white p-3 bg-violet-500/80 rounded-lg backdrop-blur-sm">
                <h2 id="player-name" class="text-lg font-bold">Player</h2>
                <div class="text-right">
                    <p class="text-xs">SCORE</p>
                    <p id="score" class="text-2xl font-bold">0</p>
                </div>
            </div>

            <div class="card p-6 md:p-8">
                <div class="text-center">
                    <p class="text-md text-gray-500">Question <span id="question-number">1</span> of <span
                            id="total-questions">10</span></p>
                    <h2 id="question"
                        class="text-xl md:text-2xl font-semibold mt-2 mb-4 min-h-[90px] flex items-center justify-center">
                        Question text goes here...</h2>
                </div>
                <div class="timer-bar-container mb-6">
                    <div id="timer-bar" class="timer-bar"></div>
                </div>
                <div id="answers" class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <!-- Answer buttons generated by JS -->
                </div>
            </div>
        </div>

        <!-- End Screen -->
        <div id="end-screen" class="screen card p-8 md:p-12 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-violet-600 mb-2">Congratulations!</h1>
            <p class="text-lg text-gray-600 mb-6">You've completed the trivia!</p>
            <div class="bg-violet-100 p-6 rounded-lg mb-8">
                <p class="text-xl text-violet-800">Your Final Score</p>
                <p id="final-score" class="text-6xl font-bold text-violet-600 mt-2">0</p>
            </div>
            <button id="restart-btn"
                class="btn bg-violet-500 hover:bg-violet-600 text-white font-bold py-3 px-12 rounded-full text-xl">
                Play Again
            </button>
        </div>
    </div>

    <!-- Game Configuration -->
    <script>
        // Game configuration from server
        @if(isset($demoMode) && $demoMode)
        window.gameConfig = {
            demoMode: true,
            gameTitle: {!! json_encode("Wedding Trivia Demo") !!},
            questions: @json($demoQuestions),
            demoLeaderboard: @json($demoLeaderboard ?? []),
            submitUrl: null, // No submission in demo mode
            csrfToken: "{{ csrf_token() }}"
        };
        @else
        window.gameConfig = {
            demoMode: false,
            clientId: {{ $client->id }},
            gameConfigId: {{ $triviaConfig->id }},
            guestId: {{ $guest->id ?? 'null' }},
            guestName: {!! json_encode($guest->name ?? '') !!},
            gameTitle: {!! json_encode($triviaConfig->game_title) !!},
            questions: @json($questions),
            submitUrl: {!! json_encode(route('guest.minigame.trivia.submit', [$client->unique_url, $guest->unique_url])) !!},
            csrfToken: "{{ csrf_token() }}"
        };
        @endif
    </script>
    <script src="{{ asset('vendor/minigames/trivia/dynamic-script.js') }}"></script>
</body>

</html>