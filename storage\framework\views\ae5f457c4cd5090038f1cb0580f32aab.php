<header class="pc-header">
    <div class="header-wrapper">
        <div class="me-auto pc-mob-drp">
            <ul class="list-unstyled">
                <!-- Mobile menu toggle -->
                <li class="pc-h-item pc-sidebar-collapse">
                    <a href="#" class="pc-head-link ms-0" id="sidebar-hide" onclick="toggleSidebar()">
                        <i class="ti ti-menu-2"></i>
                    </a>
                </li>
                <li class="pc-h-item pc-sidebar-popup">
                    <a href="#" class="pc-head-link ms-0" id="mobile-collapse" onclick="toggleSidebar()">
                        <i class="ti ti-menu-2"></i>
                    </a>
                </li>
                
                <!-- Page title for mobile -->
                <li class="pc-h-item d-lg-none">
                    <div class="pc-head-link">
                        <h6 class="mb-0 heading-font"><?php echo $__env->yieldContent('page-title', 'Dashboard'); ?></h6>
                    </div>
                </li>
            </ul>
        </div>
        
        <!-- Header right content -->
        <div class="ms-auto">
            <ul class="list-unstyled">
                <!-- Notifications -->
                <li class="dropdown pc-h-item">
                    <a class="pc-head-link dropdown-toggle arrow-none me-0" data-bs-toggle="dropdown" href="#"
                        role="button" aria-haspopup="false" aria-expanded="false">
                        <i class="fas fa-bell"></i>
                        <?php if(Auth::user()->client && Auth::user()->client->gameConfigs->where('is_starting', true)->count() > 0): ?>
                        <span class="badge bg-success pc-h-badge"><?php echo e(Auth::user()->client->gameConfigs->where('is_starting', true)->count()); ?></span>
                        <?php endif; ?>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end pc-h-dropdown">
                        <div class="dropdown-header d-flex align-items-center justify-content-between">
                            <h6 class="m-0">Notifications</h6>
                            <span class="badge bg-primary rounded-pill">
                                <?php echo e(Auth::user()->client ? Auth::user()->client->gameConfigs->where('is_starting', true)->count() : 0); ?>

                            </span>
                        </div>
                        <div class="dropdown-body text-wrap header-notification-scroll position-relative"
                            style="max-height: 215px;">
                            <?php if(Auth::user()->client && Auth::user()->client->gameConfigs->where('is_starting', true)->count() > 0): ?>
                                <?php $__currentLoopData = Auth::user()->client->gameConfigs->where('is_starting', true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activeGame): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-play-circle text-success"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <span class="float-end text-sm text-muted">Active</span>
                                        <h6 class="text-body mb-0"><?php echo e($activeGame->game_title); ?></h6>
                                        <p class="text-muted mb-0">Game is currently running</p>
                                    </div>
                                </div>
                                <hr class="border-secondary border-opacity-10 my-2">
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">No active notifications</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="text-center py-2">
                            <a href="<?php echo e(route('client.dashboard.minigames')); ?>" class="link-danger">View all games</a>
                        </div>
                    </div>
                </li>

                <!-- Quick Actions -->
                <li class="dropdown pc-h-item">
                    <a class="pc-head-link dropdown-toggle arrow-none me-0" data-bs-toggle="dropdown" href="#"
                        role="button" aria-haspopup="false" aria-expanded="false">
                        <i class="fas fa-plus-circle"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end pc-h-dropdown">
                        <div class="dropdown-header">
                            <h6 class="m-0">Quick Actions</h6>
                        </div>
                        <a href="<?php echo e(route('client.dashboard.minigames')); ?>" class="dropdown-item">
                            <i class="fas fa-gamepad me-2"></i>
                            <span>Manage Games</span>
                        </a>
                        <?php if(Auth::user()->client && Auth::user()->client->gameConfigs->count() > 0): ?>
                        <div class="dropdown-divider"></div>
                        <h6 class="dropdown-header">Start Games</h6>
                        <?php $__currentLoopData = Auth::user()->client->gameConfigs->where('is_starting', false)->where('is_done', false)->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $game): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e(route('client.dashboard.game-config', $game)); ?>" class="dropdown-item">
                            <i class="fas fa-play me-2"></i>
                            <span><?php echo e(Str::limit($game->game_title, 25)); ?></span>
                        </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </li>

                <!-- User Profile -->
                <li class="dropdown pc-h-item">
                    <a class="pc-head-link dropdown-toggle arrow-none me-0" data-bs-toggle="dropdown" href="#"
                        role="button" aria-haspopup="false" aria-expanded="false">
                        <div class="user-avatar-header">
                            <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                        </div>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end pc-h-dropdown">
                        <div class="dropdown-header">
                            <h6 class="m-0"><?php echo e(Auth::user()->name); ?></h6>
                            <span class="text-muted"><?php echo e(Auth::user()->email); ?></span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a href="#!" class="dropdown-item">
                            <i class="fas fa-user me-2"></i>
                            <span>My Profile</span>
                        </a>
                        <a href="#!" class="dropdown-item">
                            <i class="fas fa-cog me-2"></i>
                            <span>Account Settings</span>
                        </a>
                        <?php if(Auth::user()->client): ?>
                        <a href="#!" class="dropdown-item">
                            <i class="fas fa-heart me-2"></i>
                            <span><?php echo e(Auth::user()->client->client_name); ?></span>
                        </a>
                        <?php endif; ?>
                        <div class="dropdown-divider"></div>
                        <a href="<?php echo e(route('sign-out')); ?>" class="dropdown-item">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            <span>Logout</span>
                        </a>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</header>
<?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/client/partials/header.blade.php ENDPATH**/ ?>