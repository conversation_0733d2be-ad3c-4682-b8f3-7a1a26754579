<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\Guest;
use App\Models\TriviaQuestion;
use App\Models\TriviaResult;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard
     */
    public function index()
    {
        // Get dashboard statistics
        $stats = [
            'total_clients' => Client::count(),
            'total_games' => Game::count(),
            'total_game_configs' => GameConfig::count(),
            'total_guests' => Guest::count(),
            'total_questions' => TriviaQuestion::count(),
            'total_results' => TriviaResult::count(),
            'active_games' => GameConfig::where('is_active', true)->count(),
            'recent_clients' => Client::latest()->take(5)->get(),
            'recent_game_configs' => GameConfig::with('client', 'game')->latest()->take(5)->get(),
        ];

        return view('admin.dashboard.index', compact('stats'));
    }
}
