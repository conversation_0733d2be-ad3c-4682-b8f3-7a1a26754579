﻿<!DOCTYPE html>
<html lang="en">
<head>

<script src="assets/js/theme-script.js" type="04129d9120d7fdf083082985-text/javascript"></script>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<title>Dreams LMS</title>

<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.svg">

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/owl.carousel.min.css">
<link rel="stylesheet" href="assets/css/owl.theme.default.min.css">

<link rel="stylesheet" href="assets/plugins/slick/slick.css">
<link rel="stylesheet" href="assets/plugins/slick/slick-theme.css">

<link rel="stylesheet" href="assets/plugins/feather/feather.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body>

<div class="main-wrapper">

<header class="header header-page">
<div class="header-fixed">
<nav class="navbar navbar-expand-lg header-nav scroll-sticky">
<div class="container">
<div class="navbar-header">
<a id="mobile_btn" href="javascript:void(0);">
<span class="bar-icon">
<span></span>
<span></span>
<span></span>
</span>
</a>
<a href="index.html" class="navbar-brand logo">
<img src="assets/img/logo.svg" class="img-fluid" alt="Logo">
</a>
</div>
<div class="main-menu-wrapper">
<div class="menu-header">
<a href="index.html" class="menu-logo">
<img src="assets/img/logo.svg" class="img-fluid" alt="Logo">
</a>
<a id="menu_close" class="menu-close" href="javascript:void(0);">
<i class="fas fa-times"></i>
</a>
</div>
<ul class="main-nav">
<li class="has-submenu">
<a href="#">Home <i class="fas fa-chevron-down"></i></a>
<ul class="submenu">
<li><a href="index.html">Home</a></li>
<li><a href="index-two.html">Home Two</a></li>
<li><a href="index-three.html">Home Three</a></li>
<li><a href="index-four.html">Home Four</a></li>
</ul>
</li>
<li class="has-submenu">
<a href="#">Instructor <i class="fas fa-chevron-down"></i></a>
<ul class="submenu">
<li class="has-submenu">
<a href="instructor-list.html">Instructor</a>
<ul class="submenu">
<li><a href="instructor-list.html">List</a></li>
<li><a href="instructor-grid.html">Grid</a></li>
</ul>
</li>
<li><a href="instructor-dashboard.html">Dashboard</a></li>
<li><a href="instructor-profile.html">My Profile</a></li>
<li><a href="instructor-course.html">My Course</a></li>
<li><a href="instructor-wishlist.html">Wishlist</a></li>
<li><a href="instructor-reviews.html">Reviews</a></li>
<li><a href="instructor-quiz.html">My Quiz Attempts</a></li>
<li><a href="instructor-orders.html">Orders</a></li>
<li><a href="instructor-qa.html">Question & Answer</a></li>
<li><a href="instructor-referral.html">Referrals</a></li>
<li><a href="instructor-chat.html">Messages</a></li>
<li><a href="instructor-tickets.html">Support Ticket</a></li>
<li><a href="instructor-notifications.html">Notifications</a></li>
<li><a href="instructor-settings.html">Settings</a></li>
</ul>
</li>
<li class="has-submenu">
<a href="#">Student <i class="fas fa-chevron-down"></i></a>
<ul class="submenu first-submenu">
<li class="has-submenu">
<a href="students-list.html">Student</a>
<ul class="submenu">
<li><a href="students-list.html">List</a></li>
<li><a href="students-grid.html">Grid</a></li>
</ul>
</li>
<li><a href="student-dashboard.html">Student Dashboard</a></li>
<li><a href="student-profile.html">My Profile</a></li>
<li><a href="student-courses.html">Enrolled Courses</a></li>
<li><a href="student-wishlist.html">Wishlist</a></li>
<li><a href="student-reviews.html">Reviews</a></li>
<li><a href="student-quiz.html">My Quiz Attempts</a></li>
<li><a href="student-order-history.html">Orders</a></li>
<li><a href="student-qa.html">Question & Answer</a></li>
<li><a href="student-referral.html">Referrals</a></li>
<li><a href="student-messages.html">Messages</a></li>
<li><a href="student-tickets.html">Support Ticket</a></li>
<li><a href="student-settings.html">Settings</a></li>
</ul>
</li>
<li class="has-submenu">
<a href="#">Pages <i class="fas fa-chevron-down"></i></a>
<ul class="submenu">
<li><a href="notifications.html">Notification</a></li>
<li><a href="pricing-plan.html">Pricing Plan</a></li>
<li><a href="wishlist.html">Wishlist</a></li>
<li class="has-submenu">
<a href="course-list.html">Course</a>
<ul class="submenu">
<li><a href="add-course.html">Add Course</a></li>
<li><a href="course-list.html">Course List</a></li>
<li><a href="course-grid.html">Course Grid</a></li>
<li><a href="course-details.html">Course Details</a></li>
</ul>
</li>
<li class="has-submenu">
<a href="come-soon.html">Error</a>
<ul class="submenu">
<li><a href="come-soon.html">Coming Soon</a></li>
<li><a href="error-404.html">404</a></li>
<li><a href="error-500.html">500</a></li>
<li><a href="under-construction.html">Under Construction</a></li>
</ul>
</li>
<li><a href="faq.html">FAQ</a></li>
<li><a href="support.html">Support</a></li>
<li><a href="job-category.html">Category</a></li>
<li><a href="cart.html">Cart</a></li>
<li><a href="checkout.html">Checkout</a></li>
<li><a href="login.html">Login</a></li>
<li><a href="register.html">Register</a></li>
<li><a href="forgot-password.html">Forgot Password</a></li>
</ul>
</li>
<li class="has-submenu active">
<a href="#">Blog <i class="fas fa-chevron-down"></i></a>
<ul class="submenu">
<li><a href="blog-list.html">Blog List</a></li>
<li><a href="blog-grid.html">Blog Grid</a></li>
<li><a href="blog-masonry.html">Blog Masonry</a></li>
<li><a href="blog-modern.html">Blog Modern</a></li>
<li class="active"><a href="blog-details.html">Blog Details</a></li>
</ul>
</li>
<li class="login-link">
<a href="login.html">Login / Signup</a>
</li>
</ul>
</div>
<ul class="nav header-navbar-rht">
<li class="nav-item">
<div>
<a href="javascript:void(0);" id="dark-mode-toggle" class="dark-mode-toggle  ">
<i class="fa-solid fa-moon"></i>
</a>
<a href="javascript:void(0);" id="light-mode-toggle" class="dark-mode-toggle ">
<i class="fa-solid fa-sun"></i>
</a>
</div>
</li>
<li class="nav-item">
<a class="nav-link header-sign" href="login.html">Signin</a>
</li>
<li class="nav-item">
<a class="nav-link header-login" href="register.html">Signup</a>
</li>
</ul>
</div>
</nav>
</div>
</header>


<div class="breadcrumb-bar">
<div class="container">
<div class="row">
<div class="col-md-12 col-12">
<div class="breadcrumb-list">
<nav aria-label="breadcrumb" class="page-breadcrumb">
<ol class="breadcrumb">
<li class="breadcrumb-item"><a href="index.html">Home</a></li>
<li class="breadcrumb-item" aria-current="page">Pages</li>
<li class="breadcrumb-item" aria-current="page">Blog Details</li>
</ol>
</nav>
</div>
</div>
</div>
</div>
</div>


<section class="course-content">
<div class="container">
<div class="row">
<div class="col-lg-9 col-md-12">

<div class="blog">
<div class="blog-image">
<a href="blog-details.html"><img class="img-fluid" src="assets/img/blog-banner.jpg" alt="Post Image"></a>
</div>
<div class="blog-info clearfix">
<div class="post-left">
<ul>
<li>
<div class="post-author">
<a href="instructor-profile.html"><img src="assets/img/user/user.jpg" alt="Post Author"> <span>Ruby Perrin</span></a>
</div>
</li>
<li><img class="img-fluid" src="assets/img/icon/icon-22.svg" alt="Img">April 20,
2024</li>
<li><img class="img-fluid" src="assets/img/icon/icon-23.svg" alt="Img">Programming, Web Design</li>
</ul>
</div>
</div>
<h3 class="blog-title"><a href="blog-details.html">Learn Webs Applications Development from
Experts</a></h3>
<div class="blog-content">
<p>When working on a new piece of software, it’s essential to craft a software design
document to create a clear and precise vision of the client’s problem and your
proposed solution. Software design documents are important for detailing
expectations between the software engineer and the client. They help to streamline
the coding process before any code is written.</p>
<p>Read on to learn how to write great software design documents that improve
communication between you and your client, ensuring that everyone is on the same
page when working on a project together.</p>
<h4>What Is a Software Design Document?</h4>
<p>Before starting a new project with the client, the planning stage involves having a
clear vision and agreeing upon design goals. These goals should be laid out in a
technical specification document called a software design document.</p>
<p>A software developer is usually responsible for creating the software design
document. The developer will gather information from a client who wants a new piece
of software built and then they will make a document that details the proposed
solutions to the client's problems.</p>
<h4>What to Include in Your Software Design Document</h4>
<p>Problems within the coding world tend to vary, and engineering teams and developers
often write design documents differently. However, there is a certain software
design document template that you can follow to help you include all of the
essential pieces of information.</p>
<p>Here are some factors you should include:</p>
<p><strong>Key information:</strong> The title of the document, who’s working on the
project, and the date the document was last updated.</p>
<p><strong>Overview:</strong> A complete high-level summary of the project that helps
the company decide if they should continue reading the rest of the document.</p>
<p><strong>Pain points:</strong> A complete description of the problems and challenges,
alongside why this project is important. Furthermore, it’s essential to include how
it fits in with product and technical strategies.</p>
<p><strong>Goals:</strong> Accurately describe the users of the project and their
impact. In certain cases, the users may be another engineering team. It’s also
important to be clear on how to measure the success of metrics in conjunction with
goals and which KPIs will be used to track success. Lastly, it’s essential to state
which problems you won’t be fixing so that everyone who has read the document
understands what you will and will not be working on.</p>
<p><strong>Project milestones:</strong> Milestones in any project serve as a list of
measurable checkpoints, helping to break the entire project down into small parts. A
timeline and milestones can be used internally to help keep engineers on track and
show the client how the project will progress towards completion.</p>
<p><strong>Prioritization:</strong> After breaking the project down into smaller
features, it’s good to rank them in order of priority. The simplest way to do this
is to plot each feature in the project onto a prioritization matrix based on urgency
and impact.</p>
<p><strong>Solutions:</strong> Detail the current and proposed solutions to the client’s
problem. In this section, you’ll want to include what the existing solution is (if
any) and how users interact with that solution. The second part of this section is
to outline your proposed solution in as much detail as possible. It must be easy to
understand—another engineer should be able to read and build your proposed solution
without any prior knowledge of the project.</p>
<h4>How to Create a Software Design Document</h4>
<p>Keeping the above criteria in mind when creating your software design document is a
great start. To really maximize efficiency and communication, there are a few best
practices to implement.</p>
<p>Firstly, keep your language as simple as possible. The key is clarity — especially
when it comes to detailing technical points. Include visuals into your document,
helping readers accurately understand the points and data you’re trying to convey.
Diagrams, charts, and other timelines are a great way to communicate information.
</p>
<p>Send a draft of your document to the client, so they can catch parts you may have
missed or areas that are unclear and need fleshing out. Lastly, it’s important to
update your document as the project progresses, as you and other team members should
be consistently referencing the document.</p>
<p>When working on a new piece of software, it’s essential to craft a software design
document to create a clear and precise vision of the client’s problem and your
proposed solution. Software design documents are important for detailing
expectations between the software engineer and the client. They help to streamline
the coding process before any code is written.</p>
<p class="mb-0">Read on to learn how to write great software design documents that
improve communication between you and your client, ensuring that everyone is on the
same page when working on a project together.</p>
</div>
</div>

</div>

<div class="col-lg-3 col-md-12 sidebar-right theiaStickySidebar">

<div class="card search-widget blog-search blog-widget">
<div class="card-body">
<form class="search-form">
<div class="input-group">
<input type="text" placeholder="Search..." class="form-control">
<button type="submit" class="btn btn-primary"><i class="fa fa-search"></i></button>
</div>
</form>
</div>
</div>


<div class="card post-widget blog-widget">
<div class="card-header">
<h4 class="card-title">Recent Posts</h4>
</div>
<div class="card-body">
<ul class="latest-posts">
<li>
<div class="post-thumb">
<a href="blog-details.html">
<img class="img-fluid" src="assets/img/blog/blog-01.jpg" alt="Img">
</a>
</div>
<div class="post-info">
<h4>
<a href="blog-details.html">Learn Webs Applications Development from
Experts</a>
</h4>
<p><img class="img-fluid" src="assets/img/icon/icon-22.svg" alt="Img">Jun
14, 2024</p>
</div>
</li>
<li>
<div class="post-thumb">
<a href="blog-details.html">
<img class="img-fluid" src="assets/img/blog/blog-02.jpg" alt="Img">
</a>
</div>
<div class="post-info">
<h4>
<a href="blog-details.html">Expand Your Career Opportunities With
Python</a>
</h4>
<p><img class="img-fluid" src="assets/img/icon/icon-22.svg" alt="Img"> 3 Dec
2019</p>
</div>
</li>
<li>
<div class="post-thumb">
<a href="blog-details.html">
<img class="img-fluid" src="assets/img/blog/blog-03.jpg" alt="Img">
</a>
</div>
<div class="post-info">
<h4>
<a href="blog-details.html">Complete PHP Programming Career
Guideline</a>
</h4>
<p><img class="img-fluid" src="assets/img/icon/icon-22.svg" alt="Img"> 3 Dec
2019</p>
</div>
</li>
</ul>
</div>
</div>


<div class="card category-widget blog-widget">
<div class="card-header">
<h4 class="card-title">Categories</h4>
</div>
<div class="card-body">
<ul class="categories">
<li><a href="javascript:void(0);"><i class="fas fa-angle-right"></i> Business </a>
</li>
<li><a href="javascript:void(0);"><i class="fas fa-angle-right"></i> Courses </a>
</li>
<li><a href="javascript:void(0);"><i class="fas fa-angle-right"></i> Education </a>
</li>
<li><a href="javascript:void(0);"><i class="fas fa-angle-right"></i> Graphics Design
</a></li>
<li><a href="javascript:void(0);"><i class="fas fa-angle-right"></i> Programming
</a></li>
<li><a href="javascript:void(0);"><i class="fas fa-angle-right"></i> Web Design </a>
</li>
</ul>
</div>
</div>


<div class="card category-widget blog-widget">
<div class="card-header">
<h4 class="card-title">Archives</h4>
</div>
<div class="card-body">
<ul class="categories">
<li><a href="javascript:void(0);"><i class="fas fa-angle-right"></i> January 2024
</a></li>
<li><a href="javascript:void(0);"><i class="fas fa-angle-right"></i> February 2024
</a></li>
<li><a href="javascript:void(0);"><i class="fas fa-angle-right"></i> April 2024 </a>
</li>
</ul>
</div>
</div>


<div class="card tags-widget blog-widget tags-card">
<div class="card-header">
<h4 class="card-title">Latest Tags</h4>
</div>
<div class="card-body">
<ul class="tags">
<li><a href="javascript:void(0);" class="tag">HTML</a></li>
<li><a href="javascript:void(0);" class="tag">Java Script</a></li>
<li><a href="javascript:void(0);" class="tag">Css</a></li>
<li><a href="javascript:void(0);" class="tag">Jquery</a></li>
<li><a href="javascript:void(0);" class="tag">Java</a></li>
<li><a href="javascript:void(0);" class="tag">React</a></li>
</ul>
</div>
</div>

</div>

</div>
</div>
</section>


<footer class="footer">

<div class="footer-top">
<div class="container">
<div class="row">
<div class="col-lg-4 col-md-6">

<div class="footer-widget footer-about">
<div class="footer-logo">
<img src="assets/img/logo.svg" alt="logo">
</div>
<div class="footer-about-content">
<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut consequat mauris
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut consequat mauris</p>
</div>
</div>

</div>
<div class="col-lg-2 col-md-6">

<div class="footer-widget footer-menu">
<h2 class="footer-title">For Instructor</h2>
<ul>
<li><a href="instructor-profile.html">Profile</a></li>
<li><a href="login.html">Login</a></li>
<li><a href="register.html">Register</a></li>
<li><a href="instructor-list.html">Instructor</a></li>
<li><a href="instructor-dashboard.html"> Dashboard</a></li>
</ul>
</div>

</div>
<div class="col-lg-2 col-md-6">

<div class="footer-widget footer-menu">
<h2 class="footer-title">For Student</h2>
<ul>
<li><a href="student-profile.html">Profile</a></li>
<li><a href="login.html">Login</a></li>
<li><a href="register.html">Register</a></li>
<li><a href="students-list.html">Student</a></li>
<li><a href="student-dashboard.html"> Dashboard</a></li>
</ul>
</div>

</div>
<div class="col-lg-4 col-md-6">

<div class="footer-widget footer-contact">
<h2 class="footer-title">News letter</h2>
<div class="news-letter">
<form>
<input type="text" class="form-control" placeholder="Enter your email address" name="email">
</form>
</div>
<div class="footer-contact-info">
<div class="footer-address">
<img src="assets/img/icon/icon-20.svg" alt="Img" class="img-fluid">
<p> 3556 Beech Street, San Francisco,<br> California, CA 94108 </p>
</div>
<p>
<img src="assets/img/icon/icon-19.svg" alt="Img" class="img-fluid">
<a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="7f1b0d1a1e120c13120c3f1a071e120f131a511c1012">[email&#160;protected]</a>
</p>
<p class="mb-0">
<img src="assets/img/icon/icon-21.svg" alt="Img" class="img-fluid">
+19 123-456-7890
</p>
</div>
</div>

</div>
</div>
</div>
</div>


<div class="footer-bottom">
<div class="container">

<div class="copyright">
<div class="row">
<div class="col-md-6">
<div class="privacy-policy">
<ul>
<li><a href="term-condition.html">Terms</a></li>
<li><a href="privacy-policy.html">Privacy</a></li>
</ul>
</div>
</div>
<div class="col-md-6">
<div class="copyright-text">
<p class="mb-0">&copy; 2024 DreamsLMS. All rights reserved.</p>
</div>
</div>
</div>
</div>

</div>
</div>

</footer>

</div>


<script data-cfasync="false" src="../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="assets/js/jquery-3.7.1.min.js" type="04129d9120d7fdf083082985-text/javascript"></script>

<script src="assets/js/bootstrap.bundle.min.js" type="04129d9120d7fdf083082985-text/javascript"></script>

<script src="assets/js/jquery.waypoints.js" type="04129d9120d7fdf083082985-text/javascript"></script>
<script src="assets/js/jquery.counterup.min.js" type="04129d9120d7fdf083082985-text/javascript"></script>

<script src="assets/js/owl.carousel.min.js" type="04129d9120d7fdf083082985-text/javascript"></script>

<script src="assets/plugins/slick/slick.js" type="04129d9120d7fdf083082985-text/javascript"></script>

<script src="assets/plugins/feather/feather.min.js" type="04129d9120d7fdf083082985-text/javascript"></script>

<script src="assets/plugins/theia-sticky-sidebar/ResizeSensor.js" type="04129d9120d7fdf083082985-text/javascript"></script>
<script src="assets/plugins/theia-sticky-sidebar/theia-sticky-sidebar.js" type="04129d9120d7fdf083082985-text/javascript"></script>

<script src="assets/js/script.js" type="04129d9120d7fdf083082985-text/javascript"></script>
<script src="../cdn-cgi/scripts/7d0fa10a/cloudflare-static/rocket-loader.min.js" data-cf-settings="04129d9120d7fdf083082985-|49" defer=""></script></body>
</html>