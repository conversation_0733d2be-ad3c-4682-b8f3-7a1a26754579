<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string  $role
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, string $role)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.page')->with('error', 'Please login to access this page.');
        }

        $user = Auth::user();
        
        // Check if user has the required role
        if ($user->role !== $role) {
            // Redirect based on user's actual role
            switch ($user->role) {
                case 'admin':
                    return redirect()->route('admin.dashboard')->with('error', 'Access denied. You are logged in as an admin.');
                case 'client':
                    return redirect()->route('client.dashboard.index')->with('error', 'Access denied. You are logged in as a client.');
                default:
                    return redirect()->route('auth.page')->with('error', 'Access denied. Invalid user role.');
            }
        }

        return $next($request);
    }
}
