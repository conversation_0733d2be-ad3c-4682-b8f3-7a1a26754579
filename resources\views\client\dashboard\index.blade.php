@extends('client.layout.master')

@section('title', 'Dashboard')
@section('page-title', 'Client Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-card">
                <div class="d-flex align-items-center">
                    <div class="client-avatar me-3">
                        {{ substr($client->client_name, 0, 1) }}
                    </div>
                    <div>
                        <h2 class="heading-font mb-1">Welcome, {{ $client->client_name }}!</h2>
                        <p class="text-muted mb-0">Manage your wedding minigames and view guest participation</p>
                        <small class="text-info">
                            <i class="fas fa-link me-1"></i>Your unique URL: {{ $client->unique_url }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-gamepad"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ $stats['total_games'] }}</h3>
                    <p>Total Games</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card active">
                <div class="stat-icon">
                    <i class="fas fa-play"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ $stats['active_games'] }}</h3>
                    <p>Active Games</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ $stats['completed_games'] }}</h3>
                    <p>Completed</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ $stats['total_guests'] }}</h3>
                    <p>Total Guests</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ $stats['total_participants'] }}</h3>
                    <p>Participants</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ $stats['pending_games'] }}</h3>
                    <p>Pending</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Minigame Status -->
    <div class="col-12 mb-4">
        <div class="card" style="border: 2px solid #f8d7da; border-radius: 15px;">
            <div class="card-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 13px 13px 0 0;">
                <h5 class="mb-0" style="color: #721c24; font-weight: 600;">
                    <i class="fas fa-gamepad me-2"></i>Minigame Status
                </h5>
            </div>
            <div class="card-body">
                @if($client->is_have_minigame)
                <div class="alert alert-success"
                    style="border: 2px solid #d4edda; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Minigames are enabled!</strong> Your guests can access and play the configured games.
                </div>

                @if($client->gameConfigs->count() > 0)
                <div class="row">
                    @foreach($client->gameConfigs as $config)
                    <div class="col-md-6 mb-3">
                        <div class="card" style="border: 1px solid #f8d7da;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 style="color: #721c24; font-weight: 600;">{{ $config->game_title }}</h6>
                                        <small class="text-muted">{{ $config->game->display_name }}</small>
                                    </div>
                                    <div class="text-end">
                                        @if($config->is_starting)
                                        <span class="badge bg-success">Active</span>
                                        @elseif($config->is_done)
                                        <span class="badge bg-info">Completed</span>
                                        @else
                                        <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        Top {{ $config->top_players }} winners •
                                        {{ $config->guestScores->where('is_completed', true)->count() }} participants
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="text-center mt-3">
                    <a href="{{ route('client.dashboard.minigames') }}" class="btn btn-lg"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); color: white; border: none; border-radius: 25px; padding: 12px 30px;">
                        <i class="fas fa-gamepad me-2"></i>Manage Minigames
                    </a>
                </div>
                @else
                <div class="alert alert-warning"
                    style="border: 2px solid #ffeaa7; background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>No games configured yet.</strong> Contact your administrator to set up minigames.
                </div>
                @endif
                @else
                <div class="alert alert-secondary"
                    style="border: 2px solid #e2e3e5; background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Minigames are disabled.</strong> Contact your administrator to enable minigames for your
                    event.
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-12">
        <div class="card" style="border: 2px solid #f8d7da; border-radius: 15px;">
            <div class="card-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 13px 13px 0 0;">
                <h5 class="mb-0" style="color: #721c24; font-weight: 600;">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('client.dashboard.minigames') }}" class="btn btn-outline-primary"
                                style="border: 2px solid #d63384; color: #d63384; border-radius: 10px; padding: 15px;">
                                <i class="fas fa-gamepad fa-2x mb-2 d-block"></i>
                                <strong>Manage Games</strong>
                                <small class="d-block text-muted">Start, stop, and monitor games</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="#" class="btn btn-outline-success"
                                style="border: 2px solid #28a745; color: #28a745; border-radius: 10px; padding: 15px;">
                                <i class="fas fa-trophy fa-2x mb-2 d-block"></i>
                                <strong>View Leaderboards</strong>
                                <small class="d-block text-muted">Check game results and winners</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="#" class="btn btn-outline-info"
                                style="border: 2px solid #17a2b8; color: #17a2b8; border-radius: 10px; padding: 15px;">
                                <i class="fas fa-users fa-2x mb-2 d-block"></i>
                                <strong>Guest Management</strong>
                                <small class="d-block text-muted">View and manage guest list</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(214, 51, 132, 0.15);
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .alert {
        border-radius: 10px;
    }

    .badge {
        font-size: 11px;
        padding: 5px 10px;
        border-radius: 15px;
    }
</style>
@endsection