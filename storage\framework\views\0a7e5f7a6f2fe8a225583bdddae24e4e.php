<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="Interactive Wedding Minigames - Create memorable experiences for your special day">
    <meta name="keywords" content="wedding games, interactive entertainment, trivia, minigames">
    <meta name="author" content="Pecatu Minigame">

    <title><?php echo $__env->yieldContent('title', 'Pecatu Minigame - Interactive Wedding Entertainment'); ?></title>

    <!-- [Favicon] icon -->
    <link rel="icon" href="<?php echo e(asset('favicon.ico')); ?>" type="image/x-icon">

    <!-- [Font] Family - Jakarta Sans -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/jakarta-sans.css" />

    <!-- [Tabler Icons] https://tablericons.com -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/fonts/tabler-icons.min.css">
    <!-- [Feather Icons] https://feathericons.com -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/fonts/feather.css">
    <!-- [Font Awesome] https://fontawesome.com -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/fonts/fontawesome.css">
    <!-- [Material Icons] https://fonts.google.com/icons -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/fonts/material.css">

    <!-- [Template CSS Files] -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/style.css" id="main-style-link">
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/style-preset.css">

    <style>
        :root {
            --primary-pink: #ec4899;
            --secondary-pink: #f8d7da;
            --light-pink: #fdf2f8;
            --accent-pink: #be185d;
            --text-dark: #721c24;
            --text-light: #9ca3af;
        }

        body {
            font-family: 'Jakarta Sans', sans-serif;
            background: linear-gradient(135deg, var(--light-pink) 0%, rgba(251, 207, 232, 0.1) 100%);
            min-height: 100vh;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .game-card {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(251, 207, 232, 0.3);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            backdrop-filter: blur(10px);
        }

        .game-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(236, 72, 153, 0.2);
            border-color: var(--primary-pink);
        }

        .game-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }

        .game-title {
            color: var(--text-dark);
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .game-description {
            color: var(--text-light);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .btn-demo {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
            color: white;
            text-decoration: none;
        }

        .features-section {
            padding: 80px 0;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 2px solid rgba(251, 207, 232, 0.3);
        }

        .navbar-brand {
            color: var(--primary-pink) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            color: var(--text-dark) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-pink) !important;
        }

        .footer {
            background: var(--text-dark);
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .game-card {
                margin-bottom: 2rem;
            }
        }
    </style>

    <?php echo $__env->yieldContent('styles'); ?>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo e(route('landing.index')); ?>">
                <i class="fas fa-heart me-2"></i>Pecatu Minigame
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('landing.index')); ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('landing.trivia-demo')); ?>">Demo</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('auth.page')); ?>">Login</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main style="padding-top: 76px;">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <p>&copy; <?php echo e(date('Y')); ?> Pecatu Minigame. Creating memorable wedding experiences.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- [Page Specific JS] start -->
    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/plugins/popper.min.js"></script>
    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/plugins/simplebar.min.js"></script>
    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/plugins/bootstrap.min.js"></script>
    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/fonts/custom-font.js"></script>
    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/pcoded.js"></script>
    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/plugins/feather.min.js"></script>

    <?php echo $__env->yieldContent('scripts'); ?>
</body>

</html>
<?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/landing/layout.blade.php ENDPATH**/ ?>