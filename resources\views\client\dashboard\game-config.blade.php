@extends('client.layout.master')

@section('title', $gameConfig->game_title . ' - Game Details')
@section('page-title', $gameConfig->game_title)
@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('client.dashboard.minigames') }}">Minigames</a></li>
<li class="breadcrumb-item active">{{ $gameConfig->game_title }}</li>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="heading-font mb-1">{{ $gameConfig->game_title }}</h2>
                    <p class="text-muted mb-0">
                        <i class="fas fa-gamepad me-1"></i>{{ $gameConfig->game->display_name }} •
                        <i class="fas fa-user me-1"></i>{{ $client->client_name }}
                    </p>
                </div>
                <div class="game-status-large">
                    @if($gameConfig->is_starting)
                    <span class="status-badge-large running">
                        <i class="fas fa-play"></i> Game Running
                    </span>
                    @elseif($gameConfig->is_done)
                    <span class="status-badge-large completed">
                        <i class="fas fa-check"></i> Game Completed
                    </span>
                    @else
                    <span class="status-badge-large pending">
                        <i class="fas fa-clock"></i> Game Pending
                    </span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card-detailed">
                <div class="stat-icon-large">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content-large">
                    <h3>{{ $participationStats['total_participants'] }}</h3>
                    <p>Total Participants</p>
                    <small class="text-success">{{ $participationStats['completed_participants'] }} completed</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card-detailed">
                <div class="stat-icon-large">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-content-large">
                    <h3>{{ $gameConfig->top_players }}</h3>
                    <p>Top Players Shown</p>
                    <small class="text-info">Leaderboard limit</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card-detailed">
                <div class="stat-icon-large">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-content-large">
                    <h3>{{ number_format($participationStats['highest_score']) }}</h3>
                    <p>Highest Score</p>
                    <small class="text-warning">Best performance</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card-detailed">
                <div class="stat-icon-large">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-content-large">
                    <h3>{{ number_format($participationStats['average_score'], 1) }}</h3>
                    <p>Average Score</p>
                    <small class="text-primary">Overall performance</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Leaderboard -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 heading-font">
                        <i class="fas fa-trophy me-2"></i>Live Leaderboard
                    </h5>
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshLeaderboard()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    @if($leaderboard->count() > 0)
                    <div class="leaderboard-container" id="leaderboard-container">
                        @foreach($leaderboard as $score)
                        <div class="leaderboard-item {{ $score->rank <= 3 ? 'winner' : '' }}">
                            <div class="rank-display">
                                @if($score->rank == 1)
                                <span class="rank-badge gold">🥇 1</span>
                                @elseif($score->rank == 2)
                                <span class="rank-badge silver">🥈 2</span>
                                @elseif($score->rank == 3)
                                <span class="rank-badge bronze">🥉 3</span>
                                @else
                                <span class="rank-badge">{{ $score->rank }}</span>
                                @endif
                            </div>
                            <div class="player-info">
                                <h6 class="player-name">{{ $score->guest->name }}</h6>
                                <small class="player-email">{{ $score->guest->email ?? 'No email' }}</small>
                            </div>
                            <div class="score-display">
                                <span class="score-value">{{ number_format($score->score) }}</span>
                                <small class="score-time">{{ $score->completed_at->format('M d, H:i') }}</small>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-trophy fa-3x text-muted mb-3" style="opacity: 0.3;"></i>
                        <h5 class="text-muted">No Scores Yet</h5>
                        <p class="text-muted">Leaderboard will populate as guests complete the game</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Game Controls & Info -->
        <div class="col-lg-4 mb-4">
            <!-- Game Controls -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0 heading-font">
                        <i class="fas fa-gamepad me-2"></i>Game Controls
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if(!$gameConfig->is_starting && !$gameConfig->is_done)
                        <form action="{{ route('client.dashboard.start-game', $gameConfig) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-play me-1"></i>Start Game
                            </button>
                        </form>
                        @endif

                        @if($gameConfig->is_starting)
                        <form action="{{ route('client.dashboard.stop-game', $gameConfig) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-warning w-100">
                                <i class="fas fa-stop me-1"></i>Stop Game
                            </button>
                        </form>
                        @endif

                        <form action="{{ route('client.dashboard.restart-game', $gameConfig) }}" method="POST"
                            onsubmit="return confirm('Are you sure? This will clear all scores!')">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-redo me-1"></i>Reset Game
                            </button>
                        </form>

                        <a href="{{ route('client.dashboard.leaderboard', $gameConfig) }}"
                            class="btn btn-outline-info w-100">
                            <i class="fas fa-external-link-alt me-1"></i>Full Leaderboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Game Configuration -->
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 heading-font">
                        <i class="fas fa-cog me-2"></i>Game Configuration
                    </h5>
                    <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#configModal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div class="card-body">
                    <div class="config-item">
                        <strong>Game Title:</strong>
                        <span>{{ $gameConfig->game_title }}</span>
                    </div>
                    <div class="config-item">
                        <strong>Top Players Shown:</strong>
                        <span>{{ $gameConfig->top_players }}</span>
                    </div>
                    <div class="config-item">
                        <strong>Game Type:</strong>
                        <span>{{ $gameConfig->game->display_name }}</span>
                    </div>
                    @if($gameConfig->config_data)
                    @foreach($gameConfig->config_data as $key => $value)
                    <div class="config-item">
                        <strong>{{ ucfirst(str_replace('_', ' ', $key)) }}:</strong>
                        <span>{{ is_array($value) ? implode(', ', $value) : $value }}</span>
                    </div>
                    @endforeach
                    @endif
                </div>
            </div>

            <!-- Trivia Questions (if applicable) -->
            @if($gameConfig->game->minigame_name === 'trivia')
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 heading-font">
                        <i class="fas fa-question-circle me-2"></i>Trivia Questions
                    </h5>
                    <a href="{{ route('client.dashboard.questions', $gameConfig) }}"
                        class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit"></i> Manage
                    </a>
                </div>
                <div class="card-body">
                    <div class="trivia-stats">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total Questions:</span>
                            <strong>{{ $triviaQuestions ? $triviaQuestions->count() : 0 }}</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Active Questions:</span>
                            <strong>{{ $triviaQuestions ? $triviaQuestions->where('is_active', true)->count() : 0
                                }}</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Average Points:</span>
                            <strong>{{ $triviaQuestions ? round($triviaQuestions->avg('points'), 1) : 0 }}</strong>
                        </div>
                    </div>

                    @if(!$triviaQuestions || $triviaQuestions->count() == 0)
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        No questions configured yet. <a
                            href="{{ route('client.dashboard.questions', $gameConfig) }}">Add questions</a> to start the
                        game.
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Game Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0 heading-font">
                        <i class="fas fa-info-circle me-2"></i>Game Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>Game Type:</strong>
                        <span>{{ $gameConfig->game->display_name }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Created:</strong>
                        <span>{{ $gameConfig->created_at->format('M d, Y') }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Last Updated:</strong>
                        <span>{{ $gameConfig->updated_at->format('M d, Y H:i') }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Guest URL:</strong>
                        <small class="text-break">
                            {{ url('/minigame/' . $client->id . '/' . $client->unique_url . '/trivia') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back Button -->
    <div class="row">
        <div class="col-12">
            <a href="{{ route('client.dashboard.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>
    </div>
</div>

<style>
    /* Game Config Specific Styles */
    .status-badge-large {
        padding: 8px 16px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1rem;
    }

    .status-badge-large.running {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
        border: 2px solid rgba(40, 167, 69, 0.3);
    }

    .status-badge-large.completed {
        background: rgba(108, 117, 125, 0.1);
        color: #6c757d;
        border: 2px solid rgba(108, 117, 125, 0.3);
    }

    .status-badge-large.pending {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
        border: 2px solid rgba(255, 193, 7, 0.3);
    }

    .stat-card-detailed {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        height: 100%;
    }

    .stat-card-detailed:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(236, 72, 153, 0.15);
    }

    .stat-icon-large {
        font-size: 2.5rem;
        color: var(--primary-pink);
        margin-bottom: 15px;
    }

    .stat-content-large h3 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        color: var(--text-dark);
    }

    .stat-content-large p {
        margin-bottom: 5px;
        font-weight: 600;
        color: var(--text-dark);
    }

    /* Leaderboard Styles */
    .leaderboard-container {
        max-height: 600px;
        overflow-y: auto;
    }

    .leaderboard-item {
        display: flex;
        align-items: center;
        padding: 15px;
        margin-bottom: 10px;
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .leaderboard-item.winner {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
        border-color: rgba(255, 193, 7, 0.5);
    }

    .leaderboard-item:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(236, 72, 153, 0.1);
    }

    .rank-badge {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        margin-right: 15px;
        background: var(--light-pink);
        color: var(--text-dark);
    }

    .rank-badge.gold {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #b8860b;
    }

    .rank-badge.silver {
        background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
        color: #696969;
    }

    .rank-badge.bronze {
        background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
        color: #8b4513;
    }

    .player-info {
        flex: 1;
        margin-right: 15px;
    }

    .player-name {
        margin-bottom: 2px;
        color: var(--text-dark);
        font-weight: 600;
    }

    .player-email {
        color: var(--text-light);
    }

    .score-display {
        text-align: right;
    }

    .score-value {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-pink);
    }

    .score-time {
        color: var(--text-light);
    }

    /* Info Items */
    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(251, 207, 232, 0.2);
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .trivia-stats {
        font-size: 0.9rem;
    }

    .config-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(251, 207, 232, 0.2);
    }

    .config-item:last-child {
        border-bottom: none;
    }
</style>

<script>
    // Auto-refresh leaderboard every 30 seconds if game is running
@if($gameConfig->is_starting)
setInterval(refreshLeaderboard, 30000);
@endif

function refreshLeaderboard() {
    fetch('{{ route("client.dashboard.live-leaderboard", $gameConfig) }}')
        .then(response => response.json())
        .then(data => {
            updateLeaderboardDisplay(data.leaderboard);
        })
        .catch(error => console.error('Error refreshing leaderboard:', error));
}

function updateLeaderboardDisplay(leaderboard) {
    const container = document.getElementById('leaderboard-container');
    if (!container || leaderboard.length === 0) return;
    
    let html = '';
    leaderboard.forEach(score => {
        const isWinner = score.rank <= 3;
        const rankBadge = score.rank === 1 ? '🥇 1' : 
                         score.rank === 2 ? '🥈 2' : 
                         score.rank === 3 ? '🥉 3' : score.rank;
        const rankClass = score.rank === 1 ? 'gold' : 
                         score.rank === 2 ? 'silver' : 
                         score.rank === 3 ? 'bronze' : '';
        
        html += `
            <div class="leaderboard-item ${isWinner ? 'winner' : ''}">
                <div class="rank-display">
                    <span class="rank-badge ${rankClass}">${rankBadge}</span>
                </div>
                <div class="player-info">
                    <h6 class="player-name">${score.guest_name}</h6>
                </div>
                <div class="score-display">
                    <span class="score-value">${score.score.toLocaleString()}</span>
                    <small class="score-time">${score.completed_at}</small>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}
</script>

<!-- Game Configuration Modal -->
<div class="modal fade" id="configModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="border-radius: 15px;">
            <div class="modal-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 15px 15px 0 0;">
                <h5 class="modal-title" style="color: #721c24; font-weight: 600;">Edit Game Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('client.dashboard.game-config.update', $gameConfig) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="game_title" class="form-label" style="color: #721c24; font-weight: 600;">Game
                            Title</label>
                        <input type="text" class="form-control" id="game_title" name="game_title"
                            value="{{ $gameConfig->game_title }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="top_players" class="form-label" style="color: #721c24; font-weight: 600;">Top
                            Players to Show</label>
                        <input type="number" class="form-control" id="top_players" name="top_players"
                            value="{{ $gameConfig->top_players }}" min="1" max="100" required>
                        <small class="text-muted">Number of players to display on the leaderboard</small>
                    </div>

                    @if($gameConfig->game->minigame_name === 'trivia')
                    <div class="mb-3">
                        <label class="form-label" style="color: #721c24; font-weight: 600;">Trivia Settings</label>

                        <div class="mb-2">
                            <label for="default_time_limit" class="form-label">Default Time Limit (seconds)</label>
                            <input type="number" class="form-control" id="default_time_limit"
                                name="config_data[default_time_limit]"
                                value="{{ $gameConfig->config_data['default_time_limit'] ?? 30 }}" min="5" max="300">
                        </div>

                        <div class="mb-2">
                            <label for="default_points" class="form-label">Default Points per Question</label>
                            <input type="number" class="form-control" id="default_points"
                                name="config_data[default_points]"
                                value="{{ $gameConfig->config_data['default_points'] ?? 100 }}" min="1" max="1000">
                        </div>

                        <div class="mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="randomize_questions"
                                    name="config_data[randomize_questions]" value="1" {{
                                    ($gameConfig->config_data['randomize_questions'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="randomize_questions">
                                    Randomize Question Order
                                </label>
                            </div>
                        </div>

                        <div class="mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_correct_answer"
                                    name="config_data[show_correct_answer]" value="1" {{
                                    ($gameConfig->config_data['show_correct_answer'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_correct_answer">
                                    Show Correct Answer After Each Question
                                </label>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); border: none;">
                        <i class="fas fa-save me-1"></i>Save Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection