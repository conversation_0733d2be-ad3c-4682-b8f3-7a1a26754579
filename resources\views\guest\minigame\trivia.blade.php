<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $triviaConfig->game_title }} - {{ $client->client_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-pink: #ec4899;
            --secondary-pink: #f8d7da;
            --light-pink: #fdf2f8;
            --accent-pink: #be185d;
            --text-dark: #721c24;
            --text-light: #9ca3af;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--light-pink) 0%, rgba(251, 207, 232, 0.3) 100%);
            min-height: 100vh;
            color: var(--text-dark);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .game-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .game-title {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-pink);
            margin-bottom: 10px;
        }

        .client-name {
            font-size: 1.2rem;
            color: var(--text-light);
            margin-bottom: 20px;
        }

        .game-instructions {
            background: rgba(236, 72, 153, 0.1);
            border: 2px solid rgba(236, 72, 153, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            display: none;
        }

        .question-card.active {
            display: block;
        }

        .question-number {
            color: var(--primary-pink);
            font-weight: 600;
            margin-bottom: 10px;
        }

        .question-text {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .question-image {
            text-align: center;
            margin-bottom: 25px;
        }

        .question-image img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 15px;
            border: 2px solid rgba(251, 207, 232, 0.5);
        }

        .answer-options {
            display: grid;
            gap: 15px;
        }

        .answer-option {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .answer-option:hover {
            background: rgba(236, 72, 153, 0.1);
            border-color: var(--primary-pink);
            transform: translateY(-2px);
        }

        .answer-option.selected {
            background: var(--primary-pink);
            color: white;
            border-color: var(--accent-pink);
        }

        .answer-option input[type="radio"] {
            margin-right: 15px;
            transform: scale(1.2);
        }

        .answer-text {
            flex: 1;
            font-weight: 500;
        }

        .open-ended-input {
            width: 100%;
            padding: 15px;
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 15px;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .open-ended-input:focus {
            outline: none;
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.1);
        }

        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: var(--text-dark);
            border: 2px solid rgba(251, 207, 232, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(251, 207, 232, 0.3);
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 25px;
            height: 10px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .guest-form {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 15px;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .game-title {
                font-size: 2rem;
            }
            
            .question-text {
                font-size: 1.1rem;
            }
            
            .answer-option {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Game Header -->
        <div class="game-header">
            <h1 class="game-title">{{ $triviaConfig->game_title }}</h1>
            <p class="client-name">{{ $client->client_name }}</p>
            <div class="game-instructions">
                <h5><i class="fas fa-info-circle me-2"></i>How to Play</h5>
                <p class="mb-0">Answer all questions to the best of your ability. You can only play once, so make it count! Good luck!</p>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>

        <!-- Guest Information Form -->
        <div class="guest-form" id="guest-form">
            <h4 class="mb-3"><i class="fas fa-user me-2"></i>Enter Your Information</h4>
            <div class="form-group">
                <label for="guest_name" class="form-label">Your Name *</label>
                <input type="text" id="guest_name" name="guest_name" class="form-control" 
                       placeholder="Enter your full name" required>
            </div>
            <button type="button" class="btn btn-primary" onclick="startGame()">
                <i class="fas fa-play"></i> Start Game
            </button>
        </div>

        <!-- Questions Form -->
        <form id="trivia-form" method="POST" action="{{ route('guest.minigame.trivia.submit', [$client->id, $client->unique_url]) }}" style="display: none;">
            @csrf
            <input type="hidden" id="hidden_guest_name" name="guest_name">
            
            @foreach($questions as $index => $question)
            <div class="question-card" data-question="{{ $index + 1 }}">
                <div class="question-number">Question {{ $index + 1 }} of {{ $questions->count() }}</div>
                <div class="question-text">{{ $question->question_text }}</div>
                
                @if($question->question_image)
                <div class="question-image">
                    <img src="{{ Storage::url($question->question_image) }}" alt="Question image">
                </div>
                @endif

                <div class="answer-options">
                    @if($question->option_a || $question->option_b || $question->option_c || $question->option_d)
                        <!-- Multiple Choice -->
                        @if($question->option_a)
                        <div class="answer-option" onclick="selectAnswer(this, '{{ $question->option_a }}', {{ $question->id }})">
                            <input type="radio" name="answers[{{ $question->id }}]" value="{{ $question->option_a }}" id="q{{ $question->id }}_a">
                            <span class="answer-text">{{ $question->option_a }}</span>
                        </div>
                        @endif
                        
                        @if($question->option_b)
                        <div class="answer-option" onclick="selectAnswer(this, '{{ $question->option_b }}', {{ $question->id }})">
                            <input type="radio" name="answers[{{ $question->id }}]" value="{{ $question->option_b }}" id="q{{ $question->id }}_b">
                            <span class="answer-text">{{ $question->option_b }}</span>
                        </div>
                        @endif
                        
                        @if($question->option_c)
                        <div class="answer-option" onclick="selectAnswer(this, '{{ $question->option_c }}', {{ $question->id }})">
                            <input type="radio" name="answers[{{ $question->id }}]" value="{{ $question->option_c }}" id="q{{ $question->id }}_c">
                            <span class="answer-text">{{ $question->option_c }}</span>
                        </div>
                        @endif
                        
                        @if($question->option_d)
                        <div class="answer-option" onclick="selectAnswer(this, '{{ $question->option_d }}', {{ $question->id }})">
                            <input type="radio" name="answers[{{ $question->id }}]" value="{{ $question->option_d }}" id="q{{ $question->id }}_d">
                            <span class="answer-text">{{ $question->option_d }}</span>
                        </div>
                        @endif
                    @else
                        <!-- Open-ended -->
                        <input type="text" name="answers[{{ $question->id }}]" class="open-ended-input" 
                               placeholder="Type your answer here..." required>
                    @endif
                </div>
            </div>
            @endforeach

            <!-- Navigation -->
            <div class="navigation-buttons">
                <button type="button" class="btn btn-secondary" id="prev-btn" onclick="previousQuestion()" style="display: none;">
                    <i class="fas fa-arrow-left"></i> Previous
                </button>
                <button type="button" class="btn btn-primary" id="next-btn" onclick="nextQuestion()">
                    Next <i class="fas fa-arrow-right"></i>
                </button>
                <button type="submit" class="btn btn-primary" id="submit-btn" style="display: none;">
                    <i class="fas fa-check"></i> Submit Answers
                </button>
            </div>
        </form>
    </div>

    <script>
        let currentQuestion = 1;
        const totalQuestions = {{ $questions->count() }};

        function startGame() {
            const guestName = document.getElementById('guest_name').value.trim();
            if (!guestName) {
                alert('Please enter your name to start the game.');
                return;
            }

            document.getElementById('hidden_guest_name').value = guestName;
            document.getElementById('guest-form').style.display = 'none';
            document.getElementById('trivia-form').style.display = 'block';
            showQuestion(1);
            updateProgress();
        }

        function showQuestion(questionNum) {
            // Hide all questions
            document.querySelectorAll('.question-card').forEach(card => {
                card.classList.remove('active');
            });

            // Show current question
            document.querySelector(`[data-question="${questionNum}"]`).classList.add('active');

            // Update navigation buttons
            document.getElementById('prev-btn').style.display = questionNum > 1 ? 'block' : 'none';
            document.getElementById('next-btn').style.display = questionNum < totalQuestions ? 'block' : 'none';
            document.getElementById('submit-btn').style.display = questionNum === totalQuestions ? 'block' : 'none';

            updateProgress();
        }

        function nextQuestion() {
            if (currentQuestion < totalQuestions) {
                currentQuestion++;
                showQuestion(currentQuestion);
            }
        }

        function previousQuestion() {
            if (currentQuestion > 1) {
                currentQuestion--;
                showQuestion(currentQuestion);
            }
        }

        function selectAnswer(element, value, questionId) {
            // Remove selected class from siblings
            element.parentNode.querySelectorAll('.answer-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selected class to clicked option
            element.classList.add('selected');

            // Check the radio button
            element.querySelector('input[type="radio"]').checked = true;
        }

        function updateProgress() {
            const progress = (currentQuestion / totalQuestions) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';
        }

        // Auto-advance on answer selection for better UX
        document.addEventListener('change', function(e) {
            if (e.target.type === 'radio') {
                setTimeout(() => {
                    if (currentQuestion < totalQuestions) {
                        nextQuestion();
                    }
                }, 500);
            }
        });
    </script>
</body>
</html>
