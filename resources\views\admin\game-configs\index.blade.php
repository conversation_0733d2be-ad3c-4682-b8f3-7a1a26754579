@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-cogs me-2"></i>Game Configurations Management
                </h5>
                <a href="{{ route('admin.game-configs.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>Add New Configuration
                </a>
            </div>
            <div class="card-body">
                <!-- Filter Section -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <select class="form-control" id="clientFilter">
                            <option value="">All Clients</option>
                            @foreach($clients as $client)
                                <option value="{{ $client->id }}">{{ $client->client_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-control" id="gameFilter">
                            <option value="">All Games</option>
                            @foreach($games as $game)
                                <option value="{{ $game->id }}">{{ $game->display_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-control" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="starting">Running</option>
                            <option value="done">Completed</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Client</th>
                                <th>Game</th>
                                <th>Game Title</th>
                                <th>Top Players</th>
                                <th>Status</th>
                                <th>Participants</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($gameConfigs as $config)
                            <tr>
                                <td>{{ $loop->iteration + ($gameConfigs->currentPage() - 1) * $gameConfigs->perPage() }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="client-avatar me-2">
                                            {{ substr($config->client->client_name, 0, 1) }}
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $config->client->client_name }}</h6>
                                            <small class="text-muted">{{ $config->client->unique_url }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="game-info">
                                        <span class="badge bg-primary">{{ $config->game->display_name }}</span>
                                        <small class="d-block text-muted">{{ $config->game->minigame_name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <h6 class="mb-0">{{ $config->game_title }}</h6>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $config->top_players }} players</span>
                                </td>
                                <td>
                                    @if($config->is_starting)
                                        <span class="badge bg-success">
                                            <i class="fas fa-play me-1"></i>Running
                                        </span>
                                    @elseif($config->is_done)
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-check me-1"></i>Completed
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>Pending
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="participants-info">
                                        <span class="badge bg-primary">{{ $config->guestScores->count() }}</span>
                                        <small class="text-muted d-block">participants</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.game-configs.show', $config) }}" class="btn btn-sm btn-outline-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.game-configs.edit', $config) }}" class="btn btn-sm btn-outline-warning" title="Edit Configuration">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if($config->game->minigame_name === 'trivia')
                                            <a href="{{ route('admin.trivia-questions.index') }}?config={{ $config->id }}" class="btn btn-sm btn-outline-success" title="Manage Questions">
                                                <i class="fas fa-question-circle"></i>
                                            </a>
                                        @endif
                                        <form action="{{ route('admin.game-configs.destroy', $config) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this configuration?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Configuration">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div style="color: #6c757d;">
                                        <i class="fas fa-cogs fa-3x mb-3" style="opacity: 0.3;"></i>
                                        <p class="mb-0">No game configurations found</p>
                                        <small>Create your first configuration to get started</small>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                
                @if($gameConfigs->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $gameConfigs->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
/* Game configs specific styles */
.client-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--secondary-pink) 0%, #f5c6cb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    font-weight: 600;
    font-size: 14px;
}

.game-info .badge {
    font-size: 0.8rem;
}

.participants-info {
    text-align: center;
}

.btn-group .btn {
    margin-right: 2px;
}

/* Filter styles */
.form-control {
    border: 2px solid rgba(251, 207, 232, 0.5);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-pink);
    box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25);
}
</style>

<script>
// Simple client-side filtering
document.getElementById('clientFilter').addEventListener('change', function() {
    console.log('Filter by client:', this.value);
});

document.getElementById('gameFilter').addEventListener('change', function() {
    console.log('Filter by game:', this.value);
});

document.getElementById('statusFilter').addEventListener('change', function() {
    console.log('Filter by status:', this.value);
});
</script>
@endsection
