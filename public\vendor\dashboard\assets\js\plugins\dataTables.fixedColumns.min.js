/*! FixedColumns 5.0.0
 * © SpryMedia Ltd - datatables.net/license
 */
!function(s){var i,o;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return s(t,window,document)}):"object"==typeof exports?(i=require("jquery"),o=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||i(t),o(t,e),s(e,0,t.document)}:(o(window,i),module.exports=s(i,window,window.document))):s(jQuery,window,document)}(function(o,t,e){"use strict";var h,l,s,i,n=o.fn.dataTable;function d(t,e){var s,i=this;if(l&&l.versionCheck&&l.versionCheck("2"))return t=new l.Api(t),this.classes=h.extend(!0,{},d.classes),this.c=h.extend(!0,{},d.defaults,e),this.s={dt:t,rtl:"rtl"===h(t.table().node()).css("direction")},e&&void 0!==e.leftColumns&&(e.left=e.leftColumns),e&&void 0!==e.left&&(this.c[this.s.rtl?"end":"start"]=e.left),e&&void 0!==e.rightColumns&&(e.right=e.rightColumns),e&&void 0!==e.right&&(this.c[this.s.rtl?"start":"end"]=e.right),this.dom={bottomBlocker:h("<div>").addClass(this.classes.bottomBlocker),topBlocker:h("<div>").addClass(this.classes.topBlocker),scroller:h("div.dt-scroll-body",this.s.dt.table().container())},this.s.dt.settings()[0]._bInitComplete?(this._addStyles(),this._setKeyTableListener()):t.one("init.dt.dtfc",function(){i._addStyles(),i._setKeyTableListener()}),t.on("column-sizing.dt.dtfc column-reorder.dt.dtfc draw.dt.dtfc",function(){return i._addStyles()}),s=l.util.debounce(function(){i._addStyles()},50),t.on("column-visibility.dt.dtfc",function(){s()}),this.dom.scroller.on("scroll.dtfc",function(){return i._scroll()}),this._scroll(),t.settings()[0]._fixedColumns=this,t.on("destroy",function(){return i._destroy()}),this;throw new Error("FixedColumns requires DataTables 2 or newer")}function r(t,e){void 0===e&&(e=null);t=new n.Api(t),e=e||t.init().fixedColumns||n.defaults.fixedColumns;new s(t,e)}return d.prototype.end=function(t){return void 0!==t?(0<=t&&t<=this.s.dt.columns().count()&&(this.c.end=t,this._addStyles()),this):this.c.end},d.prototype.left=function(t){return this.s.rtl?this.end(t):this.start(t)},d.prototype.right=function(t){return this.s.rtl?this.start(t):this.end(t)},d.prototype.start=function(t){return void 0!==t?(0<=t&&t<=this.s.dt.columns().count()&&(this.c.start=t,this._addStyles()),this):this.c.start},d.prototype._addStyles=function(){var s=this.s.dt,i=this,o=this.s.dt.columns(":visible").count(),l=s.table().header.structure(":visible"),n=s.table().footer.structure(":visible"),d=s.columns(":visible").widths().toArray(),t=h(s.table().node()).closest("div.dt-scroll"),e=h(s.table().node()).closest("div.dt-scroll-body")[0],r=this.s.rtl,c=this.c.start,a=this.c.end,f=r?a:c,r=r?c:a,u=s.settings()[0].oBrowser.barWidth;if(0===t.length)return this;e.offsetWidth===e.clientWidth&&(u=0),s.columns(":visible").every(function(t){var e,t=s.column.index("toVisible",t);t<c?(e=i._sum(d,t),i._fixColumn(t,e,"start",l,n,u)):o-a<=t?(e=i._sum(d,o-t-1,!0),i._fixColumn(t,e,"end",l,n,u)):i._fixColumn(t,0,"none",l,n,u)}),h(s.table().node()).toggleClass(i.classes.tableFixedStart,0<c).toggleClass(i.classes.tableFixedEnd,0<a).toggleClass(i.classes.tableFixedLeft,0<f).toggleClass(i.classes.tableFixedRight,0<r);e=s.table().header(),f=s.table().footer(),r=h(e).outerHeight(),e=h(f).outerHeight();this.dom.topBlocker.appendTo(t).css("top",0).css(this.s.rtl?"left":"right",0).css("height",r).css("width",u+1).css("display",u?"block":"none"),f&&this.dom.bottomBlocker.appendTo(t).css("bottom",0).css(this.s.rtl?"left":"right",0).css("height",e).css("width",u+1).css("display",u?"block":"none")},d.prototype._destroy=function(){this.s.dt.off(".dtfc"),this.dom.scroller.off(".dtfc"),h(this.s.dt.table().node()).removeClass(this.classes.tableScrollingEnd+" "+this.classes.tableScrollingLeft+" "+this.classes.tableScrollingStart+" "+this.classes.tableScrollingRight),this.dom.bottomBlocker.remove(),this.dom.topBlocker.remove()},d.prototype._fixColumn=function(e,o,l,t,s,n){function i(t,e){var s,i;"none"===l?t.css("position","").css("left","").css("right","").removeClass(d.classes.fixedEnd+" "+d.classes.fixedLeft+" "+d.classes.fixedRight+" "+d.classes.fixedStart):(s="start"===l?"left":"right",d.s.rtl&&(s="start"===l?"right":"left"),i=o,"end"!==l||"header"!==e&&"footer"!==e||(i+=n),t.css("position","sticky").css(s,i).addClass("start"===l?d.classes.fixedStart:d.classes.fixedEnd).addClass("left"===s?d.classes.fixedLeft:d.classes.fixedRight))}var d=this,r=this.s.dt;t.forEach(function(t){t[e]&&i(h(t[e].cell),"header")}),i(r.column(e+":visible",{page:"current"}).nodes().to$(),"body"),s&&s.forEach(function(t){t[e]&&i(h(t[e].cell),"footer")})},d.prototype._scroll=function(){var t,e,s,i,o=this.dom.scroller[0];o&&(t=h(this.s.dt.table().node()).add(this.s.dt.table().header().parentNode).add(this.s.dt.table().footer().parentNode).add("div.dt-scroll-headInner table",this.s.dt.table().container()).add("div.dt-scroll-footInner table",this.s.dt.table().container()),e=o.scrollLeft,s=!this.s.rtl,i=0!==e,o=o.scrollWidth>o.clientWidth+Math.abs(e)+1,t.toggleClass(this.classes.tableScrollingStart,i),t.toggleClass(this.classes.tableScrollingEnd,o),t.toggleClass(this.classes.tableScrollingLeft,i&&s||o&&!s),t.toggleClass(this.classes.tableScrollingRight,o&&s||i&&!s))},d.prototype._setKeyTableListener=function(){var c=this;this.s.dt.on("key-focus.dt.dtfc",function(t,e,s){var i,o,l,n=h(s.node()).offset(),d=c.dom.scroller[0],r=h(h(c.s.dt.table().node()).closest("div.dt-scroll-body"));0<c.c.start&&(l=(o=h(c.s.dt.column(c.c.start-1).header())).offset(),o=o.outerWidth(),h(s.node()).hasClass(c.classes.fixedLeft)?r.scrollLeft(0):n.left<l.left+o&&(i=r.scrollLeft(),r.scrollLeft(i-(l.left+o-n.left)))),0<c.c.end&&(l=c.s.dt.columns().data().toArray().length,o=h(s.node()).outerWidth(),l=h(c.s.dt.column(l-c.c.end).header()).offset(),h(s.node()).hasClass(c.classes.fixedRight)?r.scrollLeft(d.scrollWidth-d.clientWidth):n.left+o>l.left&&(i=r.scrollLeft(),r.scrollLeft(i-(l.left-(n.left+o)))))})},d.prototype._sum=function(t,e,s){return(t=(s=void 0===s?!1:s)?t.slice().reverse():t).slice(0,e).reduce(function(t,e){return t+e},0)},d.version="5.0.0",d.classes={bottomBlocker:"dtfc-bottom-blocker",fixedEnd:"dtfc-fixed-end",fixedLeft:"dtfc-fixed-left",fixedRight:"dtfc-fixed-right",fixedStart:"dtfc-fixed-start",tableFixedEnd:"dtfc-has-end",tableFixedLeft:"dtfc-has-left",tableFixedRight:"dtfc-has-right",tableFixedStart:"dtfc-has-start",tableScrollingEnd:"dtfc-scrolling-end",tableScrollingLeft:"dtfc-scrolling-left",tableScrollingRight:"dtfc-scrolling-right",tableScrollingStart:"dtfc-scrolling-start",topBlocker:"dtfc-top-blocker"},d.defaults={i18n:{button:"FixedColumns"},start:1,end:0},s=d,l=(h=o).fn.dataTable,o.fn.dataTable.FixedColumns=s,o.fn.DataTable.FixedColumns=s,(i=n.Api.register)("fixedColumns()",function(){return this}),i("fixedColumns().start()",function(t){var e=this.context[0];return void 0!==t?(e._fixedColumns.start(t),this):e._fixedColumns.start()}),i("fixedColumns().end()",function(t){var e=this.context[0];return void 0!==t?(e._fixedColumns.end(t),this):e._fixedColumns.end()}),i("fixedColumns().left()",function(t){var e=this.context[0];return void 0!==t?(e._fixedColumns.left(t),this):e._fixedColumns.left()}),i("fixedColumns().right()",function(t){var e=this.context[0];return void 0!==t?(e._fixedColumns.right(t),this):e._fixedColumns.right()}),n.ext.buttons.fixedColumns={action:function(t,e,s,i){o(s).attr("active")?(o(s).removeAttr("active").removeClass("active"),e.fixedColumns().start(0),e.fixedColumns().end(0)):(o(s).attr("active","true").addClass("active"),e.fixedColumns().start(i.config.start),e.fixedColumns().end(i.config.end))},config:{start:1,end:0},init:function(t,e,s){void 0===t.settings()[0]._fixedColumns&&r(t.settings(),s),o(e).attr("active","true").addClass("active"),t.button(e).text(s.text||t.i18n("buttons.fixedColumns",t.settings()[0]._fixedColumns.c.i18n.button))},text:null},o(e).on("plugin-init.dt",function(t,e){"dt"!==t.namespace||!e.oInit.fixedColumns&&!n.defaults.fixedColumns||e._fixedColumns||r(e,null)}),n});