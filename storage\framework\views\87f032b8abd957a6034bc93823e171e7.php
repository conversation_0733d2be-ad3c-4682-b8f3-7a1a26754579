<head>
    <title>Wedding Minigame System - Admin</title>
    <!-- [Meta] -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Wedding Minigame System - Admin Dashboard" />
    <meta name="keywords" content="Wedding, Minigame, Admin, Dashboard, Management" />
    <meta name="author" content="Wedding Minigame System" />
    <!-- [Favicon] icon -->
    <link rel="icon" href="<?php echo e(asset('favicon.png')); ?>" type="image/x-icon" />
    <!-- [Font] Family - Jakarta Sans -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/jakarta-sans.css" />
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/fonts/inter/inter.css" id="main-font-link" />
    <!-- [Tabler Icons] https://tablericons.com -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/fonts/tabler-icons.min.css" />
    <!-- [Feather Icons] https://feathericons.com -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/fonts/feather.css" />
    <!-- [Font Awesome Icons] https://fontawesome.com/icons -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/fonts/fontawesome.css" />
    <!-- [Material Icons] https://fonts.google.com/icons -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/fonts/material.css" />
    <!-- [Template CSS Files] -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/style.css" />
    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/tech-stack.js"></script>
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/style-preset.css" />
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/sweetalert')); ?>/sweetalert.css">
    <!-- End SweetAlert2 -->

    <!-- Wedding Theme Custom CSS -->
    <style>
        :root {
            --primary-pink: #ec4899;
            --secondary-pink: #f8d7da;
            --light-pink: #fdf2f8;
            --accent-pink: #be185d;
            --text-dark: #721c24;
            --text-light: #6b7280;
            --white: #ffffff;
            --shadow-pink: 0 8px 32px rgba(251, 207, 232, 0.2);
            --gradient-bg: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #f3e8ff 100%);
        }

        /* Override default fonts with Jakarta Sans */
        body,
        .pc-sidebar,
        .pc-header,
        .card,
        .btn,
        .form-control,
        .table {
            font-family: 'Jakarta Sans', sans-serif !important;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        .heading-font {
            font-family: 'Jakarta Sans', sans-serif !important;
            font-weight: 700;
        }

        /* Main background */
        body {
            background: var(--gradient-bg) !important;
            min-height: 100vh;
        }

        /* Sidebar styling */
        .pc-sidebar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-right: 2px solid rgba(251, 207, 232, 0.3) !important;
            box-shadow: var(--shadow-pink);
        }

        .pc-sidebar .pc-navbar {
            background: transparent !important;
        }

        .pc-sidebar .pc-navbar .pc-item .pc-link {
            color: var(--text-dark) !important;
            border-radius: 12px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }

        .pc-sidebar .pc-navbar .pc-item .pc-link:hover,
        .pc-sidebar .pc-navbar .pc-item .pc-link.active {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%) !important;
            color: white !important;
            transform: translateX(5px);
        }

        /* Header styling */
        .pc-header {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 2px solid rgba(251, 207, 232, 0.3) !important;
            box-shadow: var(--shadow-pink);
        }

        .pc-header .pc-h-item .dropdown-toggle {
            color: var(--text-dark) !important;
        }

        /* Card styling */
        .card {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(251, 207, 232, 0.3) !important;
            border-radius: 20px !important;
            box-shadow: var(--shadow-pink) !important;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(251, 207, 232, 0.3) !important;
        }

        .card-header {
            background: linear-gradient(135deg, var(--secondary-pink) 0%, #f5c6cb 100%) !important;
            border-bottom: 2px solid #e9c5c8 !important;
            border-radius: 18px 18px 0 0 !important;
            color: var(--text-dark) !important;
        }

        .card-title {
            color: var(--text-dark) !important;
            font-weight: 600;
        }

        /* Button styling */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%) !important;
            border: none !important;
            border-radius: 15px !important;
            color: white !important;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(236, 72, 153, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(236, 72, 153, 0.4) !important;
            background: linear-gradient(135deg, var(--accent-pink) 0%, var(--primary-pink) 100%) !important;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
            border: none !important;
            border-radius: 15px !important;
            color: white !important;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            border: none !important;
            border-radius: 15px !important;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
            border: none !important;
            border-radius: 15px !important;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            border: none !important;
            border-radius: 15px !important;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
            border: none !important;
            border-radius: 15px !important;
        }

        /* Form styling */
        .form-control {
            border: 2px solid rgba(251, 207, 232, 0.5) !important;
            border-radius: 12px !important;
            background: rgba(255, 255, 255, 0.8) !important;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-pink) !important;
            box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25) !important;
            background: rgba(255, 255, 255, 0.95) !important;
        }

        /* Table styling */
        .table {
            background: rgba(255, 255, 255, 0.9) !important;
            border-radius: 15px !important;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--secondary-pink) 0%, #f5c6cb 100%) !important;
            color: var(--text-dark) !important;
            font-weight: 600;
            border: none !important;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(253, 242, 248, 0.7) !important;
        }

        /* Badge styling */
        .badge {
            border-radius: 12px !important;
            font-weight: 500;
            padding: 6px 12px;
        }

        /* Alert styling */
        .alert {
            border-radius: 15px !important;
            border: none !important;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
            color: #155724 !important;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
            color: #721c24 !important;
        }

        /* Pagination styling */
        .pagination {
            justify-content: center;
            margin: 20px 0;
        }

        .pagination .page-item {
            margin: 0 3px;
        }

        .pagination .page-link {
            color: var(--primary-pink) !important;
            border: 2px solid rgba(251, 207, 232, 0.5) !important;
            border-radius: 12px !important;
            padding: 10px 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            min-width: 44px;
            text-align: center;
        }

        .pagination .page-link:hover {
            background: var(--light-pink) !important;
            border-color: var(--primary-pink) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(236, 72, 153, 0.2);
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%) !important;
            border-color: var(--primary-pink) !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(236, 72, 153, 0.3);
        }

        .pagination .page-item.disabled .page-link {
            color: var(--text-light) !important;
            background: rgba(255, 255, 255, 0.5) !important;
            border-color: rgba(251, 207, 232, 0.3) !important;
        }

        /* Mobile pagination adjustments */
        @media (max-width: 768px) {
            .pagination .page-link {
                padding: 8px 12px;
                font-size: 0.9rem;
                min-width: 40px;
            }

            .pagination .page-item {
                margin: 0 1px;
            }
        }

        /* Content area */
        .pc-content {
            background: transparent !important;
        }

        .pc-container {
            background: transparent !important;
        }

        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Mobile overlay */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: block;
            backdrop-filter: blur(4px);
            transition: opacity 0.3s ease;
        }

        .mobile-close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            color: var(--primary-pink);
            font-size: 1.2rem;
            z-index: 1001;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .mobile-close-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }

        /* Responsive adjustments */
        @media (max-width: 991.98px) {
            .pc-sidebar {
                position: fixed !important;
                left: -280px !important;
                top: 0 !important;
                height: 100vh !important;
                width: 280px !important;
                z-index: 1000 !important;
                transition: left 0.3s ease !important;
            }

            .pc-sidebar.pc-sidebar-show {
                left: 0 !important;
            }

            .pc-container {
                margin-left: 0 !important;
            }

            .pc-header {
                left: 0 !important;
                width: 100% !important;
            }
        }

        @media (max-width: 768px) {
            .card {
                margin: 10px;
                border-radius: 15px !important;
            }

            .btn {
                border-radius: 12px !important;
            }

            .pc-header .pc-h-item {
                margin: 0 5px;
            }

            .pc-header .pc-h-item .dropdown-menu {
                right: 0 !important;
                left: auto !important;
                min-width: 200px;
            }

            /* Touch targets for mobile */
            .pc-navbar .pc-item .pc-link {
                min-height: 44px;
                display: flex;
                align-items: center;
                padding: 12px 16px;
            }

            .btn {
                min-height: 44px;
                padding: 12px 16px;
            }

            /* Mobile header adjustments */
            .pc-header .pc-head-link {
                min-height: 44px;
                display: flex;
                align-items: center;
                padding: 8px 12px;
            }
        }

        @media (max-width: 480px) {
            .stats-card .card-body {
                padding: 15px;
            }

            .stats-icon {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .quick-action-btn {
                height: 80px;
                font-size: 0.9rem;
            }

            .quick-action-btn i {
                font-size: 1.2rem;
            }
        }
    </style>

    <!-- Enhanced Mobile Navigation JavaScript -->
    <script>
        function toggleAdminSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-overlay');
            const body = document.body;

            if (sidebar) {
                const isShowing = sidebar.classList.contains('pc-sidebar-show');

                if (isShowing) {
                    // Hide sidebar
                    sidebar.classList.remove('pc-sidebar-show');
                    body.style.overflow = '';
                    if (overlay) {
                        overlay.remove();
                    }
                } else {
                    // Show sidebar
                    sidebar.classList.add('pc-sidebar-show');
                    body.style.overflow = 'hidden'; // Prevent background scrolling

                    // Create overlay
                    const mobileOverlay = document.createElement('div');
                    mobileOverlay.id = 'mobile-overlay';
                    mobileOverlay.className = 'mobile-overlay';
                    mobileOverlay.onclick = toggleAdminSidebar;
                    body.appendChild(mobileOverlay);
                }
            }
        }

        // Enhanced mobile navigation handling
        document.addEventListener('DOMContentLoaded', function() {
            // Handle existing sidebar toggle buttons
            const sidebarHide = document.getElementById('sidebar-hide');
            const mobileCollapse = document.getElementById('mobile-collapse');

            if (sidebarHide) {
                sidebarHide.addEventListener('click', function(e) {
                    e.preventDefault();
                    toggleAdminSidebar();
                });
            }

            if (mobileCollapse) {
                mobileCollapse.addEventListener('click', function(e) {
                    e.preventDefault();
                    toggleAdminSidebar();
                });
            }

            // Close sidebar on window resize if mobile
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 992) {
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('mobile-overlay');

                    if (sidebar && sidebar.classList.contains('pc-sidebar-show')) {
                        sidebar.classList.remove('pc-sidebar-show');
                        document.body.style.overflow = '';
                        if (overlay) {
                            overlay.remove();
                        }
                    }
                }
            });

            // Handle touch events for better mobile experience
            let touchStartX = 0;
            let touchEndX = 0;

            document.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            });

            document.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });

            function handleSwipe() {
                const sidebar = document.getElementById('sidebar');
                if (!sidebar) return;

                const swipeThreshold = 50;
                const swipeDistance = touchEndX - touchStartX;

                // Swipe right to open sidebar (only if starting from left edge)
                if (swipeDistance > swipeThreshold && touchStartX < 50 && window.innerWidth < 992) {
                    if (!sidebar.classList.contains('pc-sidebar-show')) {
                        toggleAdminSidebar();
                    }
                }

                // Swipe left to close sidebar
                if (swipeDistance < -swipeThreshold && sidebar.classList.contains('pc-sidebar-show')) {
                    toggleAdminSidebar();
                }
            }
        });
    </script>

    <style>
        /* Enhanced Mobile Navigation Styles */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: block;
            backdrop-filter: blur(4px);
            transition: opacity 0.3s ease;
        }

        @media (min-width: 992px) {
            .mobile-overlay {
                display: none !important;
            }
        }

        .mobile-close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            color: var(--primary-pink);
            font-size: 1.2rem;
            z-index: 1001;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .mobile-close-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }

        /* Sidebar responsive improvements */
        @media (max-width: 991.98px) {
            .pc-sidebar {
                position: fixed !important;
                left: -280px !important;
                top: 0 !important;
                height: 100vh !important;
                width: 280px !important;
                z-index: 1000 !important;
                transition: left 0.3s ease !important;
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(20px) !important;
                border-right: 2px solid rgba(251, 207, 232, 0.3) !important;
            }

            .pc-sidebar.pc-sidebar-show {
                left: 0 !important;
            }

            .pc-container {
                margin-left: 0 !important;
            }

            .pc-header {
                left: 0 !important;
                width: 100% !important;
            }

            /* Touch-friendly navigation items */
            .pc-navbar .pc-item .pc-link {
                padding: 15px 20px !important;
                min-height: 44px !important;
                display: flex !important;
                align-items: center !important;
                font-size: 16px !important;
            }

            .pc-navbar .pc-item .pc-micon {
                width: 44px !important;
                height: 44px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                margin-right: 12px !important;
            }

            .pc-navbar .pc-item .pc-micon i {
                font-size: 18px !important;
            }

            /* User card mobile adjustments */
            .pc-user-card {
                margin: 15px !important;
                border-radius: 15px !important;
            }

            .pc-user-card .card-body {
                padding: 15px !important;
            }
        }

        /* Header mobile improvements */
        @media (max-width: 767.98px) {
            .pc-header .header-wrapper {
                padding: 0 15px !important;
            }

            .pc-header .pc-head-link {
                min-width: 44px !important;
                min-height: 44px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .dropdown-menu {
                position: absolute !important;
                right: 0 !important;
                left: auto !important;
                min-width: 200px !important;
                margin-top: 5px !important;
                border-radius: 12px !important;
                border: 2px solid rgba(251, 207, 232, 0.3) !important;
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(20px) !important;
            }

            .dropdown-item {
                padding: 12px 20px !important;
                font-size: 14px !important;
                min-height: 44px !important;
                display: flex !important;
                align-items: center !important;
            }
        }
    </style>
</head><?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/admin/partials/head.blade.php ENDPATH**/ ?>