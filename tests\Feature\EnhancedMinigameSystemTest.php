<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\Guest;
use App\Models\TriviaQuestion;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EnhancedMinigameSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $guest;
    protected $gameConfig;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'client',
            'email' => '<EMAIL>',
        ]);

        // Create test client
        $this->client = Client::factory()->create([
            'user_id' => $this->user->id,
            'client_name' => 'Test Wedding',
            'unique_url' => 'test-wedding-123',
            'is_have_minigame' => true,
        ]);

        // Create test guest
        $this->guest = Guest::factory()->create([
            'client_id' => $this->client->id,
            'name' => 'John Doe',
            'unique_url' => 'john-doe-456',
        ]);

        // Create trivia game
        $game = Game::factory()->create([
            'minigame_name' => 'trivia',
            'display_name' => 'Trivia Game',
        ]);

        // Create game config
        $this->gameConfig = GameConfig::factory()->create([
            'client_id' => $this->client->id,
            'game_id' => $game->id,
            'game_title' => 'Wedding Trivia',
            'is_starting' => true,
        ]);

        // Create test questions
        TriviaQuestion::factory()->count(3)->create([
            'game_config_id' => $this->gameConfig->id,
        ]);
    }

    /** @test */
    public function guest_can_access_trivia_with_new_url_structure()
    {
        $response = $this->get("/game/trivia/{$this->client->unique_url}/{$this->guest->unique_url}");

        $response->assertStatus(200);
        $response->assertViewIs('minigame.trivia.index');
        $response->assertViewHas('client', $this->client);
        $response->assertViewHas('guest', $this->guest);
    }

    /** @test */
    public function guest_cannot_access_trivia_with_invalid_urls()
    {
        // Invalid client URL
        $response = $this->get("/game/trivia/invalid-client/{$this->guest->unique_url}");
        $response->assertStatus(404);

        // Invalid guest URL
        $response = $this->get("/game/trivia/{$this->client->unique_url}/invalid-guest");
        $response->assertStatus(404);
    }

    /** @test */
    public function guest_cannot_access_trivia_from_different_client()
    {
        // Create another client and guest
        $otherClient = Client::factory()->create([
            'unique_url' => 'other-client-789',
            'is_have_minigame' => true,
        ]);
        
        $otherGuest = Guest::factory()->create([
            'client_id' => $otherClient->id,
            'unique_url' => 'other-guest-789',
        ]);

        // Try to access with mismatched client/guest
        $response = $this->get("/game/trivia/{$this->client->unique_url}/{$otherGuest->unique_url}");
        $response->assertStatus(404);
    }

    /** @test */
    public function client_can_manage_questions()
    {
        $this->actingAs($this->user);

        // Access questions management page
        $response = $this->get("/client/games/{$this->gameConfig->id}/questions");
        $response->assertStatus(200);
        $response->assertViewIs('client.dashboard.questions');

        // Add a new question
        $questionData = [
            'question' => 'What is the bride\'s favorite color?',
            'options' => ['Red', 'Blue', 'Green', 'Pink'],
            'correct_answer' => 3,
            'points' => 100,
            'time_limit' => 30,
        ];

        $response = $this->post("/client/games/{$this->gameConfig->id}/questions", $questionData);
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('trivia_questions', [
            'game_config_id' => $this->gameConfig->id,
            'question' => 'What is the bride\'s favorite color?',
        ]);
    }

    /** @test */
    public function client_can_update_game_configuration()
    {
        $this->actingAs($this->user);

        $configData = [
            'game_title' => 'Updated Wedding Trivia',
            'top_players' => 15,
            'config_data' => [
                'default_time_limit' => 45,
                'default_points' => 150,
                'randomize_questions' => true,
            ],
        ];

        $response = $this->put("/client/games/{$this->gameConfig->id}/config", $configData);
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->gameConfig->refresh();
        $this->assertEquals('Updated Wedding Trivia', $this->gameConfig->game_title);
        $this->assertEquals(15, $this->gameConfig->top_players);
        $this->assertEquals(45, $this->gameConfig->config_data['default_time_limit']);
    }

    /** @test */
    public function guest_can_submit_trivia_answers_with_new_structure()
    {
        $questions = TriviaQuestion::where('game_config_id', $this->gameConfig->id)->get();
        
        $answers = [];
        foreach ($questions as $question) {
            $answers[$question->id] = $question->correct_answer;
        }

        $response = $this->post("/game/trivia/{$this->client->unique_url}/{$this->guest->unique_url}/submit", [
            'answers' => $answers,
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('guest_scores', [
            'client_id' => $this->client->id,
            'guest_id' => $this->guest->id,
            'game_config_id' => $this->gameConfig->id,
            'is_completed' => true,
        ]);
    }

    /** @test */
    public function game_cannot_start_without_questions()
    {
        $this->actingAs($this->user);

        // Remove all questions
        TriviaQuestion::where('game_config_id', $this->gameConfig->id)->delete();

        $response = $this->post("/client/games/{$this->gameConfig->id}/start");
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    /** @test */
    public function unauthorized_client_cannot_access_other_client_resources()
    {
        // Create another user and client
        $otherUser = User::factory()->create(['role' => 'client']);
        $otherClient = Client::factory()->create(['user_id' => $otherUser->id]);

        $this->actingAs($this->user);

        // Try to access other client's game config
        $response = $this->get("/client/games/{$otherClient->gameConfigs->first()->id ?? 999}/questions");
        $response->assertStatus(403);
    }
}
