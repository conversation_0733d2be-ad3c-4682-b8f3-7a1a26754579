@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-question-circle me-2"></i>Trivia Question Details
                </h5>
                <small class="text-muted">View trivia question information</small>
            </div>
            <div class="card-body">
                <!-- Question Information -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-info-circle me-2"></i>Question Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Game Configuration:</label>
                                    <div class="d-flex align-items-center">
                                        <div class="client-avatar me-2">
                                            {{ substr($triviaQuestion->gameConfig->client->client_name, 0, 1) }}
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $triviaQuestion->gameConfig->client->client_name }}</h6>
                                            <small class="text-muted">{{ $triviaQuestion->gameConfig->game_title }}</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label fw-bold">Question Text:</label>
                                    <div class="question-display">
                                        {{ $triviaQuestion->question_text }}
                                    </div>
                                </div>

                                @if($triviaQuestion->question_image)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Question Image:</label>
                                    <div class="question-image">
                                        <img src="{{ Storage::url($triviaQuestion->question_image) }}" 
                                             alt="Question image" class="img-fluid rounded" style="max-height: 300px;">
                                    </div>
                                </div>
                                @endif

                                <div class="mb-3">
                                    <label class="form-label fw-bold">Correct Answer:</label>
                                    <div class="correct-answer">
                                        <span class="badge bg-success fs-6 p-2">{{ $triviaQuestion->correct_answer }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- Question Options -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-list me-2"></i>Answer Options
                                </h6>
                            </div>
                            <div class="card-body">
                                @if($triviaQuestion->option_a || $triviaQuestion->option_b || $triviaQuestion->option_c || $triviaQuestion->option_d)
                                    <div class="options-list">
                                        @if($triviaQuestion->option_a)
                                            <div class="option-item mb-2 {{ $triviaQuestion->correct_answer === $triviaQuestion->option_a ? 'correct-option' : '' }}">
                                                <span class="option-label">A</span>
                                                <span class="option-text">{{ $triviaQuestion->option_a }}</span>
                                                @if($triviaQuestion->correct_answer === $triviaQuestion->option_a)
                                                    <i class="fas fa-check-circle text-success ms-2"></i>
                                                @endif
                                            </div>
                                        @endif

                                        @if($triviaQuestion->option_b)
                                            <div class="option-item mb-2 {{ $triviaQuestion->correct_answer === $triviaQuestion->option_b ? 'correct-option' : '' }}">
                                                <span class="option-label">B</span>
                                                <span class="option-text">{{ $triviaQuestion->option_b }}</span>
                                                @if($triviaQuestion->correct_answer === $triviaQuestion->option_b)
                                                    <i class="fas fa-check-circle text-success ms-2"></i>
                                                @endif
                                            </div>
                                        @endif

                                        @if($triviaQuestion->option_c)
                                            <div class="option-item mb-2 {{ $triviaQuestion->correct_answer === $triviaQuestion->option_c ? 'correct-option' : '' }}">
                                                <span class="option-label">C</span>
                                                <span class="option-text">{{ $triviaQuestion->option_c }}</span>
                                                @if($triviaQuestion->correct_answer === $triviaQuestion->option_c)
                                                    <i class="fas fa-check-circle text-success ms-2"></i>
                                                @endif
                                            </div>
                                        @endif

                                        @if($triviaQuestion->option_d)
                                            <div class="option-item mb-2 {{ $triviaQuestion->correct_answer === $triviaQuestion->option_d ? 'correct-option' : '' }}">
                                                <span class="option-label">D</span>
                                                <span class="option-text">{{ $triviaQuestion->option_d }}</span>
                                                @if($triviaQuestion->correct_answer === $triviaQuestion->option_d)
                                                    <i class="fas fa-check-circle text-success ms-2"></i>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                @else
                                    <div class="text-center py-3">
                                        <i class="fas fa-edit fa-2x text-muted mb-2"></i>
                                        <p class="text-muted mb-0">Open-ended Question</p>
                                        <small>No multiple choice options</small>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Question Statistics -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-chart-bar me-2"></i>Question Statistics
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Created:</span>
                                        <span class="fw-bold">{{ $triviaQuestion->created_at->format('M d, Y') }}</span>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Last Updated:</span>
                                        <span class="fw-bold">{{ $triviaQuestion->updated_at->format('M d, Y') }}</span>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Question Type:</span>
                                        <span class="fw-bold">
                                            @if($triviaQuestion->option_a || $triviaQuestion->option_b || $triviaQuestion->option_c || $triviaQuestion->option_d)
                                                Multiple Choice
                                            @else
                                                Open-ended
                                            @endif
                                        </span>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Has Image:</span>
                                        <span class="fw-bold">
                                            @if($triviaQuestion->question_image)
                                                <i class="fas fa-check text-success"></i> Yes
                                            @else
                                                <i class="fas fa-times text-muted"></i> No
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('admin.trivia-questions.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Questions
                    </a>
                    <div>
                        <a href="{{ route('admin.trivia-questions.edit', $triviaQuestion) }}" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i>Edit Question
                        </a>
                        <form action="{{ route('admin.trivia-questions.destroy', $triviaQuestion) }}" method="POST" class="d-inline" 
                              onsubmit="return confirm('Are you sure you want to delete this question?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Delete Question
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Question display styles */
.question-display {
    background: rgba(251, 207, 232, 0.1);
    border: 2px solid rgba(251, 207, 232, 0.3);
    border-radius: 12px;
    padding: 20px;
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-dark);
}

.client-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--secondary-pink) 0%, #f5c6cb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    font-weight: 600;
    font-size: 16px;
}

.correct-answer .badge {
    font-size: 1rem;
    padding: 8px 16px;
}

/* Options styling */
.option-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 2px solid rgba(251, 207, 232, 0.3);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.option-item.correct-option {
    background: rgba(40, 167, 69, 0.1);
    border-color: rgba(40, 167, 69, 0.3);
}

.option-label {
    background: var(--primary-pink);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
    margin-right: 12px;
    flex-shrink: 0;
}

.option-text {
    flex: 1;
    font-weight: 500;
}

/* Statistics styling */
.stat-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(251, 207, 232, 0.2);
}

.stat-item:last-child {
    border-bottom: none;
}

/* Question image styling */
.question-image img {
    border: 2px solid rgba(251, 207, 232, 0.3);
    border-radius: 12px;
}
</style>
@endsection
