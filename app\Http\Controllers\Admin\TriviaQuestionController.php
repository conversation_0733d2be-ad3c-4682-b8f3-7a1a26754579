<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GameConfig;
use App\Models\TriviaQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TriviaQuestionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'admin') {
                abort(403, 'Access Denied. Only admin role is allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $gameConfigId = $request->get('game_config_id');
        $query = TriviaQuestion::with('gameConfig.client', 'gameConfig.game');

        if ($gameConfigId) {
            $query->where('game_config_id', $gameConfigId);
        }

        $questions = $query->orderBy('order')->paginate(15);
        $gameConfigs = GameConfig::with(['client', 'game'])->get();

        return view('admin.trivia-questions.index', compact('questions', 'gameConfigs', 'gameConfigId'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $gameConfigId = $request->get('game_config_id');
        $gameConfigs = GameConfig::with(['client', 'game'])->get();

        return view('admin.trivia-questions.create', compact('gameConfigs', 'gameConfigId'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'game_config_id' => 'required|exists:game_configs,id',
            'question_text' => 'required|string|max:1000',
            'option_a' => 'nullable|string|max:255',
            'option_b' => 'nullable|string|max:255',
            'option_c' => 'nullable|string|max:255',
            'option_d' => 'nullable|string|max:255',
            'correct_answer' => 'required|string|max:255',
            'question_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only([
            'game_config_id', 'question_text', 'option_a', 'option_b',
            'option_c', 'option_d', 'correct_answer'
        ]);

        // Handle image upload
        if ($request->hasFile('question_image')) {
            $imagePath = $request->file('question_image')->store('trivia-questions', 'public');
            $data['question_image'] = $imagePath;
        }

        TriviaQuestion::create($data);

        alert()->success('Success', 'Trivia question created successfully!');
        return redirect()->route('admin.trivia-questions.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(TriviaQuestion $triviaQuestion)
    {
        $triviaQuestion->load('gameConfig.client', 'gameConfig.game');

        return view('admin.trivia-questions.show', compact('triviaQuestion'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TriviaQuestion $triviaQuestion)
    {
        $gameConfigs = GameConfig::with(['client', 'game'])->get();

        return view('admin.trivia-questions.edit', compact('triviaQuestion', 'gameConfigs'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TriviaQuestion $triviaQuestion)
    {
        $request->validate([
            'game_config_id' => 'required|exists:game_configs,id',
            'question_text' => 'required|string|max:1000',
            'option_a' => 'nullable|string|max:255',
            'option_b' => 'nullable|string|max:255',
            'option_c' => 'nullable|string|max:255',
            'option_d' => 'nullable|string|max:255',
            'correct_answer' => 'required|string|max:255',
            'question_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only([
            'game_config_id', 'question_text', 'option_a', 'option_b',
            'option_c', 'option_d', 'correct_answer'
        ]);

        // Handle image upload
        if ($request->hasFile('question_image')) {
            // Delete old image if exists
            if ($triviaQuestion->question_image) {
                \Storage::disk('public')->delete($triviaQuestion->question_image);
            }

            $imagePath = $request->file('question_image')->store('trivia-questions', 'public');
            $data['question_image'] = $imagePath;
        }

        $triviaQuestion->update($data);

        alert()->success('Success', 'Trivia question updated successfully!');
        return redirect()->route('admin.trivia-questions.show', $triviaQuestion);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TriviaQuestion $triviaQuestion)
    {
        $gameConfigId = $triviaQuestion->game_config_id;
        $triviaQuestion->delete();

        alert()->success('Success', 'Trivia question deleted successfully!');
        return redirect()->route('admin.trivia-questions.index', ['game_config_id' => $gameConfigId]);
    }
}
