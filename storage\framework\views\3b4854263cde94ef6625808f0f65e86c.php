<?php $__env->startSection('content'); ?>
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-plus-circle me-2"></i>Create New Trivia Question
                </h5>
                <small class="text-muted">Add a new trivia question to the system</small>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('admin.trivia-questions.store')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    
                    <!-- Game Configuration Selection -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="game_config_id" class="form-label">Game Configuration <span class="text-danger">*</span></label>
                            <select class="form-control <?php $__errorArgs = ['game_config_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="game_config_id" name="game_config_id" required>
                                <option value="">Select Game Configuration</option>
                                <?php $__currentLoopData = $gameConfigs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $config): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($config->id); ?>" <?php echo e(old('game_config_id') == $config->id ? 'selected' : ''); ?>>
                                        <?php echo e($config->client->client_name); ?> - <?php echo e($config->game_title); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <small class="form-text text-muted">Choose which game configuration this question belongs to</small>
                            <?php $__errorArgs = ['game_config_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="question_image" class="form-label">Question Image (Optional)</label>
                            <input type="file" class="form-control <?php $__errorArgs = ['question_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="question_image" name="question_image" accept="image/*">
                            <small class="form-text text-muted">Upload an image to accompany the question (JPG, PNG, GIF)</small>
                            <?php $__errorArgs = ['question_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Question Text -->
                    <div class="mb-3">
                        <label for="question_text" class="form-label">Question Text <span class="text-danger">*</span></label>
                        <textarea class="form-control <?php $__errorArgs = ['question_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="question_text" name="question_text" rows="3" 
                                  placeholder="Enter your trivia question here..." required><?php echo e(old('question_text')); ?></textarea>
                        <small class="form-text text-muted">Write a clear and engaging trivia question</small>
                        <?php $__errorArgs = ['question_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Multiple Choice Options -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0 heading-font">
                                <i class="fas fa-list me-2"></i>Multiple Choice Options
                            </h6>
                            <small class="text-muted">Leave options blank for open-ended questions</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="option_a" class="form-label">Option A</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['option_a'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="option_a" name="option_a" value="<?php echo e(old('option_a')); ?>" 
                                           placeholder="First option">
                                    <?php $__errorArgs = ['option_a'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="option_b" class="form-label">Option B</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['option_b'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="option_b" name="option_b" value="<?php echo e(old('option_b')); ?>" 
                                           placeholder="Second option">
                                    <?php $__errorArgs = ['option_b'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="option_c" class="form-label">Option C</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['option_c'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="option_c" name="option_c" value="<?php echo e(old('option_c')); ?>" 
                                           placeholder="Third option">
                                    <?php $__errorArgs = ['option_c'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="option_d" class="form-label">Option D</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['option_d'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="option_d" name="option_d" value="<?php echo e(old('option_d')); ?>" 
                                           placeholder="Fourth option">
                                    <?php $__errorArgs = ['option_d'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Correct Answer -->
                    <div class="mb-3">
                        <label for="correct_answer" class="form-label">Correct Answer <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['correct_answer'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="correct_answer" name="correct_answer" value="<?php echo e(old('correct_answer')); ?>" 
                               placeholder="Enter the correct answer" required>
                        <small class="form-text text-muted">
                            For multiple choice: enter the exact text of the correct option<br>
                            For open-ended: enter the expected answer (case-insensitive matching)
                        </small>
                        <?php $__errorArgs = ['correct_answer'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Guidelines Card -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0 heading-font">
                                <i class="fas fa-lightbulb me-2"></i>Question Guidelines
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Best Practices:</h6>
                                    <ul class="small text-muted">
                                        <li>Keep questions clear and concise</li>
                                        <li>Avoid ambiguous wording</li>
                                        <li>Make questions engaging and fun</li>
                                        <li>Test difficulty appropriate for guests</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">Multiple Choice Tips:</h6>
                                    <ul class="small text-muted">
                                        <li>Provide 2-4 plausible options</li>
                                        <li>Avoid "all of the above" options</li>
                                        <li>Keep options similar in length</li>
                                        <li>Only one clearly correct answer</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo e(route('admin.trivia-questions.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Questions
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Create Question
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
/* Form specific styles */
.form-check-input:checked {
    background-color: var(--primary-pink) !important;
    border-color: var(--primary-pink) !important;
}

.form-check-input:focus {
    border-color: var(--primary-pink) !important;
    box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25) !important;
}

.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    display: block;
}

/* File input styling */
input[type="file"] {
    padding: 8px 12px;
}

/* Option inputs styling */
.card .form-control {
    transition: all 0.3s ease;
}

.card .form-control:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(236, 72, 153, 0.15);
}
</style>

<script>
// Auto-fill correct answer when option is selected
document.addEventListener('DOMContentLoaded', function() {
    const options = ['option_a', 'option_b', 'option_c', 'option_d'];
    const correctAnswerField = document.getElementById('correct_answer');
    
    options.forEach(optionId => {
        const optionField = document.getElementById(optionId);
        if (optionField) {
            optionField.addEventListener('blur', function() {
                if (this.value && !correctAnswerField.value) {
                    // Suggest this as correct answer if none is set
                    correctAnswerField.placeholder = `Suggestion: ${this.value}`;
                }
            });
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/admin/trivia-questions/create.blade.php ENDPATH**/ ?>