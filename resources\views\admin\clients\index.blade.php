@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-users me-2"></i>Client Management
                </h5>
                <a href="{{ route('admin.clients.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>Add New Client
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Client Name</th>
                                <th>Unique URL</th>
                                <th>Guests Count</th>
                                <th>Minigame Status</th>
                                <th>Active Games</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($clients as $client)
                            <tr>
                                <td>{{ $loop->iteration + ($clients->currentPage() - 1) * $clients->perPage() }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2">
                                            <span>{{ substr($client->client_name, 0, 1) }}</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $client->client_name }}</h6>
                                            <small class="text-muted">{{ $client->user->name ?? 'N/A' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <code class="client-url">{{ $client->unique_url }}</code>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        {{ $client->guests->count() }} guests
                                    </span>
                                </td>
                                <td>
                                    @if($client->is_have_minigame)
                                    <span class="badge bg-success">
                                        <i class="fas fa-gamepad me-1"></i>Enabled
                                    </span>
                                    @else
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-times me-1"></i>Disabled
                                    </span>
                                    @endif
                                </td>
                                <td>
                                    @if($client->gameConfigs->count() > 0)
                                    <div class="d-flex flex-wrap gap-1">
                                        @foreach($client->gameConfigs as $config)
                                        <span class="badge {{ $config->is_starting ? 'bg-success' : 'bg-secondary' }}"
                                            style="font-size: 10px;">
                                            {{ $config->game->display_name }}
                                        </span>
                                        @endforeach
                                    </div>
                                    @else
                                    <span class="text-muted">No games configured</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.clients.show', $client) }}"
                                            class="btn btn-sm btn-outline-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.clients.edit', $client) }}"
                                            class="btn btn-sm btn-outline-warning" title="Edit Client">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ route('admin.clients.minigame-settings', $client) }}"
                                            class="btn btn-primary btn-sm" title="Minigame Settings">
                                            <i class="fas fa-gamepad"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div style="color: #6c757d;">
                                        <i class="fas fa-users fa-3x mb-3" style="opacity: 0.3;"></i>
                                        <p class="mb-0">No clients found</p>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                @if($clients->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $clients->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
    /* Additional custom styles for client management */
    .avatar {
        background: linear-gradient(135deg, var(--secondary-pink) 0%, #f5c6cb 100%);
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .avatar span {
        color: var(--text-dark);
        font-weight: 600;
        font-size: 14px;
    }

    .avatar:hover {
        transform: scale(1.1);
    }

    .client-url {
        background: var(--secondary-pink);
        color: var(--text-dark);
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 0.875rem;
    }

    .btn-group .btn {
        margin-right: 2px;
    }
</style>
@endsection