@extends('admin.layout.master')

@section('content')
<div class="fade-in-up">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-4">
                    <h1 class="heading-font mb-2" style="color: var(--primary-pink);">
                        <i class="fas fa-heart me-2"></i>Wedding Minigame Dashboard
                    </h1>
                    <p class="text-muted mb-0">Welcome back, {{ Auth::user()->name }}! Here's your wedding minigame
                        system overview.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary">
                            <i class="fas fa-user-friends"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="heading-font mb-0">{{ number_format($totalClients) }}</h3>
                            <p class="text-muted mb-0">Total Clients</p>
                            <small class="text-success">
                                <i class="fas fa-gamepad me-1"></i>{{ $clientsWithMinigames }} with minigames
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="heading-font mb-0">{{ $activeGameConfigs }}</h3>
                            <p class="text-muted mb-0">Active Games</p>
                            <small class="text-info">
                                <i class="fas fa-cogs me-1"></i>{{ $totalGameConfigs }} total configs
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="heading-font mb-0">{{ number_format($totalGuests) }}</h3>
                            <p class="text-muted mb-0">Total Guests</p>
                            <small class="text-primary">
                                <i class="fas fa-trophy me-1"></i>{{ $guestsWithScores }} participated
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="heading-font mb-0">{{ number_format($averageScore, 1) }}</h3>
                            <p class="text-muted mb-0">Average Score</p>
                            <small class="text-success">
                                <i class="fas fa-play me-1"></i>{{ $totalGameSessions }} sessions
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="heading-font mb-0">
                        <i class="fas fa-chart-area me-2"></i>Monthly Activity Overview
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="heading-font mb-0">
                        <i class="fas fa-percentage me-2"></i>Minigame Adoption
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="adoption-circle">
                        <span class="percentage">{{ $minigameParticipationRate }}%</span>
                    </div>
                    <p class="mt-3 mb-0">{{ $clientsWithMinigames }} of {{ $totalClients }} clients have minigames
                        enabled</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Top Games -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="heading-font mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Activity
                    </h5>
                    <small class="text-muted">Last 7 days</small>
                </div>
                <div class="card-body">
                    @forelse($recentGameSessions as $session)
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <div class="activity-content">
                            <p class="mb-1">
                                <strong>{{ $session->guest->name }}</strong>
                                completed {{ $session->gameConfig->game->display_name }}
                            </p>
                            <small class="text-muted">
                                Score: {{ $session->score }} • {{ $session->created_at->diffForHumans() }}
                            </small>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No recent activity</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="heading-font mb-0">
                        <i class="fas fa-trophy me-2"></i>Top Performing Games
                    </h5>
                </div>
                <div class="card-body">
                    @forelse($topGames as $gameConfig)
                    <div class="top-game-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ $gameConfig->game->display_name }}</h6>
                                <small class="text-muted">{{ $gameConfig->game_title }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary">{{ $gameConfig->guest_scores_count }} plays</span>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-gamepad fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No games played yet</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="heading-font mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.clients.create') }}"
                                class="btn btn-primary w-100 quick-action-btn">
                                <i class="fas fa-plus-circle mb-2"></i>
                                <div>Add New Client</div>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.games.index') }}" class="btn btn-success w-100 quick-action-btn">
                                <i class="fas fa-gamepad mb-2"></i>
                                <div>Manage Games</div>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.trivia-questions.index') }}"
                                class="btn btn-warning w-100 quick-action-btn">
                                <i class="fas fa-question-circle mb-2"></i>
                                <div>Trivia Questions</div>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.games.statistics') }}" class="btn btn-info w-100 quick-action-btn">
                                <i class="fas fa-chart-bar mb-2"></i>
                                <div>View Statistics</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Monthly Activity Chart
const monthlyData = @json($monthlyStats);
const ctx = document.getElementById('monthlyChart').getContext('2d');

new Chart(ctx, {
    type: 'line',
    data: {
        labels: monthlyData.map(item => item.month),
        datasets: [{
            label: 'New Clients',
            data: monthlyData.map(item => item.clients),
            borderColor: 'var(--primary-pink)',
            backgroundColor: 'rgba(236, 72, 153, 0.1)',
            tension: 0.4
        }, {
            label: 'Games Played',
            data: monthlyData.map(item => item.games_played),
            borderColor: 'var(--accent-pink)',
            backgroundColor: 'rgba(190, 24, 93, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>

<style>
    /* Dashboard specific styles */
    .stats-card {
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid rgba(251, 207, 232, 0.3);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 15px;
    }

    .top-game-item {
        padding: 12px 0;
        border-bottom: 1px solid rgba(251, 207, 232, 0.3);
    }

    .top-game-item:last-child {
        border-bottom: none;
    }

    .adoption-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;

        background: conic-gradient(var(--primary-pink) {
                    {
                    $minigameParticipationRate * 3.6
                }
            }

            deg, var(--light-pink) 0deg);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        position: relative;
    }

    .adoption-circle::before {
        content: '';
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: white;
        position: absolute;
    }

    .percentage {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-pink);
        z-index: 1;
    }

    .quick-action-btn {
        height: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .quick-action-btn:hover {
        transform: translateY(-3px);
    }

    .quick-action-btn i {
        font-size: 1.5rem;
    }
</style>
@endsection