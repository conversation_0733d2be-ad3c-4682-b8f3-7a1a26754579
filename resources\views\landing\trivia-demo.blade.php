@extends('landing.layout')

@section('title', 'Trivia Demo - Pecatu Minigame')

@section('styles')
<style>
    .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }

    .demo-header {
        text-align: center;
        margin-bottom: 3rem;
        padding: 2rem;
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        color: white;
        border-radius: 20px;
    }

    .demo-badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .question-card {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
    }

    .question-number {
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .question-text {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 1.5rem;
        line-height: 1.4;
    }

    .option-btn {
        width: 100%;
        padding: 1rem;
        margin-bottom: 0.75rem;
        border: 2px solid rgba(251, 207, 232, 0.5);
        background: rgba(255, 255, 255, 0.8);
        border-radius: 15px;
        text-align: left;
        font-weight: 500;
        color: var(--text-dark);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .option-btn:hover {
        border-color: var(--primary-pink);
        background: rgba(236, 72, 153, 0.1);
        transform: translateX(5px);
    }

    .option-btn.selected {
        border-color: var(--primary-pink);
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        color: white;
    }

    .option-letter {
        display: inline-block;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: var(--primary-pink);
        color: white;
        text-align: center;
        line-height: 30px;
        font-weight: 600;
        margin-right: 1rem;
    }

    .option-btn.selected .option-letter {
        background: rgba(255, 255, 255, 0.3);
    }

    .player-input-section {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .form-control {
        border: 2px solid rgba(251, 207, 232, 0.5);
        border-radius: 15px;
        padding: 1rem;
        font-size: 1.1rem;
        text-align: center;
    }

    .form-control:focus {
        border-color: var(--primary-pink);
        box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25);
    }

    .submit-btn {
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        border: none;
        color: white;
        padding: 1rem 3rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        width: 100%;
        max-width: 300px;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
    }

    .submit-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .progress-bar {
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        height: 8px;
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .back-link {
        color: var(--primary-pink);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        color: var(--accent-pink);
        text-decoration: none;
    }
</style>
@endsection

@section('content')
<div class="container py-5">
    <div class="demo-container">
        <!-- Demo Header -->
        <div class="demo-header">
            <div class="demo-badge">
                <i class="fas fa-play me-2"></i>DEMO MODE
            </div>
            <h1 style="margin-bottom: 0.5rem;">Wedding Trivia Demo</h1>
            <p style="margin-bottom: 0; opacity: 0.9;">Experience our interactive trivia game with sample wedding questions</p>
        </div>

        <!-- Player Name Input -->
        <div class="player-input-section">
            <h3 style="color: var(--text-dark); margin-bottom: 1rem;">Enter Your Name</h3>
            <p style="color: var(--text-light); margin-bottom: 1.5rem;">Let's personalize your demo experience</p>
            <form id="demoForm" action="{{ route('landing.trivia-demo.submit') }}" method="POST">
                @csrf
                <div class="mb-3">
                    <input type="text" class="form-control" id="player_name" name="player_name" 
                           placeholder="Your Name" required maxlength="255">
                </div>
                
                <!-- Questions -->
                @foreach($demoQuestions as $index => $question)
                <div class="question-card" id="question-{{ $index }}" style="display: none;">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div class="question-number">{{ $index + 1 }}</div>
                        <div class="text-muted">{{ $index + 1 }} of {{ count($demoQuestions) }}</div>
                    </div>
                    
                    <div class="progress mb-3" style="height: 8px; background: rgba(251, 207, 232, 0.3);">
                        <div class="progress-bar" style="width: {{ (($index + 1) / count($demoQuestions)) * 100 }}%"></div>
                    </div>
                    
                    <div class="question-text">{{ $question['question'] }}</div>
                    
                    <div class="options">
                        @foreach($question['options'] as $optionIndex => $option)
                        <button type="button" class="option-btn" data-question="{{ $index }}" data-option="{{ $optionIndex }}">
                            <span class="option-letter">{{ chr(65 + $optionIndex) }}</span>
                            {{ $option }}
                        </button>
                        @endforeach
                    </div>
                    
                    <input type="hidden" name="answers[{{ $index }}]" id="answer-{{ $index }}">
                </div>
                @endforeach
                
                <!-- Submit Section -->
                <div class="text-center" id="submit-section" style="display: none;">
                    <h3 style="color: var(--text-dark); margin-bottom: 1rem;">Ready to Submit?</h3>
                    <p style="color: var(--text-light); margin-bottom: 2rem;">You've answered all questions. Let's see how you did!</p>
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-check me-2"></i>Submit Answers
                    </button>
                </div>
            </form>
        </div>

        <!-- Navigation -->
        <div class="text-center">
            <a href="{{ route('landing.index') }}" class="back-link">
                <i class="fas fa-arrow-left me-2"></i>Back to Home
            </a>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    let currentQuestion = -1;
    const totalQuestions = {{ count($demoQuestions) }};
    const answers = {};
    let playerNameEntered = false;

    document.addEventListener('DOMContentLoaded', function() {
        const playerNameInput = document.getElementById('player_name');
        const optionButtons = document.querySelectorAll('.option-btn');
        
        // Handle player name input
        playerNameInput.addEventListener('input', function() {
            if (this.value.trim().length >= 2 && !playerNameEntered) {
                playerNameEntered = true;
                setTimeout(() => {
                    showNextQuestion();
                }, 500);
            }
        });
        
        // Handle option selection
        optionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const questionIndex = parseInt(this.dataset.question);
                const optionIndex = parseInt(this.dataset.option);
                
                // Remove selection from other options in this question
                document.querySelectorAll(`[data-question="${questionIndex}"]`).forEach(btn => {
                    btn.classList.remove('selected');
                });
                
                // Select this option
                this.classList.add('selected');
                
                // Store answer
                answers[questionIndex] = optionIndex;
                document.getElementById(`answer-${questionIndex}`).value = optionIndex;
                
                // Auto-advance after a short delay
                setTimeout(() => {
                    showNextQuestion();
                }, 800);
            });
        });
    });
    
    function showNextQuestion() {
        // Hide current question
        if (currentQuestion >= 0) {
            document.getElementById(`question-${currentQuestion}`).style.display = 'none';
        }
        
        currentQuestion++;
        
        if (currentQuestion < totalQuestions) {
            // Show next question
            document.getElementById(`question-${currentQuestion}`).style.display = 'block';
            
            // Scroll to question
            document.getElementById(`question-${currentQuestion}`).scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        } else {
            // Show submit section
            document.getElementById('submit-section').style.display = 'block';
            document.getElementById('submit-section').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }
    
    // Prevent form submission if not all questions are answered
    document.getElementById('demoForm').addEventListener('submit', function(e) {
        if (Object.keys(answers).length < totalQuestions) {
            e.preventDefault();
            alert('Please answer all questions before submitting.');
        }
    });
</script>
@endsection
