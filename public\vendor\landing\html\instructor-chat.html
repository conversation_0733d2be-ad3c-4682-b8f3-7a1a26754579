﻿<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
<title>Dreams LMS</title>

<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.svg">

<script src="assets/js/theme-script.js" type="97d542c1fb17e75d599a9587-text/javascript"></script>

<link rel="stylesheet" href="assets/css/bootstrap.min.css">

<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

<link rel="stylesheet" href="assets/css/feather.css">

<link rel="stylesheet" href="assets/plugins/boxicons/css/boxicons.min.css">

<link rel="stylesheet" href="assets/plugins/swiper/swiper.min.css">

<link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="chat-page main-chat-blk">

<div class="main-wrapper chat-wrapper">

<header class="header header-page">
<div class="header-fixed">
<nav class="navbar navbar-expand-lg header-nav scroll-sticky">
<div class="container ">
<div class="navbar-header">
<a id="mobile_btn" href="javascript:void(0);">
<span class="bar-icon">
<span></span>
<span></span>
<span></span>
</span>
</a>
<a href="index.html" class="navbar-brand logo">
<img src="assets/img/logo.svg" class="img-fluid" alt="Logo">
</a>
</div>
<div class="main-menu-wrapper">
<div class="menu-header">
<a href="index.html" class="menu-logo">
<img src="assets/img/logo.svg" class="img-fluid" alt="Logo">
</a>
<a id="menu_close" class="menu-close" href="javascript:void(0);">
<i class="fas fa-times"></i>
</a>
</div>
<ul class="main-nav">
<li class="has-submenu">
<a href="#">Home <i class="fas fa-chevron-down"></i></a>
<ul class="submenu">
<li><a href="index.html">Home</a></li>
<li><a href="index-two.html">Home Two</a></li>
<li><a href="index-three.html">Home Three</a></li>
<li><a href="index-four.html">Home Four</a></li>
</ul>
</li>
<li class="has-submenu active">
<a href="#">Instructor <i class="fas fa-chevron-down"></i></a>
<ul class="submenu">
<li class="has-submenu">
<a href="instructor-list.html">Instructor</a>
<ul class="submenu">
<li><a href="instructor-list.html">List</a></li>
<li><a href="instructor-grid.html">Grid</a></li>
</ul>
</li>
<li><a href="instructor-dashboard.html">Dashboard</a></li>
<li><a href="instructor-profile.html">My Profile</a></li>
<li><a href="instructor-course.html">My Course</a></li>
<li><a href="instructor-wishlist.html">Wishlist</a></li>
<li><a href="instructor-reviews.html">Reviews</a></li>
<li><a href="instructor-quiz.html">My Quiz Attempts</a></li>
<li><a href="instructor-orders.html">Orders</a></li>
<li><a href="instructor-qa.html">Question & Answer</a></li>
<li><a href="instructor-referral.html">Referrals</a></li>
<li class="active"><a href="instructor-chat.html">Messages</a></li>
<li><a href="instructor-tickets.html">Support Ticket</a></li>
<li><a href="instructor-notifications.html">Notifications</a></li>
<li><a href="instructor-settings.html">Settings</a></li>
</ul>
</li>
<li class="has-submenu">
<a href="#">Student <i class="fas fa-chevron-down"></i></a>
<ul class="submenu first-submenu">
<li class="has-submenu ">
<a href="students-list.html">Student</a>
<ul class="submenu">
<li><a href="students-list.html">List</a></li>
<li><a href="students-grid.html">Grid</a></li>
</ul>
</li>
<li><a href="student-dashboard.html">Student Dashboard</a></li>
<li><a href="student-profile.html">My Profile</a></li>
<li><a href="student-courses.html">Enrolled Courses</a></li>
<li><a href="student-wishlist.html">Wishlist</a></li>
<li><a href="student-reviews.html">Reviews</a></li>
<li><a href="student-quiz.html">My Quiz Attempts</a></li>
<li><a href="student-order-history.html">Orders</a></li>
<li><a href="student-qa.html">Question & Answer</a></li>
<li><a href="student-referral.html">Referrals</a></li>
<li><a href="student-messages.html">Messages</a></li>
<li><a href="student-tickets.html">Support Ticket</a></li>
<li><a href="student-settings.html">Settings</a></li>
</ul>
</li>
<li class="has-submenu">
<a href="#">Pages <i class="fas fa-chevron-down"></i></a>
<ul class="submenu">
<li><a href="notifications.html">Notification</a></li>
<li><a href="pricing-plan.html">Pricing Plan</a></li>
<li><a href="wishlist.html">Wishlist</a></li>
<li class="has-submenu">
<a href="course-list.html">Course</a>
<ul class="submenu">
<li><a href="add-course.html">Add Course</a></li>
<li><a href="course-list.html">Course List</a></li>
<li><a href="course-grid.html">Course Grid</a></li>
<li><a href="course-details.html">Course Details</a></li>
</ul>
</li>
<li class="has-submenu">
<a href="come-soon.html">Error</a>
<ul class="submenu">
<li><a href="come-soon.html">Coming Soon</a></li>
<li><a href="error-404.html">404</a></li>
<li><a href="error-500.html">500</a></li>
<li><a href="under-construction.html">Under Construction</a></li>
</ul>
</li>
<li><a href="faq.html">FAQ</a></li>
<li><a href="support.html">Support</a></li>
<li><a href="job-category.html">Category</a></li>
<li><a href="cart.html">Cart</a></li>
<li><a href="checkout.html">Checkout</a></li>
<li><a href="login.html">Login</a></li>
<li><a href="register.html">Register</a></li>
<li><a href="forgot-password.html">Forgot Password</a></li>
</ul>
</li>
<li class="has-submenu">
<a href="#">Blog <i class="fas fa-chevron-down"></i></a>
<ul class="submenu">
<li><a href="blog-list.html">Blog List</a></li>
<li><a href="blog-grid.html">Blog Grid</a></li>
<li><a href="blog-masonry.html">Blog Masonry</a></li>
<li><a href="blog-modern.html">Blog Modern</a></li>
<li><a href="blog-details.html">Blog Details</a></li>
</ul>
</li>
<li class="login-link">
<a href="login.html">Login / Signup</a>
</li>
</ul>
</div>
<ul class="nav header-navbar-rht">
<li class="nav-item">
<div>
<a href="javascript:void(0);" id="dark-mode-toggle" class="dark-mode-toggle  ">
<i class="fa-solid fa-moon"></i>
</a>
<a href="javascript:void(0);" id="light-mode-toggle" class="dark-mode-toggle ">
<i class="fa-solid fa-sun"></i>
</a>
</div>
</li>
<li class="nav-item user-nav">
<a href="#" class="dropdown-toggle" data-bs-toggle="dropdown">
<span class="user-img">
<img src="assets/img/user/user-17.jpg" alt="Img">
<span class="status online"></span>
</span>
</a>
<div class="users dropdown-menu dropdown-menu-right" data-popper-placement="bottom-end">
<div class="user-header">
<div class="avatar avatar-sm">
<img src="assets/img/user/user-17.jpg" alt="User Image" class="avatar-img rounded-circle">
</div>
<div class="user-text">
<h6>Eugene Andre</h6>
<p class="text-muted mb-0">Instructor</p>
</div>
</div>
<a class="dropdown-item" href="instructor-dashboard.html"><i class="feather-home me-1"></i> Dashboard</a>
<a class="dropdown-item" href="instructor-settings.html"><i class="feather-star me-1"></i> Edit Profile</a>
<a class="dropdown-item" href="index.html"><i class="feather-log-out me-1"></i> Logout</a>
</div>
</li>
</ul>
</div>
</nav>
</div>
</header>


<div class="breadcrumb-bar breadcrumb-bar-info">
<div class="container">
<div class="row">
<div class="col-md-12 col-12">
<div class="breadcrumb-list">
<h2 class="breadcrumb-title">Messages</h2>
<nav aria-label="breadcrumb" class="page-breadcrumb">
<ol class="breadcrumb">
<li class="breadcrumb-item"><a href="index.html">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Messages</li>
</ol>
</nav>
</div>
</div>
</div>
</div>
</div>


<div class="page-content chat-page-wrapper">
<div class="container">
<div class="row">

<div class="col-xl-3 col-lg-3 theiaStickySidebar">
<div class="settings-widget dash-profile">
<div class="settings-menu">
<div class="profile-bg">
<div class="profile-img">
<a href="instructor-profile.html"><img src="assets/img/user/user-17.jpg" alt="Img"></a>
</div>
</div>
<div class="profile-group">
<div class="profile-name text-center">
<h4><a href="instructor-profile.html">Eugene Andre</a></h4>
<p>Instructor</p>
<a href="add-course.html" class="add-course btn-primary">Add New Course</a>
</div>
</div>
</div>
</div>
<div class="settings-widget account-settings">
<div class="settings-menu">
<h3>Dashboard</h3>
<ul>
<li class="nav-item">
<a href="instructor-dashboard.html" class="nav-link">
<i class="bx bxs-tachometer"></i>Dashboard
</a>
</li>
<li class="nav-item">
<a href="instructor-profile.html" class="nav-link">
<i class="bx bxs-user"></i>My Profile
</a>
</li>
<li class="nav-item">
<a href="instructor-enrolled-course.html" class="nav-link">
<i class="bx bxs-graduation"></i>Enrolled Courses
</a>
</li>
<li class="nav-item">
<a href="instructor-wishlist.html" class="nav-link">
<i class="bx bxs-heart"></i>Wishlist
</a>
</li>
<li class="nav-item">
<a href="instructor-reviews.html" class="nav-link">
<i class="bx bxs-star"></i>Reviews
</a>
</li>
<li class="nav-item">
<a href="instructor-quiz.html" class="nav-link">
<i class="bx bxs-shapes"></i>My Quiz Attempts
</a>
</li>
<li class="nav-item">
<a href="instructor-orders.html" class="nav-link">
<i class="bx bxs-cart"></i>Order History
</a>
</li>
<li class="nav-item">
<a href="instructor-qa.html" class="nav-link">
<i class="bx bxs-bookmark-alt"></i>Question & Answer
</a>
</li>
<li class="nav-item">
<a href="instructor-referral.html" class="nav-link">
<i class="bx bxs-user-plus"></i>Referrals
</a>
</li>
<li class="nav-item active">
<a href="instructor-chat.html" class="nav-link">
<i class="bx bxs-chat"></i>Messages
</a>
</li>
<li class="nav-item">
<a href="instructor-notifications.html" class="nav-link">
<i class="bx bxs-bell"></i>Notifications
</a>
</li>
<li class="nav-item">
<a href="instructor-tickets.html" class="nav-link">
<i class="bx bxs-coupon"></i>Support Tickets
</a>
</li>
</ul>
<h3>Instructor</h3>
<ul>
<li class="nav-item">
<a href="instructor-course.html" class="nav-link ">
<i class="bx bxs-rocket"></i>My Courses
</a>
</li>
<li class="nav-item">
<a href="instructor-announcements.html" class="nav-link">
<i class="bx bxs-volume-full"></i>Announcements
</a>
</li>
<li class="nav-item">
<a href="instructor-withdraw.html" class="nav-link ">
<i class="bx bxs-wallet"></i>Withdrawls
</a>
</li>
<li class="nav-item">
<a href="instructor-quiz-attempts.html" class="nav-link">
<i class="bx bxs-shapes"></i>Quiz Attempts
</a>
</li>
<li class="nav-item">
<a href="instructor-assignment.html" class="nav-link ">
<i class="bx bxs-file"></i>Assignments
</a>
</li>
<li class="nav-item">
<a href="instructor-earnings.html" class="nav-link">
<i class="bx bxs-badge-dollar"></i>Earnings
</a>
</li>
</ul>
<h3>Account Settings</h3>
<ul>
<li class="nav-item">
<a href="instructor-settings.html" class="nav-link ">
<i class="bx bxs-cog"></i>Settings
</a>
</li>
<li class="nav-item">
<a href="index.html" class="nav-link">
<i class="bx bxs-log-out"></i>Logout
</a>
</li>
</ul>
</div>
</div>
</div>


<div class="col-xl-9 col-lg-9 theiaStickySidebar">
<div class="settings-widget card-details mb-0">
<div class="settings-menu p-0">
<div class="profile-heading">
<h3>Message</h3>
</div>
<div class="checkout-form">

<div class="content">
<div class="sidebar-group left-sidebar chat_sidebar">

<div id="chats" class="left-sidebar-wrap sidebar active slimscroll">
<div class="slimscroll">

<div class="left-chat-title all-chats d-flex justify-content-between align-items-center">
<div class="select-group-chat">
<div class="dropdown">
<a href="javascript:void(0);">
All Chats
</a>
</div>
</div>
<div class="add-section">
<ul>
<li><a href="javascript:void(0);" class="user-chat-search-btn"><i class="feather-search"></i></a></li>
</ul>

<div class="user-chat-search">
<form>
<span class="form-control-feedback"><i class="feather-search"></i></span>
<input type="text" name="chat-search" placeholder="Search" class="form-control">
<div class="user-close-btn-chat"><i class="feather-x"></i></div>
</form>
</div>

</div>
</div>


<div class="top-online-contacts">
<div class="fav-title">
<h6>Online Now</h6>
<a href="#" class="view-all-chat-profiles">View All</a>
</div>
<div class="swiper-container">
<div class="swiper-wrapper">
<div class="swiper-slide">
<div class="top-contacts-box">
<div class="profile-img online">
<a href="#"><img src="assets/img/user/user12.jpg" alt="Image"></a>
</div>
</div>
</div>
<div class="swiper-slide">
<div class="top-contacts-box">
<div class="profile-img online">
<a href="#"><img src="assets/img/user/user2.jpg" alt="Image"></a>
</div>
</div>
</div>
<div class="swiper-slide">
<div class="top-contacts-box">
<div class="profile-img online">
<a href="#"><img src="assets/img/user/user3.jpg" alt="Image"></a>
</div>
</div>
</div>
<div class="swiper-slide">
<div class="top-contacts-box">
<div class="profile-img online">
<a href="#"><img src="assets/img/user/user3.jpg" alt="Image"></a>
</div>
</div>
</div>
<div class="swiper-slide">
<div class="top-contacts-box">
<div class="profile-img online">
<a href="#"><img src="assets/img/user/user5.jpg" alt="Image"></a>
</div>
</div>
</div>
<div class="swiper-slide">
<div class="top-contacts-box">
<div class="profile-img online">
<a href="#"><img src="assets/img/user/user6.jpg" alt="Image"></a>
</div>
</div>
</div>
</div>
</div>
</div>

<div class="sidebar-body chat-body" id="chatsidebar">

<div class="d-flex justify-content-between align-items-center ps-0 pe-0">
<div class="fav-title pin-chat">
<h6>Pinned Chat</h6>
</div>
</div>

<ul class="user-list space-chat">
<li class="user-list-item chat-user-list">
<a href="javascript:void(0);" class="status-active">
<div class="avatar avatar-online">
<img src="assets/img/user/user12.jpg" class="rounded-circle" alt="image">
</div>
<div class="users-list-body">
<div>
<h5>Mark Villiams</h5>
<p>Have you called them?</p>
</div>
<div class="last-chat-time">
<small class="text-muted">10:20 PM</small>
<div class="chat-pin">
<i class="fa-solid fa-thumbtack me-2"></i>
<i class="fa-solid fa-check-double"></i>
</div>
</div>
</div>
</a>
<div class="chat-hover ms-1">
<div class="chat-action-col">
<span class="d-flex" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</span>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<span class="dropdown-item "><span><i class="bx bx-archive-in"></i></span>Archive Chat </span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-log-out"></i></span>Delete Chat</span>
<span class="dropdown-item"><span><i class="bx bx-pin"></i></span>Unpin Chat</span>
<span class="dropdown-item"><span><i class="bx bx-check-square"></i></span>Mark as Unread</span>
</div>
</div>
</div>
</li>
<li class="user-list-item chat-user-list">
<a href="javascript:void(0);">
<div>
<div class="avatar ">
<img src="assets/img/user/user5.jpg" class="rounded-circle" alt="image">
</div>
</div>
<div class="users-list-body">
<div>
<h5>Elizabeth Sosa</h5>
<p><span class="animate-typing-col">Typing
<span class="dot"></span>
<span class="dot"></span>
<span class="dot"></span>
</span>
</p>
</div>
<div class="last-chat-time">
<small class="text-muted">Yesterday</small>
<div class="chat-pin">
<i class="fa-solid fa-thumbtack"></i>
</div>
</div>
</div>
</a>
<div class="chat-hover">
<div class="chat-action-col">
<span class="d-flex" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</span>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<span class="dropdown-item "><span><i class="bx bx-archive-in"></i></span>Archive Chat </span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-log-out"></i></span>Delete Chat</span>
<span class="dropdown-item"><span><i class="bx bx-pin"></i></span>Unpin Chat</span>
<span class="dropdown-item"><span><i class="bx bx-check-square"></i></span>Mark as Unread</span>
</div>
</div>
</div>
</li>
<li class="user-list-item chat-user-list">
<a href="javascript:void(0);">
<div class="avatar avatar-online">
<img src="assets/img/user/user3.jpg" class="rounded-circle" alt="image">
</div>
<div class="users-list-body">
<div>
<h5>Michael Howard</h5>
<p>Thank you</p>
</div>
<div class="last-chat-time">
<small class="text-muted">10:20 PM</small>
<div class="chat-pin">
<i class="fa-solid fa-thumbtack me-2"></i>
<i class="fa-solid fa-check-double check"></i>
</div>
</div>
</div>
</a>
<div class="chat-hover ms-1">
<div class="chat-action-col">
<span class="d-flex" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</span>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<span class="dropdown-item "><span><i class="bx bx-archive-in"></i></span>Archive Chat </span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-log-out"></i></span>Delete Chat</span>
<span class="dropdown-item"><span><i class="bx bx-pin"></i></span>Unpin Chat</span>
<span class="dropdown-item"><span><i class="bx bx-check-square"></i></span>Mark as Unread</span>
</div>
</div>
</div>
</li>
</ul>

<div class="d-flex justify-content-between align-items-center ps-0 pe-0">
<div class="fav-title pin-chat">
<h6>Recent Chat</h6>
</div>
</div>

<ul class="user-list">
<li class="user-list-item chat-user-list">
<a href="javascript:void(0);">
<div class="avatar avatar-online">
<img src="assets/img/user/user11.jpg" class="rounded-circle" alt="image">
</div>
<div class="users-list-body">
<div>
<h5>Horace Keene</h5>
<p>Have you called them?</p>
</div>
<div class="last-chat-time">
<small class="text-muted">Just Now</small>
<div class="chat-pin">
<span class="count-message">5</span>
</div>
</div>
</div>
</a>
<div class="chat-hover ms-1">
<div class="chat-action-col">
<span class="d-flex" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</span>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<span class="dropdown-item "><span><i class="bx bx-archive-in"></i></span>Archive Chat </span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-trash"></i></span>Delete Chat</span>
<span class="dropdown-item"><span><i class="bx bx-pin"></i></span>Pin Chat</span>
<span class="dropdown-item"><span><i class="bx bx-check-square"></i></span>Mark as Read</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#block-user"><span><i class="bx bx-block"></i></span>Block</span>
</div>
</div>
</div>
</li>
<li class="user-list-item chat-user-list">
<a href="javascript:void(0);">
<div>
<div class="avatar avatar-online">
<img src="assets/img/user/user4.jpg" class="rounded-circle" alt="image">
</div>
</div>
<div class="users-list-body">
<div>
<h5>Hollis Tran</h5>
<p><i class="bx bx-video me-1"></i>Video</p>
</div>
<div class="last-chat-time">
<small class="text-muted">Yesterday</small>
<div class="chat-pin">
<i class="fa-solid fa-check"></i>
</div>
</div>
</div>
</a>
<div class="chat-hover ms-1">
<div class="chat-action-col">
<span class="d-flex" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</span>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<span class="dropdown-item "><span><i class="bx bx-archive-in"></i></span>Archive Chat </span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-trash"></i></span>Delete Chat</span>
<span class="dropdown-item"><span><i class="bx bx-pin"></i></span>Pin Chat</span>
<span class="dropdown-item"><span><i class="bx bx-check-square"></i></span>Mark as Unread</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#block-user"><span><i class="bx bx-block"></i></span>Block</span>
</div>
</div>
</div>
</li>
<li class="user-list-item chat-user-list">
<a href="javascript:void(0);">
<div class="avatar">
<img src="assets/img/user/user.jpg" class="rounded-circle" alt="image">
</div>
<div class="users-list-body">
<div>
<h5>James Albert</h5>
<p><i class="bx bx-file me-1"></i>Project Tools.doc</p>
</div>
<div class="last-chat-time">
<small class="text-muted">10:20 PM</small>
</div>
</div>
</a>
<div class="chat-hover ms-1">
<div class="chat-action-col">
<span class="d-flex" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</span>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<span class="dropdown-item "><span><i class="bx bx-archive-in"></i></span>Archive Chat </span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-trash"></i></span>Delete Chat</span>
<span class="dropdown-item"><span><i class="bx bx-pin"></i></span>Pin Chat</span>
<span class="dropdown-item"><span><i class="bx bx-check-square"></i></span>Mark as Unread</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#block-user"><span><i class="bx bx-block"></i></span>Block</span>
</div>
</div>
</div>
</li>
<li class="user-list-item chat-user-list">
<a href="javascript:void(0);">
<div>
<div class="avatar avatar-online">
<img src="assets/img/user/user6.jpg" class="rounded-circle" alt="image">
</div>
</div>
<div class="users-list-body">
<div>
<h5>Debra Jones</h5>
<p><i class="bx bx-microphone me-1"></i>Audio</p>
</div>
<div class="last-chat-time">
<small class="text-muted">12:30 PM</small>
<div class="chat-pin">
<i class="fa-solid fa-check-double check"></i>
</div>
</div>
</div>
</a>
<div class="chat-hover ms-1">
<div class="chat-action-col">
<span class="d-flex" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</span>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<span class="dropdown-item "><span><i class="bx bx-archive-in"></i></span>Archive Chat </span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-trash"></i></span>Delete Chat</span>
<span class="dropdown-item"><span><i class="bx bx-pin"></i></span>Pin Chat</span>
<span class="dropdown-item"><span><i class="bx bx-check-square"></i></span>Mark as Unread</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#block-user"><span><i class="bx bx-block"></i></span>Block</span>
</div>
</div>
</div>
</li>
<li class="user-list-item chat-user-list">
<a href="javascript:void(0);">
<div>
<div class="avatar ">
<img src="assets/img/user/user1.jpg" class="rounded-circle" alt="image">
</div>
</div>
<div class="users-list-body">
<div>
<h5>Dina Brown</h5>
<p>Have you called them?</p>
</div>
<div class="last-chat-time">
<small class="text-muted">Yesterday</small>
<div class="chat-pin">
<i class="fa-solid fa-microphone-slash"></i>
</div>
</div>
</div>
</a>
<div class="chat-hover ms-1">
<div class="chat-action-col">
<span class="d-flex" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</span>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<span class="dropdown-item "><span><i class="bx bx-archive-in"></i></span>Archive Chat </span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-trash"></i></span>Delete Chat</span>
<span class="dropdown-item"><span><i class="bx bx-pin"></i></span>Pin Chat</span>
<span class="dropdown-item"><span><i class="bx bx-check-square"></i></span>Mark as Unread</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#block-user"><span><i class="bx bx-block"></i></span>Block</span>
</div>
</div>
</div>
</li>
<li class="user-list-item chat-user-list">
<a href="javascript:void(0);">
<div>
<div class="avatar avatar-online">
<img src="assets/img/user/user13.jpg" class="rounded-circle" alt="image">
</div>
</div>
<div class="users-list-body">
<div>
<h5>Judy Mercer</h5>
<p class="missed-call-col"><i class="bx bx-phone-incoming me-1"></i>Missed Call</p>
</div>
<div class="last-chat-time">
<small class="text-muted">25/July/23</small>
</div>
</div>
</a>
<div class="chat-hover ms-1">
<div class="chat-action-col">
<span class="d-flex" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</span>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<span class="dropdown-item "><span><i class="bx bx-archive-in"></i></span>Archive Chat </span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-trash"></i></span>Delete Chat</span>
<span class="dropdown-item"><span><i class="bx bx-pin"></i></span>Pin Chat</span>
<span class="dropdown-item"><span><i class="bx bx-check-square"></i></span>Mark as Unread</span>
<span class="dropdown-item" data-bs-toggle="modal" data-bs-target="#block-user"><span><i class="bx bx-block"></i></span>Block</span>
</div>
</div>
</div>
</li>
</ul>
</div>
</div>
</div>

</div>


<div class="chat chat-messages" id="middle">
<div class="h-100">
<div class="chat-header">
<div class="user-details mb-0">
<div class="d-lg-none">
<ul class="list-inline mt-2 me-2">
<li class="list-inline-item">
<a class="text-muted px-0 left_sides" href="javascript:void(0);" data-chat="open">
<i class="fas fa-arrow-left"></i>
</a>
</li>
</ul>
</div>
<figure class="avatar mb-0">
<img src="assets/img/user/user12.jpg" class="rounded-circle" alt="image">
</figure>
<div class="mt-1">
<h5>Mark Villiams</h5>
<small class="last-seen">
Last Seen at 07:15 PM
</small>
</div>
</div>
<div class="chat-options ">
<ul class="list-inline">
<li class="list-inline-item">
<a href="javascript:void(0)" class="btn btn-outline-light chat-search-btn" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Search">
<i class="feather-search"></i>
</a>
</li>
<li class="list-inline-item">
<a class="btn btn-outline-light no-bg" href="javascript:void(0);" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</a>
<div class="dropdown-menu dropdown-menu-end">
<a href="index.html" class="dropdown-item "><span><i class="bx bx-x"></i></span>Close Chat </a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#mute-notification"><span><i class="bx bx-volume-mute"></i></span>Mute Notification</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#disappearing-messages"><span><i class="bx bx-time-five"></i></span>Disappearing Message</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#clear-chat"><span><i class="bx bx-brush-alt"></i></span>Clear Message</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#change-chat"><span><i class="bx bx-trash"></i></span>Delete Chat</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#report-user"><span><i class="bx bx-dislike"></i></span>Report</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#block-user"><span><i class="bx bx-block"></i></span>Block</a>
</div>
</li>
</ul>
</div>

<div class="chat-search">
<form>
<span class="form-control-feedback"><i class="bx bx-search"></i></span>
<input type="text" name="chat-search" placeholder="Search Chats" class="form-control">
<div class="close-btn-chat"><i class="feather-x"></i></div>
</form>
</div>

</div>
<div class="chat-body chat-page-group slimscroll">
<div class="messages">
<div class="chats">
<div class="chat-avatar">
<img src="assets/img/user/user12.jpg" class="rounded-circle dreams_chat" alt="image">
</div>
<div class="chat-content">
<div class="chat-profile-name">
<h6>Mark Villiams<span>8:16 PM</span><span class="check-star msg-star d-none"><i class="bx bxs-star"></i></span></h6>
<div class="chat-action-btns ms-2">
<div class="chat-action-col">
<a class="#" href="javascript:void(0);" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis"></i>
</a>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<a href="javascript:void(0);" class="dropdown-item message-info-left"><span><i class="bx bx-info-circle"></i></span>Message Info </a>
<a href="javascript:void(0);" class="dropdown-item reply-button"><span><i class="bx bx-share"></i></span>Reply</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-smile"></i></span>React</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#forward-message"><span><i class="bx bx-reply"></i></span>Forward</a>
<a href="javascript:void(0);" class="dropdown-item click-star"><span><i class="bx bx-star"></i></span><span class="star-msg">Star Message</span></a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#report-user"><span><i class="bx bx-dislike"></i></span>Report</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#delete-message"><span><i class="bx bx-trash"></i></span>Delete</a>
</div>
</div>
</div>
</div>
<div class="message-content">
Hello <a href="javascript:void(0);">@Alex</a> Thank you for the beautiful web design ahead schedule.
</div>
</div>
</div>
<div class="chat-line">
<span class="chat-date">Today, July 24</span>
</div>
<div class="chats chats-right">
<div class="chat-content">
<div class="chat-profile-name text-end">
<h6>Alex Smith<span>8:16 PM</span><span class="check-star msg-star-one d-none"><i class="bx bxs-star"></i></span></h6>
<div class="chat-action-btns ms-2">
<div class="chat-action-col">
<a class="#" href="javascript:void(0);" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis"></i>
</a>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<a href="javascript:void(0);" class="dropdown-item message-info-left"><span><i class="bx bx-info-circle"></i></span>Message Info </a>
<a href="javascript:void(0);" class="dropdown-item reply-button"><span><i class="bx bx-share"></i></span>Reply</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-smile"></i></span>React</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#forward-message"><span><i class="bx bx-reply"></i></span>Forward</a>
<a href="javascript:void(0);" class="dropdown-item click-star-one"><span><i class="bx bx-star"></i></span><span class="star-msg-one">Star Message</span></a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#edit-message"><span><i class="bx bx-edit-alt"></i></span>Edit</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#delete-message"><span><i class="bx bx-trash"></i></span>Delete</a>
</div>
</div>
</div>
</div>
<div class="message-content ">
<div class="chat-voice-group">
<ul>
<li><a href="javascript:void(0);"><span><img src="assets/img/icon/play-01.svg" alt="image"></span></a></li>
<li><img src="assets/img/icon/voice.svg" class="img-fluid" alt="image"></li>
<li>0:05</li>
</ul>
</div>
</div>
</div>
<div class="chat-avatar">
<img src="assets/img/user/user2.jpg" class="rounded-circle dreams_chat" alt="image">
</div>
</div>
<div class="chats">
<div class="chat-avatar">
<img src="assets/img/user/user12.jpg" class="rounded-circle dreams_chat" alt="image">
</div>
<div class="chat-content">
<div class="chat-profile-name">
<h6>Mark Villiams<span>8:16 PM</span><span class="check-star msg-star-three d-none"><i class="bx bxs-star"></i></span></h6>
<div class="chat-action-btns ms-2">
<div class="chat-action-col">
<a class="#" href="javascript:void(0);" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis"></i>
</a>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<a href="javascript:void(0);" class="dropdown-item message-info-left"><span><i class="bx bx-info-circle"></i></span>Message Info </a>
<a href="javascript:void(0);" class="dropdown-item reply-button"><span><i class="bx bx-share"></i></span>Reply</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-smile"></i></span>React</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#forward-message"><span><i class="bx bx-reply"></i></span>Forward</a>
<a href="javascript:void(0);" class="dropdown-item click-star-three"><span><i class="bx bx-star"></i></span><span class="star-msg-three">Star Message</span></a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#report-user"><span><i class="bx bx-dislike"></i></span>Report</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#delete-message"><span><i class="bx bx-trash"></i></span>Delete</a>
</div>
</div>
</div>
</div>
<div class="message-content award-link chat-award-link">
<a href="javascript:void(0);">https://www.youtube.com/watch?v=GCmL3mS0Psk</a>
<img src="assets/img/chat-img-01.jpg" class="img-fluid" alt="img"> </div>
</div>
</div>
<div class="chats chats-right">
<div class="chat-content">
<div class="chat-profile-name text-end">
<h6>Alex Smith<span>8:16 PM</span><span class="check-star msg-star-one d-none"><i class="bx bxs-star"></i></span></h6>
<div class="chat-action-btns ms-2">
<div class="chat-action-col">
<a class="#" href="javascript:void(0);" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis"></i>
</a>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<a href="javascript:void(0);" class="dropdown-item message-info-left"><span><i class="bx bx-info-circle"></i></span>Message Info </a>
<a href="javascript:void(0);" class="dropdown-item reply-button"><span><i class="bx bx-share"></i></span>Reply</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-smile"></i></span>React</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#forward-message"><span><i class="bx bx-reply"></i></span>Forward</a>
<a href="javascript:void(0);" class="dropdown-item click-star-one"><span><i class="bx bx-star"></i></span><span class="star-msg-one">Star Message</span></a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#edit-message"><span><i class="bx bx-edit-alt"></i></span>Edit</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#delete-message"><span><i class="bx bx-trash"></i></span>Delete</a>
</div>
</div>
</div>
</div>
<div class="message-content ">
Thankyou for the notes sir. Will you send me the location where I could bought?
</div>
</div>
<div class="chat-avatar">
<img src="assets/img/user/user2.jpg" class="rounded-circle dreams_chat" alt="image">
</div>
</div>
<div class="chats">
<div class="chat-avatar">
<img src="assets/img/user/user12.jpg" class="rounded-circle dreams_chat" alt="image">
</div>
<div class="chat-content">
<div class="chat-profile-name">
<h6>Mark Villiams<span>8:16 PM</span><span class="check-star msg-star-five d-none"><i class="bx bxs-star"></i></span></h6>
<div class="chat-action-btns ms-2">
<div class="chat-action-col">
<a class="#" href="javascript:void(0);" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis"></i>
</a>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<a href="javascript:void(0);" class="dropdown-item message-info-left"><span><i class="bx bx-info-circle"></i></span>Message Info </a>
<a href="javascript:void(0);" class="dropdown-item reply-button"><span><i class="bx bx-share"></i></span>Reply</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-smile"></i></span>React</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#forward-message"><span><i class="bx bx-reply"></i></span>Forward</a>
<a href="javascript:void(0);" class="dropdown-item click-star-five"><span><i class="bx bx-star"></i></span><span class="star-msg-five">Star Message</span></a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#report-user"><span><i class="bx bx-dislike"></i></span>Report</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#delete-message"><span><i class="bx bx-trash"></i></span>Delete</a>
</div>
</div>
</div>
</div>
<div class="message-content">
<div class="location-sharing">
<div class="sharing-location-icon">
<i class="fa-solid fa-location-dot"></i>
</div>
<h6>My Location <a href="#">Download</a></h6>
</div>
</div>
<div class="like-chat-grp">
<ul>
<li class="like-chat"><a href="javascript:void(0);">2<img src="assets/img/icon/like.svg" alt="Icon"></a></li>
<li class="comment-chat"><a href="javascript:void(0);">2<img src="assets/img/icon/heart.svg" alt="Icon"></a></li>
</ul>
</div>
</div>
</div>
<div class="chats">
<div class="chat-avatar">
<img src="assets/img/user/user12.jpg" class="rounded-circle dreams_chat" alt="image">
</div>
<div class="chat-content">
<div class="chat-profile-name">
<h6>Mark Villiams<span>8:16 PM</span><span class="check-star msg-star d-none"><i class="bx bxs-star"></i></span></h6>
<div class="chat-action-btns ms-2">
<div class="chat-action-col">
<a class="#" href="javascript:void(0);" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis"></i>
</a>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<a href="javascript:void(0);" class="dropdown-item message-info-left"><span><i class="bx bx-info-circle"></i></span>Message Info </a>
<a href="javascript:void(0);" class="dropdown-item reply-button"><span><i class="bx bx-share"></i></span>Reply</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-smile"></i></span>React</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#forward-message"><span><i class="bx bx-reply"></i></span>Forward</a>
<a href="javascript:void(0);" class="dropdown-item click-star"><span><i class="bx bx-star"></i></span><span class="star-msg">Star Message</span></a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#report-user"><span><i class="bx bx-edit-alt"></i></span>Report</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#delete-message"><span><i class="bx bx-trash"></i></span>Delete</a>
</div>
</div>
</div>
</div>
<div class="message-content reply-getcontent">
Thank you for your support
</div>
</div>
</div>
</div>
</div>
</div>
<div class="chat-footer">
<form>
<div class="smile-foot">
<div class="chat-action-btns">
<div class="chat-action-col">
<a class="action-circle" href="javascript:void(0);" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis-vertical"></i>
</a>
<div class="dropdown-menu dropdown-menu-end">
<a href="javascript:void(0);" class="dropdown-item "><span><i class="bx bx-file"></i></span>Document</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-camera"></i></span>Camera</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-image"></i></span>Gallery</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-volume-full"></i></span>Audio</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-map"></i></span>Location</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-user-pin"></i></span>Contact</a>
</div>
</div>
</div>
</div>
<div class="smile-foot emoj-action-foot">
<a href="javascript:void(0);" class="action-circle"><i class="bx bx-smile"></i></a>
</div>
<div class="smile-foot">
<a href="javascript:void(0);" class="action-circle"><i class="bx bx-microphone-off"></i></a>
</div>
<div class="replay-forms">
<div class="chats forward-chat-msg reply-div d-none">
<div class="contact-close_call text-end">
<a href="javascript:void(0);" class="close-replay">
<i class="bx bx-x"></i>
</a>
</div>
<div class="chat-avatar">
<img src="assets/img/user/user3.jpg" class="rounded-circle dreams_chat" alt="image">
</div>
<div class="chat-content">
<div class="chat-profile-name">
<h6>Mark Villiams<span>8:16 PM</span></h6>
<div class="chat-action-btns ms-2">
<div class="chat-action-col">
<a class="#" href="javascript:void(0);" data-bs-toggle="dropdown">
<i class="fa-solid fa-ellipsis"></i>
</a>
<div class="dropdown-menu chat-drop-menu dropdown-menu-end">
<a href="javascript:void(0);" class="dropdown-item message-info-left"><span><i class="bx bx-info-circle"></i></span>Message Info </a>
<a href="javascript:void(0);" class="dropdown-item reply-button"><span><i class="bx bx-share"></i></span>Reply</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-smile"></i></span>React</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#forward-message"><span><i class="bx bx-reply"></i></span>Forward</a>
<a href="javascript:void(0);" class="dropdown-item"><span><i class="bx bx-star"></i></span>Star Message</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#report-user"><span><i class="bx bx-dislike"></i></span>Report</a>
<a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#delete-message"><span><i class="bx bx-trash"></i></span>Delete</a>
</div>
</div>
</div>
</div>
<div class="message-content reply-content">
</div>
</div>
</div>
<input type="text" class="form-control chat_form" placeholder="Type your message here...">
</div>
<div class="form-buttons">
<button class="btn send-btn" type="submit">
<i class="bx bx-paper-plane"></i>
</button>
</div>
</form>
</div>
</div>

</div>
</div>
</div>
</div>
</div>

</div>
</div>
</div>


<footer class="footer">

<div class="footer-top">
<div class="container">
<div class="row">
<div class="col-lg-4 col-md-6">

<div class="footer-widget footer-about">
<div class="footer-logo">
<img src="assets/img/logo.svg" alt="logo">
</div>
<div class="footer-about-content">
<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut consequat mauris Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut consequat mauris</p>
</div>
</div>

</div>
<div class="col-lg-2 col-md-6">

<div class="footer-widget footer-menu">
<h2 class="footer-title">For Instructor</h2>
<ul>
<li><a href="instructor-profile.html">Profile</a></li>
<li><a href="login.html">Login</a></li>
<li><a href="register.html">Register</a></li>
<li><a href="instructor-list.html">Instructor</a></li>
<li><a href="instructor-dashboard.html"> Dashboard</a></li>
</ul>
</div>

</div>
<div class="col-lg-2 col-md-6">

<div class="footer-widget footer-menu">
<h2 class="footer-title">For Student</h2>
<ul>
<li><a href="student-profile.html">Profile</a></li>
<li><a href="login.html">Login</a></li>
<li><a href="register.html">Register</a></li>
<li><a href="students-list.html">Student</a></li>
<li><a href="student-dashboard.html"> Dashboard</a></li>
</ul>
</div>

</div>
<div class="col-lg-4 col-md-6">

<div class="footer-widget footer-contact">
<h2 class="footer-title">News letter</h2>
<div class="news-letter">
<form>
<input type="text" class="form-control" placeholder="Enter your email address" name="email">
</form>
</div>
<div class="footer-contact-info">
<div class="footer-address">
<img src="assets/img/icon/icon-20.svg" alt="Img" class="img-fluid">
<p> 3556 Beech Street, San Francisco,<br> California, CA 94108 </p>
</div>
<p>
<img src="assets/img/icon/icon-19.svg" alt="Img" class="img-fluid">
<a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="583c2a3d39352b34352b183d20393528343d763b3735">[email&#160;protected]</a>
</p>
<p class="mb-0">
<img src="assets/img/icon/icon-21.svg" alt="Img" class="img-fluid">
+19 123-456-7890
</p>
</div>
</div>

</div>
</div>
</div>
</div>


<div class="footer-bottom">
<div class="container">

<div class="copyright">
<div class="row">
<div class="col-md-6">
<div class="privacy-policy">
<ul>
<li><a href="term-condition.html">Terms</a></li>
<li><a href="privacy-policy.html">Privacy</a></li>
</ul>
</div>
</div>
<div class="col-md-6">
<div class="copyright-text">
<p class="mb-0">&copy; 2024 DreamsLMS. All rights reserved.</p>
</div>
</div>
</div>
</div>

</div>
</div>

</footer>

</div>


<script data-cfasync="false" src="../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="assets/js/jquery-3.7.1.min.js" type="97d542c1fb17e75d599a9587-text/javascript"></script>

<script src="assets/js/bootstrap.bundle.min.js" type="97d542c1fb17e75d599a9587-text/javascript"></script>

<script src="assets/plugins/theia-sticky-sidebar/ResizeSensor.js" type="97d542c1fb17e75d599a9587-text/javascript"></script>
<script src="assets/plugins/theia-sticky-sidebar/theia-sticky-sidebar.js" type="97d542c1fb17e75d599a9587-text/javascript"></script>

<script src="assets/js/jquery.slimscroll.min.js" type="97d542c1fb17e75d599a9587-text/javascript"></script>

<script src="assets/plugins/swiper/swiper.min.js" type="97d542c1fb17e75d599a9587-text/javascript"></script>

<script src="assets/js/script.js" type="97d542c1fb17e75d599a9587-text/javascript"></script>
<script src="../cdn-cgi/scripts/7d0fa10a/cloudflare-static/rocket-loader.min.js" data-cf-settings="97d542c1fb17e75d599a9587-|49" defer=""></script></body>
</html>