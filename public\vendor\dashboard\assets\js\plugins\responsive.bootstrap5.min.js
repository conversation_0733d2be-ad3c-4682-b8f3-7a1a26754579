/*! Bootstrap 5 integration for DataTables' Responsive
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var o,a;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-responsive"],function(e){return n(e,window,document)}):"object"==typeof exports?(o=require("jquery"),a=function(e,d){d.fn.dataTable||require("datatables.net-bs5")(e,d),d.fn.dataTable.Responsive||require("datatables.net-responsive")(e,d)},"undefined"==typeof window?module.exports=function(e,d){return e=e||window,d=d||o(e),a(e,d),n(d,e,e.document)}:(a(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)}(function(r,e,l){"use strict";var u,d=r.fn.dataTable,n=d.Responsive.display,f=n.modal,m=r('<div class="modal fade dtr-bs-modal" role="dialog"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"/></div></div></div>'),o=e.bootstrap;return d.Responsive.bootstrap=function(e){o=e},n.modal=function(s){return u=u||new o.Modal(m[0]),function(e,d,n,o){if(r.fn.modal){var a,t,i=n();if(!1===i)return!1;if(d){if(!r.contains(l,m[0])||e.index()!==m.data("dtr-row-idx"))return null;m.find("div.modal-body").empty().append(i)}else s&&s.header&&(t=(a=m.find("div.modal-header")).find("button").detach(),a.empty().append('<h4 class="modal-title">'+s.header(e)+"</h4>").append(t)),m.find("div.modal-body").empty().append(i),m.data("dtr-row-idx",e.index()).one("hidden.bs.modal",o).appendTo("body").modal(),u.show();return!0}return f(e,d,n,o)}},d});