@extends('admin.layout.master')

@section('title', 'Admin Dashboard')

@section('content')
<div class="pc-container">
    <div class="pc-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h2 class="mb-0">Admin Dashboard</h2>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Home</a></li>
                            <li class="breadcrumb-item" aria-current="page">Dashboard</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <!-- Statistics Cards -->
            <div class="col-md-6 col-xl-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h3 class="text-white">{{ $stats['total_clients'] }}</h3>
                                <span class="text-white-75">Total Clients</span>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-xl-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h3 class="text-white">{{ $stats['total_games'] }}</h3>
                                <span class="text-white-75">Total Games</span>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-gamepad fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-xl-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h3 class="text-white">{{ $stats['active_games'] }}</h3>
                                <span class="text-white-75">Active Games</span>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-play fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-xl-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h3 class="text-white">{{ $stats['total_guests'] }}</h3>
                                <span class="text-white-75">Total Guests</span>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user-friends fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Clients -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Recent Clients</h5>
                    </div>
                    <div class="card-body">
                        @if($stats['recent_clients']->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Created</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($stats['recent_clients'] as $client)
                                        <tr>
                                            <td>{{ $client->client_name }}</td>
                                            <td>{{ $client->user->email ?? 'N/A' }}</td>
                                            <td>{{ $client->created_at->format('M d, Y') }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-muted">No clients found.</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Game Configs -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Recent Game Configurations</h5>
                    </div>
                    <div class="card-body">
                        @if($stats['recent_game_configs']->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Game Title</th>
                                            <th>Client</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($stats['recent_game_configs'] as $config)
                                        <tr>
                                            <td>{{ $config->game_title }}</td>
                                            <td>{{ $config->client->client_name ?? 'N/A' }}</td>
                                            <td>
                                                <span class="badge bg-{{ $config->is_active ? 'success' : 'secondary' }}">
                                                    {{ $config->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-muted">No game configurations found.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="{{ route('admin.clients.index') }}" class="btn btn-primary w-100">
                                    <i class="fas fa-users me-2"></i>Manage Clients
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="{{ route('admin.games.index') }}" class="btn btn-success w-100">
                                    <i class="fas fa-gamepad me-2"></i>Manage Games
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="{{ route('admin.game-configs.index') }}" class="btn btn-warning w-100">
                                    <i class="fas fa-cog me-2"></i>Game Configs
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="{{ route('admin.trivia-questions.index') }}" class="btn btn-info w-100">
                                    <i class="fas fa-question-circle me-2"></i>Questions
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
