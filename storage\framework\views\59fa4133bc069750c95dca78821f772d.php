<?php $__env->startSection('title', 'Manage Questions'); ?>
<?php $__env->startSection('page-title', 'Manage Trivia Questions'); ?>
<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item"><a href="<?php echo e(route('client.dashboard.minigames')); ?>">Minigames</a></li>
<li class="breadcrumb-item active">Questions</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <!-- Header -->
    <div class="col-12 mb-4">
        <div class="card"
            style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border: none; border-radius: 15px;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 style="color: #721c24; font-weight: 700; margin-bottom: 5px;">
                            <i class="fas fa-question-circle me-2" style="color: #d63384;"></i>
                            Question Management
                        </h2>
                        <p style="color: #721c24; margin-bottom: 0;">
                            Manage questions for: <strong><?php echo e($gameConfig->game_title); ?></strong>
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal"
                            data-bs-target="#addQuestionModal" style="border-radius: 20px;">
                            <i class="fas fa-plus me-1"></i>Add Question
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-info" data-bs-toggle="modal"
                                data-bs-target="#bulkImportModal" style="border-radius: 20px 0 0 20px;">
                                <i class="fas fa-upload me-1"></i>Import Excel
                            </button>
                            <a href="<?php echo e(route('client.dashboard.questions.template', $gameConfig)); ?>"
                                class="btn btn-outline-info" style="border-radius: 0 20px 20px 0;">
                                <i class="fas fa-download me-1"></i>Template
                            </a>
                        </div>
                        <a href="<?php echo e(route('client.dashboard.minigames')); ?>" class="btn btn-outline-dark"
                            style="border-radius: 20px;">
                            <i class="fas fa-arrow-left me-1"></i>Back to Games
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Questions List -->
    <div class="col-12">
        <div class="card" style="border: 2px solid #f8d7da; border-radius: 15px;">
            <div class="card-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 13px 13px 0 0;">
                <h5 class="mb-0" style="color: #721c24; font-weight: 600;">
                    Questions (<?php echo e($questions->count()); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if($questions->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="color: #721c24;">#</th>
                                <th style="color: #721c24;">Question</th>
                                <th style="color: #721c24;">Options</th>
                                <th style="color: #721c24;">Correct Answer</th>
                                <th style="color: #721c24;">Status</th>
                                <th style="color: #721c24;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($index + 1); ?></td>
                                <td>
                                    <div style="max-width: 300px;">
                                        <?php echo e(Str::limit($question->question, 100)); ?>

                                    </div>
                                </td>
                                <td>
                                    <div style="max-width: 200px;">
                                        <?php $__currentLoopData = $question->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optIndex => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <small
                                            class="d-block <?php echo e($optIndex == $question->correct_answer ? 'text-success fw-bold' : 'text-muted'); ?>">
                                            <?php echo e(chr(65 + $optIndex)); ?>. <?php echo e(Str::limit($option, 30)); ?>

                                        </small>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success">
                                        <?php echo e(chr(65 + $question->correct_answer)); ?>

                                    </span>
                                </td>
                                <td>
                                    <?php if($question->is_active): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary edit-question-btn"
                                            data-question="<?php echo e(json_encode($question)); ?>" data-bs-toggle="modal"
                                            data-bs-target="#editQuestionModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form action="<?php echo e(route('client.dashboard.questions.delete', $question)); ?>"
                                            method="POST" class="d-inline"
                                            onsubmit="return confirm('Are you sure you want to delete this question?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-question-circle fa-4x mb-3" style="color: #d63384; opacity: 0.3;"></i>
                    <h4 style="color: #721c24;">No Questions Yet</h4>
                    <p class="text-muted">Add your first trivia question to get started!</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#addQuestionModal"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); border: none; border-radius: 10px;">
                        <i class="fas fa-plus me-1"></i>Add First Question
                    </button>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Question Modal -->
<div class="modal fade" id="addQuestionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px;">
            <div class="modal-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 15px 15px 0 0;">
                <h5 class="modal-title" style="color: #721c24; font-weight: 600;">Add New Question</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?php echo e(route('client.dashboard.questions.store', $gameConfig)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="question" class="form-label"
                            style="color: #721c24; font-weight: 600;">Question</label>
                        <textarea class="form-control" id="question" name="question" rows="3" required
                            placeholder="Enter your trivia question here..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label" style="color: #721c24; font-weight: 600;">Answer Options</label>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" placeholder="Option A"
                                    required>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" placeholder="Option B"
                                    required>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" placeholder="Option C">
                            </div>
                            <div class="mb-3">
                                <input type="text" class="form-control" name="options[]" placeholder="Option D">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="correct_answer" class="form-label"
                                    style="color: #721c24; font-weight: 600;">Correct Answer</label>
                                <select class="form-select" id="correct_answer" name="correct_answer" required>
                                    <option value="">Select Answer</option>
                                    <option value="0">A</option>
                                    <option value="1">B</option>
                                    <option value="2">C</option>
                                    <option value="3">D</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Question limit warning -->
                    <div class="alert alert-info" id="questionLimitWarning" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Maximum 10 questions allowed per game.</strong> You currently have <span
                            id="currentQuestionCount"><?php echo e($questions->count()); ?></span> questions.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); border: none;">
                        <i class="fas fa-plus me-1"></i>Add Question
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Question Modal -->
<div class="modal fade" id="editQuestionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px;">
            <div class="modal-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 15px 15px 0 0;">
                <h5 class="modal-title" style="color: #721c24; font-weight: 600;">Edit Question</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editQuestionForm" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_question" class="form-label"
                            style="color: #721c24; font-weight: 600;">Question</label>
                        <textarea class="form-control" id="edit_question" name="question" rows="3" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label" style="color: #721c24; font-weight: 600;">Answer Options</label>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" id="edit_option_0"
                                    placeholder="Option A" required>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" id="edit_option_1"
                                    placeholder="Option B" required>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" id="edit_option_2"
                                    placeholder="Option C">
                            </div>
                            <div class="mb-3">
                                <input type="text" class="form-control" name="options[]" id="edit_option_3"
                                    placeholder="Option D">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_correct_answer" class="form-label"
                                    style="color: #721c24; font-weight: 600;">Correct Answer</label>
                                <select class="form-select" id="edit_correct_answer" name="correct_answer" required>
                                    <option value="0">A</option>
                                    <option value="1">B</option>
                                    <option value="2">C</option>
                                    <option value="3">D</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active"
                                        value="1">
                                    <label class="form-check-label" for="edit_is_active" style="color: #721c24;">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); border: none;">
                        <i class="fas fa-save me-1"></i>Update Question
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Import Modal -->
<div class="modal fade" id="bulkImportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px;">
            <div class="modal-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 15px 15px 0 0;">
                <h5 class="modal-title" style="color: #721c24; font-weight: 600;">Import Questions from Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?php echo e(route('client.dashboard.questions.excel-import', $gameConfig)); ?>" method="POST"
                enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="excel_file" class="form-label" style="color: #721c24; font-weight: 600;">Excel File
                            (.xlsx)</label>
                        <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx,.xls"
                            required>
                        <small class="text-muted">
                            Upload an Excel file with questions. Maximum 10 questions per game.
                        </small>
                    </div>

                    <div class="alert alert-info">
                        <h6 style="color: #721c24;"><i class="fas fa-info-circle me-2"></i>Excel Format Requirements:
                        </h6>
                        <ul class="mb-0" style="font-size: 14px;">
                            <li><strong>Column A:</strong> Question</li>
                            <li><strong>Column B:</strong> Option A</li>
                            <li><strong>Column C:</strong> Option B</li>
                            <li><strong>Column D:</strong> Option C (optional)</li>
                            <li><strong>Column E:</strong> Option D (optional)</li>
                            <li><strong>Column F:</strong> Correct Answer (A, B, C, or D)</li>
                        </ul>
                        <div class="mt-2">
                            <a href="<?php echo e(route('client.dashboard.questions.template', $gameConfig)); ?>"
                                class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download me-1"></i>Download Template
                            </a>
                        </div>
                    </div>

                    <div class="alert alert-warning" id="excelImportWarning" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Import will replace existing questions!</strong> Current questions will be deleted.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); border: none;">
                        <i class="fas fa-upload me-1"></i>Import Questions
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const currentQuestionCount = <?php echo e($questions->count()); ?>;
        const maxQuestions = 10;

        // Check question limit on page load
        checkQuestionLimit();

        // Handle edit question modal
        const editButtons = document.querySelectorAll('.edit-question-btn');
        const editForm = document.getElementById('editQuestionForm');

        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const questionData = JSON.parse(this.getAttribute('data-question'));

                // Set form action
                editForm.action = `/client/questions/${questionData.id}`;

                // Populate form fields
                document.getElementById('edit_question').value = questionData.question;
                document.getElementById('edit_correct_answer').value = questionData.correct_answer;
                document.getElementById('edit_is_active').checked = questionData.is_active;

                // Populate options
                for (let i = 0; i < 4; i++) {
                    const optionInput = document.getElementById(`edit_option_${i}`);
                    if (optionInput) {
                        optionInput.value = questionData.options[i] || '';
                    }
                }
            });
        });

        // Question limit validation
        function checkQuestionLimit() {
            const addButton = document.querySelector('[data-bs-target="#addQuestionModal"]');
            const warningDiv = document.getElementById('questionLimitWarning');
            const countSpan = document.getElementById('currentQuestionCount');

            if (currentQuestionCount >= maxQuestions) {
                if (addButton) {
                    addButton.disabled = true;
                    addButton.innerHTML = '<i class="fas fa-ban me-1"></i>Maximum Questions Reached';
                    addButton.classList.remove('btn-success');
                    addButton.classList.add('btn-secondary');
                }
                if (warningDiv) {
                    warningDiv.style.display = 'block';
                    warningDiv.classList.remove('alert-info');
                    warningDiv.classList.add('alert-warning');
                }
            } else if (currentQuestionCount >= maxQuestions - 2) {
                if (warningDiv) {
                    warningDiv.style.display = 'block';
                }
            }

            if (countSpan) {
                countSpan.textContent = currentQuestionCount;
            }
        }

        // Form validation for add question
        const addQuestionForm = document.querySelector('#addQuestionModal form');
        if (addQuestionForm) {
            addQuestionForm.addEventListener('submit', function(e) {
                if (currentQuestionCount >= maxQuestions) {
                    e.preventDefault();
                    alert('Maximum of 10 questions allowed per game.');
                    return false;
                }
            });
        }
    });
</script>

<style>
    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(214, 51, 132, 0.15);
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }

    .table th {
        border-top: none;
        font-weight: 600;
    }

    .modal-content {
        box-shadow: 0 10px 30px rgba(214, 51, 132, 0.2);
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #d63384;
        box-shadow: 0 0 0 0.2rem rgba(214, 51, 132, 0.25);
    }

    .badge {
        font-size: 11px;
        padding: 5px 10px;
        border-radius: 15px;
    }
</style>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('client.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/client/dashboard/questions.blade.php ENDPATH**/ ?>