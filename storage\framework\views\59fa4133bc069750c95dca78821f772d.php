<?php $__env->startSection('title', 'Manage Questions'); ?>
<?php $__env->startSection('page-title', 'Manage Trivia Questions'); ?>
<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item"><a href="<?php echo e(route('client.dashboard.minigames')); ?>">Minigames</a></li>
<li class="breadcrumb-item active">Questions</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <!-- Header -->
    <div class="col-12 mb-4">
        <div class="card"
            style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border: none; border-radius: 15px;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 style="color: #721c24; font-weight: 700; margin-bottom: 5px;">
                            <i class="fas fa-question-circle me-2" style="color: #d63384;"></i>
                            Question Management
                        </h2>
                        <p style="color: #721c24; margin-bottom: 0;">
                            Manage questions for: <strong><?php echo e($gameConfig->game_title); ?></strong>
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal"
                            data-bs-target="#addQuestionModal" style="border-radius: 20px;">
                            <i class="fas fa-plus me-1"></i>Add Question
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal"
                            data-bs-target="#bulkImportModal" style="border-radius: 20px;">
                            <i class="fas fa-upload me-1"></i>Bulk Import
                        </button>
                        <a href="<?php echo e(route('client.dashboard.minigames')); ?>" class="btn btn-outline-dark"
                            style="border-radius: 20px;">
                            <i class="fas fa-arrow-left me-1"></i>Back to Games
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Questions List -->
    <div class="col-12">
        <div class="card" style="border: 2px solid #f8d7da; border-radius: 15px;">
            <div class="card-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 13px 13px 0 0;">
                <h5 class="mb-0" style="color: #721c24; font-weight: 600;">
                    Questions (<?php echo e($questions->count()); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if($questions->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="color: #721c24;">#</th>
                                <th style="color: #721c24;">Question</th>
                                <th style="color: #721c24;">Options</th>
                                <th style="color: #721c24;">Correct Answer</th>
                                <th style="color: #721c24;">Points</th>
                                <th style="color: #721c24;">Time Limit</th>
                                <th style="color: #721c24;">Status</th>
                                <th style="color: #721c24;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($question->order ?: $index + 1); ?></td>
                                <td>
                                    <div style="max-width: 300px;">
                                        <?php echo e(Str::limit($question->question, 100)); ?>

                                    </div>
                                </td>
                                <td>
                                    <div style="max-width: 200px;">
                                        <?php $__currentLoopData = $question->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optIndex => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <small
                                            class="d-block <?php echo e($optIndex == $question->correct_answer ? 'text-success fw-bold' : 'text-muted'); ?>">
                                            <?php echo e(chr(65 + $optIndex)); ?>. <?php echo e(Str::limit($option, 30)); ?>

                                        </small>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success">
                                        <?php echo e(chr(65 + $question->correct_answer)); ?>

                                    </span>
                                </td>
                                <td><?php echo e($question->points); ?></td>
                                <td><?php echo e($question->time_limit); ?>s</td>
                                <td>
                                    <?php if($question->is_active): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary edit-question-btn"
                                            data-question="<?php echo e(json_encode($question)); ?>" data-bs-toggle="modal"
                                            data-bs-target="#editQuestionModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form action="<?php echo e(route('client.dashboard.questions.delete', $question)); ?>"
                                            method="POST" class="d-inline"
                                            onsubmit="return confirm('Are you sure you want to delete this question?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-question-circle fa-4x mb-3" style="color: #d63384; opacity: 0.3;"></i>
                    <h4 style="color: #721c24;">No Questions Yet</h4>
                    <p class="text-muted">Add your first trivia question to get started!</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#addQuestionModal"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); border: none; border-radius: 10px;">
                        <i class="fas fa-plus me-1"></i>Add First Question
                    </button>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Question Modal -->
<div class="modal fade" id="addQuestionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px;">
            <div class="modal-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 15px 15px 0 0;">
                <h5 class="modal-title" style="color: #721c24; font-weight: 600;">Add New Question</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?php echo e(route('client.dashboard.questions.store', $gameConfig)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="question" class="form-label"
                            style="color: #721c24; font-weight: 600;">Question</label>
                        <textarea class="form-control" id="question" name="question" rows="3" required
                            placeholder="Enter your trivia question here..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label" style="color: #721c24; font-weight: 600;">Answer Options</label>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" placeholder="Option A"
                                    required>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" placeholder="Option B"
                                    required>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" placeholder="Option C">
                            </div>
                            <div class="mb-3">
                                <input type="text" class="form-control" name="options[]" placeholder="Option D">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="correct_answer" class="form-label"
                                    style="color: #721c24; font-weight: 600;">Correct Answer</label>
                                <select class="form-select" id="correct_answer" name="correct_answer" required>
                                    <option value="0">A</option>
                                    <option value="1">B</option>
                                    <option value="2">C</option>
                                    <option value="3">D</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="points" class="form-label"
                                    style="color: #721c24; font-weight: 600;">Points</label>
                                <input type="number" class="form-control" id="points" name="points" value="100" min="1"
                                    max="1000" required>
                            </div>
                            <div class="mb-3">
                                <label for="time_limit" class="form-label"
                                    style="color: #721c24; font-weight: 600;">Time Limit (seconds)</label>
                                <input type="number" class="form-control" id="time_limit" name="time_limit" value="30"
                                    min="5" max="300" required>
                            </div>
                            <div class="mb-3">
                                <label for="order" class="form-label" style="color: #721c24; font-weight: 600;">Order
                                    (optional)</label>
                                <input type="number" class="form-control" id="order" name="order" min="0">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); border: none;">
                        <i class="fas fa-plus me-1"></i>Add Question
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Question Modal -->
<div class="modal fade" id="editQuestionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px;">
            <div class="modal-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 15px 15px 0 0;">
                <h5 class="modal-title" style="color: #721c24; font-weight: 600;">Edit Question</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editQuestionForm" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_question" class="form-label"
                            style="color: #721c24; font-weight: 600;">Question</label>
                        <textarea class="form-control" id="edit_question" name="question" rows="3" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label" style="color: #721c24; font-weight: 600;">Answer Options</label>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" id="edit_option_0"
                                    placeholder="Option A" required>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" id="edit_option_1"
                                    placeholder="Option B" required>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" name="options[]" id="edit_option_2"
                                    placeholder="Option C">
                            </div>
                            <div class="mb-3">
                                <input type="text" class="form-control" name="options[]" id="edit_option_3"
                                    placeholder="Option D">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_correct_answer" class="form-label"
                                    style="color: #721c24; font-weight: 600;">Correct Answer</label>
                                <select class="form-select" id="edit_correct_answer" name="correct_answer" required>
                                    <option value="0">A</option>
                                    <option value="1">B</option>
                                    <option value="2">C</option>
                                    <option value="3">D</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="edit_points" class="form-label"
                                    style="color: #721c24; font-weight: 600;">Points</label>
                                <input type="number" class="form-control" id="edit_points" name="points" min="1"
                                    max="1000" required>
                            </div>
                            <div class="mb-3">
                                <label for="edit_time_limit" class="form-label"
                                    style="color: #721c24; font-weight: 600;">Time Limit (seconds)</label>
                                <input type="number" class="form-control" id="edit_time_limit" name="time_limit" min="5"
                                    max="300" required>
                            </div>
                            <div class="mb-3">
                                <label for="edit_order" class="form-label"
                                    style="color: #721c24; font-weight: 600;">Order</label>
                                <input type="number" class="form-control" id="edit_order" name="order" min="0">
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active"
                                        value="1">
                                    <label class="form-check-label" for="edit_is_active" style="color: #721c24;">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); border: none;">
                        <i class="fas fa-save me-1"></i>Update Question
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Import Modal -->
<div class="modal fade" id="bulkImportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px;">
            <div class="modal-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 15px 15px 0 0;">
                <h5 class="modal-title" style="color: #721c24; font-weight: 600;">Bulk Import Questions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?php echo e(route('client.dashboard.questions.bulk-import', $gameConfig)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="questions_json" class="form-label"
                            style="color: #721c24; font-weight: 600;">Questions JSON</label>
                        <textarea class="form-control" id="questions_json" name="questions_json" rows="10" required
                            placeholder='[
  {
    "question": "What is the capital of France?",
    "options": ["London", "Berlin", "Paris", "Madrid"],
    "correct_answer": 2,
    "points": 100,
    "time_limit": 30
  },
  {
    "question": "Which planet is closest to the Sun?",
    "options": ["Venus", "Mercury", "Earth", "Mars"],
    "correct_answer": 1,
    "points": 100,
    "time_limit": 30
  }
]'></textarea>
                        <small class="text-muted">
                            Enter questions in JSON format. Each question should have: question, options (array),
                            correct_answer (0-3), points, and time_limit.
                        </small>
                    </div>

                    <div class="alert alert-info">
                        <h6 style="color: #721c24;">JSON Format Example:</h6>
                        <pre style="font-size: 12px; margin-bottom: 0;">[
  {
    "question": "Your question here?",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "correct_answer": 0,
    "points": 100,
    "time_limit": 30,
    "is_active": true
  }
]</pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); border: none;">
                        <i class="fas fa-upload me-1"></i>Import Questions
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Handle edit question modal
    const editButtons = document.querySelectorAll('.edit-question-btn');
    const editForm = document.getElementById('editQuestionForm');

    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const questionData = JSON.parse(this.getAttribute('data-question'));

            // Set form action
            editForm.action = `/client/questions/${questionData.id}`;

            // Populate form fields
            document.getElementById('edit_question').value = questionData.question;
            document.getElementById('edit_correct_answer').value = questionData.correct_answer;
            document.getElementById('edit_points').value = questionData.points;
            document.getElementById('edit_time_limit').value = questionData.time_limit;
            document.getElementById('edit_order').value = questionData.order || '';
            document.getElementById('edit_is_active').checked = questionData.is_active;

            // Populate options
            for (let i = 0; i < 4; i++) {
                const optionInput = document.getElementById(`edit_option_${i}`);
                if (optionInput) {
                    optionInput.value = questionData.options[i] || '';
                }
            }
        });
    });
});
</script>

<style>
    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(214, 51, 132, 0.15);
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }

    .table th {
        border-top: none;
        font-weight: 600;
    }

    .modal-content {
        box-shadow: 0 10px 30px rgba(214, 51, 132, 0.2);
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #d63384;
        box-shadow: 0 0 0 0.2rem rgba(214, 51, 132, 0.25);
    }

    .badge {
        font-size: 11px;
        padding: 5px 10px;
        border-radius: 15px;
    }
</style>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('client.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/client/dashboard/questions.blade.php ENDPATH**/ ?>