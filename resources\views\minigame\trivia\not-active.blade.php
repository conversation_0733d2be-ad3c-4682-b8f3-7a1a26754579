<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $gameConfig->game_title }} - Not Active</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #f3e8ff 100%);
            min-height: 100vh;
        }
        
        .floral-bg {
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(251, 207, 232, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(251, 207, 232, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(251, 207, 232, 0.2) 0%, transparent 50%);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(251, 207, 232, 0.3);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(251, 207, 232, 0.2);
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="floral-bg flex items-center justify-center min-h-screen p-4">
    <div class="w-full max-w-2xl mx-auto">
        <div class="card p-8 md:p-12 text-center">
            @if($gameConfig->is_done)
                <!-- Game Completed -->
                <div class="mb-6">
                    <i class="fas fa-check-circle text-6xl text-blue-500 mb-4"></i>
                    <h1 class="text-4xl md:text-5xl font-bold text-blue-600 mb-4" style="font-family: 'Playfair Display', serif;">
                        Game Completed!
                    </h1>
                    <p class="text-lg text-gray-600 mb-2">{{ $gameConfig->game_title }}</p>
                    <p class="text-md text-gray-500 mb-6">{{ $client->client_name }} Wedding Celebration</p>
                </div>
                
                <div class="bg-blue-50 border-2 border-blue-200 rounded-lg p-6 mb-6">
                    <h3 class="text-xl font-bold text-blue-800 mb-2">
                        <i class="fas fa-trophy mr-2"></i>Game Has Ended
                    </h3>
                    <p class="text-blue-600">
                        This trivia game has been completed. Thank you for your interest in participating!
                    </p>
                </div>
                
                <div class="space-y-4">
                    <a href="{{ route('minigame.leaderboard', ['client_id' => $client->id, 'game_config_id' => $gameConfig->id]) }}" 
                       class="inline-block px-8 py-3 bg-blue-500 text-white font-bold rounded-full text-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-trophy mr-2"></i>View Final Results
                    </a>
                </div>
                
            @else
                <!-- Game Not Started -->
                <div class="mb-6">
                    <i class="fas fa-pause-circle text-6xl text-gray-500 mb-4 pulse"></i>
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-600 mb-4" style="font-family: 'Playfair Display', serif;">
                        Game Not Active
                    </h1>
                    <p class="text-lg text-gray-600 mb-2">{{ $gameConfig->game_title }}</p>
                    <p class="text-md text-gray-500 mb-6">{{ $client->client_name }} Wedding Celebration</p>
                </div>
                
                <div class="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-6 mb-6">
                    <h3 class="text-xl font-bold text-yellow-800 mb-2">
                        <i class="fas fa-clock mr-2"></i>Coming Soon!
                    </h3>
                    <p class="text-yellow-600">
                        This trivia game hasn't started yet. Please check back later or wait for the host to begin the game.
                    </p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="bg-pink-50 border border-pink-200 rounded-lg p-4">
                        <h4 class="font-bold text-pink-800 mb-1">Game Details</h4>
                        <p class="text-sm text-pink-600">Top {{ $gameConfig->top_players }} winners will be selected</p>
                    </div>
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                        <h4 class="font-bold text-purple-800 mb-1">How to Play</h4>
                        <p class="text-sm text-purple-600">Answer questions about the happy couple</p>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <button onclick="location.reload()" 
                            class="inline-block px-8 py-3 bg-pink-500 text-white font-bold rounded-full text-lg hover:bg-pink-600 transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh Page
                    </button>
                    <p class="text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>
                        This page will automatically refresh when the game becomes available
                    </p>
                </div>
            @endif
            
            <!-- Additional Info -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <p class="text-sm text-gray-500">
                    <i class="fas fa-heart text-pink-500 mr-1"></i>
                    Celebrating {{ $client->client_name }}'s Special Day
                </p>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh every 30 seconds if game is not done
        @if(!$gameConfig->is_done)
        setInterval(function() {
            location.reload();
        }, 30000);
        @endif
        
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to cards
            const cards = document.querySelectorAll('.bg-yellow-50, .bg-pink-50, .bg-purple-50, .bg-blue-50');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
