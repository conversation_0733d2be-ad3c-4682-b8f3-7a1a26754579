@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-plus-circle me-2"></i>Create New Game Configuration
                </h5>
                <small class="text-muted">Set up a new game configuration for a client</small>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.game-configs.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <!-- Client Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="client_id" class="form-label">Client <span class="text-danger">*</span></label>
                            <select class="form-control @error('client_id') is-invalid @enderror" 
                                    id="client_id" name="client_id" required>
                                <option value="">Select Client</option>
                                @foreach($clients as $client)
                                    <option value="{{ $client->id }}" {{ old('client_id') == $client->id ? 'selected' : '' }}>
                                        {{ $client->client_name }} ({{ $client->unique_url }})
                                    </option>
                                @endforeach
                            </select>
                            <small class="form-text text-muted">Choose the client for this game configuration</small>
                            @error('client_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Game Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="game_id" class="form-label">Game <span class="text-danger">*</span></label>
                            <select class="form-control @error('game_id') is-invalid @enderror" 
                                    id="game_id" name="game_id" required>
                                <option value="">Select Game</option>
                                @foreach($games as $game)
                                    <option value="{{ $game->id }}" {{ old('game_id') == $game->id ? 'selected' : '' }}>
                                        {{ $game->display_name }} ({{ $game->minigame_name }})
                                    </option>
                                @endforeach
                            </select>
                            <small class="form-text text-muted">Choose the game type for this configuration</small>
                            @error('game_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Game Title -->
                    <div class="mb-3">
                        <label for="game_title" class="form-label">Game Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('game_title') is-invalid @enderror" 
                               id="game_title" name="game_title" value="{{ old('game_title') }}" 
                               placeholder="Enter a custom title for this game instance" required>
                        <small class="form-text text-muted">This title will be displayed to guests playing the game</small>
                        @error('game_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Top Players -->
                    <div class="mb-3">
                        <label for="top_players" class="form-label">Top Players to Display <span class="text-danger">*</span></label>
                        <input type="number" class="form-control @error('top_players') is-invalid @enderror" 
                               id="top_players" name="top_players" value="{{ old('top_players', 10) }}" 
                               min="1" max="50" required>
                        <small class="form-text text-muted">Number of top players to show on the leaderboard (1-50)</small>
                        @error('top_players')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Game Status -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0 heading-font">
                                <i class="fas fa-toggle-on me-2"></i>Game Status
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_starting" name="is_starting" 
                                               value="1" {{ old('is_starting') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_starting">
                                            <strong>Start Game Immediately</strong>
                                            <small class="d-block text-muted">Allow guests to play this game right away</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_done" name="is_done" 
                                               value="1" {{ old('is_done') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_done">
                                            <strong>Mark as Completed</strong>
                                            <small class="d-block text-muted">Game has finished and no new players can join</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Configuration Guidelines -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0 heading-font">
                                <i class="fas fa-lightbulb me-2"></i>Configuration Guidelines
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Best Practices:</h6>
                                    <ul class="small text-muted">
                                        <li>Use descriptive game titles that guests will understand</li>
                                        <li>Set appropriate leaderboard size for your event</li>
                                        <li>Test the configuration before the event</li>
                                        <li>Ensure you have questions ready for trivia games</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">Status Tips:</h6>
                                    <ul class="small text-muted">
                                        <li>Leave "Start Game" unchecked to prepare first</li>
                                        <li>You can start/stop games later from the management panel</li>
                                        <li>Completed games show final rankings</li>
                                        <li>Only one configuration per client-game combination</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.game-configs.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Configurations
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Create Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
/* Form specific styles */
.form-check-input:checked {
    background-color: var(--primary-pink) !important;
    border-color: var(--primary-pink) !important;
}

.form-check-input:focus {
    border-color: var(--primary-pink) !important;
    box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25) !important;
}

.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    display: block;
}

/* Enhanced form styling */
.form-control:focus {
    border-color: var(--primary-pink);
    box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25);
}

.form-check-label {
    cursor: pointer;
}

.form-check-label strong {
    color: var(--text-dark);
}

/* Card styling */
.card .card-header h6 {
    color: var(--primary-pink);
}
</style>

<script>
// Auto-generate game title based on selections
document.addEventListener('DOMContentLoaded', function() {
    const clientSelect = document.getElementById('client_id');
    const gameSelect = document.getElementById('game_id');
    const gameTitleInput = document.getElementById('game_title');
    
    function updateGameTitle() {
        const clientText = clientSelect.options[clientSelect.selectedIndex]?.text || '';
        const gameText = gameSelect.options[gameSelect.selectedIndex]?.text || '';
        
        if (clientText && gameText && !gameTitleInput.value) {
            const clientName = clientText.split(' (')[0]; // Remove URL part
            const gameName = gameText.split(' (')[0]; // Remove minigame_name part
            gameTitleInput.value = `${clientName} - ${gameName}`;
        }
    }
    
    clientSelect.addEventListener('change', updateGameTitle);
    gameSelect.addEventListener('change', updateGameTitle);
    
    // Prevent conflicting status checkboxes
    const startingCheckbox = document.getElementById('is_starting');
    const doneCheckbox = document.getElementById('is_done');
    
    startingCheckbox.addEventListener('change', function() {
        if (this.checked) {
            doneCheckbox.checked = false;
        }
    });
    
    doneCheckbox.addEventListener('change', function() {
        if (this.checked) {
            startingCheckbox.checked = false;
        }
    });
});
</script>
@endsection
