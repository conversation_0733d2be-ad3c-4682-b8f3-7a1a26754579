body {
    font-family: "Poppins", sans-serif;
    background-color: #fce7f3;
    /* Soft pastel pink background */
    color: #4a4a4a;
    overflow: hidden;
    /* Hide overflow from animations */
}

h1,
h2,
h3 {
    font-family: "Playfair Display", serif;
}

.screen {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.screen.active {
    display: block;
}

.card {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.timer-bar-container {
    width: 100%;
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
}

.timer-bar {
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, #a78bfa, #c4b5fd);
    transition: width 0.1s linear;
}

/* Kahoot-style answer buttons */
.answer-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: white;
    font-weight: 600;
    border-radius: 8px;
    padding: 1rem;
    min-height: 80px;
    box-shadow: 0 5px 0 0 rgba(0, 0, 0, 0.2);
    position: relative;
}

.answer-btn:hover {
    filter: brightness(1.1);
    transform: translateY(-2px);
    box-shadow: 0 7px 0 0 rgba(0, 0, 0, 0.2);
}

.answer-btn:active {
    transform: translateY(2px);
    box-shadow: 0 3px 0 0 rgba(0, 0, 0, 0.2);
}

.answer-shape {
    width: 40px;
    height: 40px;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Correct / Incorrect animations */
@keyframes pulse-green {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.7);
    }

    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 20px rgba(74, 222, 128, 0);
    }

    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(74, 222, 128, 0);
    }
}

.correct-answer {
    animation: pulse-green 1s;
    opacity: 1 !important;
    border: 3px solid white;
}

@keyframes pulse-red-glow {
    0% {
        box-shadow: 0 5px 0 0 rgba(0, 0, 0, 0.2),
            0 0 0 0 rgba(248, 113, 113, 0.7);
    }

    70% {
        box-shadow: 0 5px 0 0 rgba(0, 0, 0, 0.2),
            0 0 0 20px rgba(248, 113, 113, 0);
    }

    100% {
        box-shadow: 0 5px 0 0 rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(248, 113, 113, 0);
    }
}

.incorrect-answer-selected {
    animation: pulse-red-glow 1s;
}

.incorrect-answer {
    opacity: 0.5 !important;
}

/* Score popup animation */
#score-popup {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
    /* 48px */
    font-weight: 700;
    color: #f59e0b;
    /* Amber/gold color */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    z-index: 100;
    pointer-events: none;
    /* So it doesn't block clicks */
    opacity: 0;
}

@keyframes score-pop-up {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 1;
    }

    60% {
        transform: translate(-50%, -150%) scale(1.2);
        /* Move up */
        opacity: 1;
    }

    100% {
        transform: translate(-50%, -200%) scale(1.2);
        /* Continue moving up */
        opacity: 0;
    }
}

#score-popup.show {
    animation: score-pop-up 1.2s ease-out forwards;
}

/* Screen Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.98);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes countdown-pop {
    0% {
        opacity: 0;
        transform: scale(1.5);
    }

    50% {
        opacity: 1;
        transform: scale(1);
    }

    100% {
        opacity: 0;
        transform: scale(0.5);
    }
}

#countdown-number {
    font-size: 10rem;
    font-weight: 700;
    color: #8b5cf6;
    animation: countdown-pop 1s ease-in-out infinite;
}

/* Animated Floral Background */
.background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 0;
}

#main-container {
    position: relative;
    z-index: 1;
}

.flowers {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    pointer-events: none;
}

.flowers li {
    position: absolute;
    display: block;
    list-style: none;
    animation: float-up 25s linear infinite;
    bottom: -150px;
}

@keyframes float-up {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.7;
    }

    100% {
        transform: translateY(-120vh) rotate(720deg);
        opacity: 0;
    }
}
