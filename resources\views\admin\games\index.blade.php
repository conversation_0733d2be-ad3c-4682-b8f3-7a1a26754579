@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-gamepad me-2"></i>Games Management
                </h5>
                <a href="{{ route('admin.games.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>Add New Game
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Game Name</th>
                                <th>Display Name</th>
                                <th>View Path</th>
                                <th>Configurations</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($games as $game)
                            <tr>
                                <td>{{ $loop->iteration + ($games->currentPage() - 1) * $games->perPage() }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="game-icon me-2">
                                            <i class="fas fa-gamepad"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $game->minigame_name }}</h6>
                                            @if($game->description)
                                                <small class="text-muted">{{ Str::limit($game->description, 50) }}</small>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-semibold">{{ $game->display_name }}</span>
                                </td>
                                <td>
                                    <code class="game-path">{{ $game->view_path }}</code>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ $game->game_configs_count }} configs
                                    </span>
                                </td>
                                <td>
                                    @if($game->is_active)
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Active
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times me-1"></i>Inactive
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.games.show', $game) }}" class="btn btn-sm btn-outline-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.games.edit', $game) }}" class="btn btn-sm btn-outline-warning" title="Edit Game">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.games.destroy', $game) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this game?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Game">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div style="color: #6c757d;">
                                        <i class="fas fa-gamepad fa-3x mb-3" style="opacity: 0.3;"></i>
                                        <p class="mb-0">No games found</p>
                                        <small>Create your first game to get started</small>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                
                @if($games->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $games->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
/* Games specific styles */
.game-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.game-path {
    background: var(--light-pink);
    color: var(--text-dark);
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.8rem;
}

.btn-group .btn {
    margin-right: 2px;
}
</style>
@endsection
