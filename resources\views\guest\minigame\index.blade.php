<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $client->client_name }} - Wedding Minigames</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-pink: #ec4899;
            --secondary-pink: #f8d7da;
            --light-pink: #fdf2f8;
            --accent-pink: #be185d;
            --text-dark: #721c24;
            --text-light: #6b7280;
            --white: #ffffff;
            --shadow: 0 8px 32px rgba(251, 207, 232, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #f3e8ff 100%);
            min-height: 100vh;
            position: relative;
        }

        /* Floating florals background */
        .floating-florals {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floral-element {
            position: absolute;
            font-size: 2rem;
            opacity: 0.2;
            animation: float 8s ease-in-out infinite;
        }

        .floral-1 { top: 10%; left: 10%; animation-delay: 0s; }
        .floral-2 { top: 20%; right: 15%; animation-delay: 2s; }
        .floral-3 { top: 60%; left: 5%; animation-delay: 4s; }
        .floral-4 { bottom: 20%; right: 10%; animation-delay: 6s; }
        .floral-5 { bottom: 10%; left: 20%; animation-delay: 8s; }
        .floral-6 { top: 40%; right: 5%; animation-delay: 10s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(15deg); }
        }

        .main-container {
            position: relative;
            z-index: 2;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(251, 207, 232, 0.3);
            border-radius: 25px;
            padding: 40px;
            box-shadow: var(--shadow);
            max-width: 800px;
            width: 100%;
            text-align: center;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .welcome-title {
            font-family: 'Playfair Display', serif;
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-pink);
            margin-bottom: 15px;
        }

        .welcome-subtitle {
            color: var(--text-light);
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .client-name {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            color: var(--accent-pink);
            margin-bottom: 40px;
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .game-card {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(251, 207, 232, 0.4);
            border-radius: 20px;
            padding: 30px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .game-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        }

        .game-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(251, 207, 232, 0.4);
            text-decoration: none;
            color: inherit;
        }

        .game-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }

        .game-title {
            font-family: 'Playfair Display', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 10px;
        }

        .game-description {
            color: var(--text-light);
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .play-button {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .play-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(236, 72, 153, 0.4);
            color: white;
            text-decoration: none;
        }

        .no-games {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-light);
        }

        .no-games i {
            font-size: 4rem;
            opacity: 0.3;
            margin-bottom: 20px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .welcome-card {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .welcome-title {
                font-size: 2.5rem;
            }
            
            .client-name {
                font-size: 1.5rem;
            }
            
            .games-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .game-card {
                padding: 25px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Floating florals -->
    <div class="floating-florals">
        <div class="floral-element floral-1">🌸</div>
        <div class="floral-element floral-2">🌺</div>
        <div class="floral-element floral-3">🌷</div>
        <div class="floral-element floral-4">🌹</div>
        <div class="floral-element floral-5">🌻</div>
        <div class="floral-element floral-6">🌼</div>
    </div>

    <div class="main-container">
        <div class="welcome-card">
            <h1 class="welcome-title">
                <i class="fas fa-heart me-3"></i>Welcome to Our Wedding
            </h1>
            <p class="welcome-subtitle">Join us in celebrating love with fun interactive games!</p>
            
            <h2 class="client-name">{{ $client->client_name }}</h2>

            @if($activeGames->count() > 0)
                <div class="games-grid">
                    @foreach($activeGames as $gameConfig)
                        <a href="{{ route('guest.minigame.trivia', [$client->id, $client->unique_url]) }}" class="game-card">
                            <div class="game-icon">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <h3 class="game-title">{{ $gameConfig->game_title }}</h3>
                            <p class="game-description">
                                Test your knowledge about the happy couple with our fun trivia questions! 
                                Compete with other guests and see who knows them best.
                            </p>
                            <span class="play-button">
                                <i class="fas fa-play me-2"></i>Play Now
                            </span>
                        </a>
                    @endforeach
                </div>
            @else
                <div class="no-games">
                    <i class="fas fa-gamepad"></i>
                    <h3>No Games Available</h3>
                    <p>Games will be available soon. Please check back later!</p>
                </div>
            @endif
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add floating animation to floral elements
        document.addEventListener('DOMContentLoaded', function() {
            const floralElements = document.querySelectorAll('.floral-element');
            
            floralElements.forEach((element, index) => {
                // Add random movement
                setInterval(() => {
                    const randomX = Math.random() * 20 - 10;
                    const randomY = Math.random() * 20 - 10;
                    element.style.transform = `translate(${randomX}px, ${randomY}px) rotate(${Math.random() * 20 - 10}deg)`;
                }, 4000 + Math.random() * 2000);
            });
        });
    </script>
</body>
</html>
