<?php $__env->startSection('content'); ?>
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-cogs me-2"></i>Game Configuration Details
                </h5>
                <small class="text-muted">View game configuration information and statistics</small>
            </div>
            <div class="card-body">
                <!-- Configuration Information -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-info-circle me-2"></i>Configuration Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Client:</label>
                                        <div class="d-flex align-items-center">
                                            <div class="client-avatar me-2">
                                                <?php echo e(substr($gameConfig->client->client_name, 0, 1)); ?>

                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($gameConfig->client->client_name); ?></h6>
                                                <small class="text-muted"><?php echo e($gameConfig->client->unique_url); ?></small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Game Type:</label>
                                        <div class="game-info">
                                            <span class="badge bg-primary fs-6 p-2"><?php echo e($gameConfig->game->display_name); ?></span>
                                            <small class="d-block text-muted mt-1"><?php echo e($gameConfig->game->minigame_name); ?></small>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Game Title:</label>
                                        <div class="game-title">
                                            <h6 class="mb-0"><?php echo e($gameConfig->game_title); ?></h6>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Top Players Display:</label>
                                        <div class="top-players">
                                            <span class="badge bg-info fs-6 p-2"><?php echo e($gameConfig->top_players); ?> players</span>
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-3">
                                        <label class="form-label fw-bold">Current Status:</label>
                                        <div class="status-display">
                                            <?php if($gameConfig->is_starting): ?>
                                                <span class="badge bg-success fs-6 p-2">
                                                    <i class="fas fa-play me-1"></i>Running
                                                </span>
                                                <small class="text-muted ms-2">Game is active and accepting new players</small>
                                            <?php elseif($gameConfig->is_done): ?>
                                                <span class="badge bg-secondary fs-6 p-2">
                                                    <i class="fas fa-check me-1"></i>Completed
                                                </span>
                                                <small class="text-muted ms-2">Game has finished, no new players can join</small>
                                            <?php else: ?>
                                                <span class="badge bg-warning fs-6 p-2">
                                                    <i class="fas fa-clock me-1"></i>Pending
                                                </span>
                                                <small class="text-muted ms-2">Game is configured but not yet started</small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Leaderboard -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-trophy me-2"></i>Current Leaderboard
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if($leaderboard->count() > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Rank</th>
                                                    <th>Player</th>
                                                    <th>Score</th>
                                                    <th>Completed</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $leaderboard; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $score): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <?php if($score->rank <= 3): ?>
                                                            <span class="rank-badge rank-<?php echo e($score->rank); ?>">
                                                                <?php if($score->rank == 1): ?> 🥇
                                                                <?php elseif($score->rank == 2): ?> 🥈
                                                                <?php else: ?> 🥉
                                                                <?php endif; ?>
                                                                <?php echo e($score->rank); ?>

                                                            </span>
                                                        <?php else: ?>
                                                            <span class="rank-badge"><?php echo e($score->rank); ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="player-info">
                                                            <h6 class="mb-0"><?php echo e($score->guest->name); ?></h6>
                                                            <small class="text-muted"><?php echo e($score->guest->email ?? 'No email'); ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="score-badge"><?php echo e(number_format($score->score)); ?></span>
                                                    </td>
                                                    <td>
                                                        <small class="text-muted"><?php echo e($score->completed_at->format('M d, Y H:i')); ?></small>
                                                    </td>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-trophy fa-3x text-muted mb-3" style="opacity: 0.3;"></i>
                                        <p class="text-muted mb-0">No players have completed this game yet</p>
                                        <small>Leaderboard will appear once guests start playing</small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- Game Statistics -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-chart-bar me-2"></i>Game Statistics
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Total Participants:</span>
                                        <span class="fw-bold"><?php echo e($gameConfig->guestScores->count()); ?></span>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Completed Games:</span>
                                        <span class="fw-bold"><?php echo e($gameConfig->guestScores->where('is_completed', true)->count()); ?></span>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">In Progress:</span>
                                        <span class="fw-bold"><?php echo e($gameConfig->guestScores->where('is_completed', false)->count()); ?></span>
                                    </div>
                                </div>
                                <?php if($gameConfig->guestScores->where('is_completed', true)->count() > 0): ?>
                                    <div class="stat-item mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span class="text-muted">Average Score:</span>
                                            <span class="fw-bold"><?php echo e(number_format($gameConfig->guestScores->where('is_completed', true)->avg('score'), 1)); ?></span>
                                        </div>
                                    </div>
                                    <div class="stat-item mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span class="text-muted">Highest Score:</span>
                                            <span class="fw-bold"><?php echo e(number_format($gameConfig->guestScores->where('is_completed', true)->max('score'))); ?></span>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="stat-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Created:</span>
                                        <span class="fw-bold"><?php echo e($gameConfig->created_at->format('M d, Y')); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Game Actions -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-tools me-2"></i>Game Actions
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if(!$gameConfig->is_starting && !$gameConfig->is_done): ?>
                                    <form action="<?php echo e(route('admin.game-configs.start', $gameConfig)); ?>" method="POST" class="mb-2">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-play me-1"></i>Start Game
                                        </button>
                                    </form>
                                <?php endif; ?>

                                <?php if($gameConfig->is_starting): ?>
                                    <form action="<?php echo e(route('admin.game-configs.stop', $gameConfig)); ?>" method="POST" class="mb-2">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="fas fa-stop me-1"></i>Stop Game
                                        </button>
                                    </form>
                                <?php endif; ?>

                                <?php if($gameConfig->game->minigame_name === 'trivia'): ?>
                                    <a href="<?php echo e(route('admin.trivia-questions.index')); ?>?config=<?php echo e($gameConfig->id); ?>" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-question-circle me-1"></i>Manage Questions
                                    </a>
                                <?php endif; ?>

                                <form action="<?php echo e(route('admin.game-configs.reset', $gameConfig)); ?>" method="POST" class="mb-2" 
                                      onsubmit="return confirm('Are you sure? This will delete all player scores!')">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-outline-danger w-100">
                                        <i class="fas fa-redo me-1"></i>Reset Game
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-between mt-4">
                    <a href="<?php echo e(route('admin.game-configs.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Configurations
                    </a>
                    <div>
                        <a href="<?php echo e(route('admin.game-configs.edit', $gameConfig)); ?>" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i>Edit Configuration
                        </a>
                        <form action="<?php echo e(route('admin.game-configs.destroy', $gameConfig)); ?>" method="POST" class="d-inline" 
                              onsubmit="return confirm('Are you sure you want to delete this configuration?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Delete Configuration
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Configuration display styles */
.client-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--secondary-pink) 0%, #f5c6cb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    font-weight: 600;
    font-size: 16px;
}

.game-info .badge, .top-players .badge, .status-display .badge {
    font-size: 0.9rem;
}

/* Leaderboard styles */
.rank-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--light-pink);
    color: var(--text-dark);
    font-weight: 600;
    font-size: 14px;
}

.rank-badge.rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
}

.rank-badge.rank-2 {
    background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
}

.rank-badge.rank-3 {
    background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
}

.score-badge {
    background: var(--primary-pink);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
}

.player-info h6 {
    color: var(--text-dark);
}

/* Statistics styling */
.stat-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(251, 207, 232, 0.2);
}

.stat-item:last-child {
    border-bottom: none;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/admin/game-configs/show.blade.php ENDPATH**/ ?>