<?php

namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Course;
use Illuminate\Support\Facades\Auth;

class InstructorController extends Controller
{
    public function index()
    {
        // Pastikan pengguna sudah login
        if (!Auth::check()) {
            return redirect()->route('home.sign-in');
        }

        // Ambil data pengguna yang sedang login
        $user = Auth::user();

        $courses = Course::where(['instructor_id' => auth()->user()->id])->with(['instructor.detailInstructor', 'reviews.user', 'enrolled', 'category', 'lessons'])->get();

        return view('instructor.index', compact('courses', 'user'));
    }
}
