<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TriviaQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'game_config_id',
        'question',
        'options',
        'correct_answer',
        'points',
        'time_limit',
        'order',
        'is_active',
    ];

    protected $casts = [
        'options' => 'array',
        'is_active' => 'boolean',
    ];

    public function gameConfig()
    {
        return $this->belongsTo(GameConfig::class);
    }

    public function getCorrectAnswerTextAttribute()
    {
        return $this->options[$this->correct_answer] ?? null;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }
}
