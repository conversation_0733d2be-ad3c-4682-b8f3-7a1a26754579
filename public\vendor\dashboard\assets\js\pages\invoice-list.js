"use strict";function floatchart(){new simpleDatatables.DataTable("#pc-dt-simple-1",{sortable:!1}),new simpleDatatables.DataTable("#pc-dt-simple-2",{sortable:!1}),new simpleDatatables.DataTable("#pc-dt-simple-3",{sortable:!1}),new simpleDatatables.DataTable("#pc-dt-simple-4",{sortable:!1});new ApexCharts(document.querySelector("#total-invoice-1-chart"),{chart:{type:"area",height:55,sparkline:{enabled:!0}},colors:["#2ca87f"],fill:{type:"gradient",gradient:{shadeIntensity:1,type:"vertical",inverseColors:!1,opacityFrom:.5,opacityTo:0}},stroke:{curve:"smooth",width:2},series:[{data:[0,20,10,45,30,55,20,30]}],tooltip:{fixed:{enabled:!1},x:{show:!1},y:{title:{formatter:function(e){return"Ticket "}}},marker:{show:!1}}}).render(),new ApexCharts(document.querySelector("#total-invoice-2-chart"),{chart:{type:"area",height:55,sparkline:{enabled:!0}},colors:["#e58a00"],fill:{type:"gradient",gradient:{shadeIntensity:1,type:"vertical",inverseColors:!1,opacityFrom:.5,opacityTo:0}},stroke:{curve:"smooth",width:2},series:[{data:[30,20,55,30,45,10,20,0]}],tooltip:{fixed:{enabled:!1},x:{show:!1},y:{title:{formatter:function(e){return"Ticket "}}},marker:{show:!1}}}).render(),new ApexCharts(document.querySelector("#total-invoice-3-chart"),{chart:{type:"area",height:55,sparkline:{enabled:!0}},colors:["#dc2626"],fill:{type:"gradient",gradient:{shadeIntensity:1,type:"vertical",inverseColors:!1,opacityFrom:.5,opacityTo:0}},stroke:{curve:"smooth",width:2},series:[{data:[0,20,10,45,30,55,20,30]}],tooltip:{fixed:{enabled:!1},x:{show:!1},y:{title:{formatter:function(e){return"Ticket "}}},marker:{show:!1}}}).render()}document.addEventListener("DOMContentLoaded",function(){setTimeout(function(){floatchart()},500)});