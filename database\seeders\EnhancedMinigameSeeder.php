<?php

namespace Database\Seeders;

use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\Guest;
use App\Models\TriviaQuestion;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class EnhancedMinigameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test client user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Client',
                'username' => 'testclient',
                'password' => Hash::make('password'),
                'role' => 'client',
            ]
        );

        // Create a test client
        $client = Client::firstOrCreate(
            ['user_id' => $user->id],
            [
                'client_name' => 'Smith Wedding 2024',
                'unique_url' => 'smith-wedding-2024',
                'is_have_minigame' => true,
            ]
        );

        // Create test guests
        $guests = [
            ['name' => '<PERSON>', 'unique_url' => 'john-doe-guest'],
            ['name' => '<PERSON>', 'unique_url' => 'jane-smith-guest'],
            ['name' => '<PERSON>', 'unique_url' => 'bob-johnson-guest'],
            ['name' => 'Alice <PERSON>', 'unique_url' => 'alice-brown-guest'],
            ['name' => 'Charlie Wilson', 'unique_url' => 'charlie-wilson-guest'],
        ];

        foreach ($guests as $guestData) {
            Guest::firstOrCreate(
                [
                    'client_id' => $client->id,
                    'unique_url' => $guestData['unique_url']
                ],
                [
                    'name' => $guestData['name'],
                ]
            );
        }

        // Ensure trivia game exists
        $triviaGame = Game::firstOrCreate(
            ['minigame_name' => 'trivia'],
            [
                'display_name' => 'Trivia Game',
                'description' => 'Interactive trivia game for wedding guests',
            ]
        );

        // Create game config
        $gameConfig = GameConfig::firstOrCreate(
            [
                'client_id' => $client->id,
                'game_id' => $triviaGame->id,
            ],
            [
                'game_title' => 'Wedding Trivia Challenge',
                'top_players' => 10,
                'is_starting' => false,
                'is_done' => false,
                'config_data' => [
                    'default_time_limit' => 30,
                    'default_points' => 100,
                    'randomize_questions' => false,
                    'show_correct_answer' => true,
                ],
            ]
        );

        // Create sample trivia questions
        $sampleQuestions = [
            [
                'question' => 'Where did the bride and groom first meet?',
                'options' => ['At work', 'In college', 'Through friends', 'Online'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 30,
                'order' => 1,
            ],
            [
                'question' => 'What is the bride\'s favorite color?',
                'options' => ['Pink', 'Blue', 'Purple', 'Green'],
                'correct_answer' => 0,
                'points' => 100,
                'time_limit' => 25,
                'order' => 2,
            ],
            [
                'question' => 'How many years have they been together?',
                'options' => ['2 years', '3 years', '4 years', '5 years'],
                'correct_answer' => 2,
                'points' => 150,
                'time_limit' => 35,
                'order' => 3,
            ],
            [
                'question' => 'What is their favorite vacation destination?',
                'options' => ['Beach', 'Mountains', 'City', 'Countryside'],
                'correct_answer' => 0,
                'points' => 100,
                'time_limit' => 30,
                'order' => 4,
            ],
            [
                'question' => 'What month did they get engaged?',
                'options' => ['January', 'March', 'June', 'December'],
                'correct_answer' => 2,
                'points' => 200,
                'time_limit' => 40,
                'order' => 5,
            ],
            [
                'question' => 'What is the groom\'s favorite hobby?',
                'options' => ['Reading', 'Gaming', 'Sports', 'Cooking'],
                'correct_answer' => 2,
                'points' => 100,
                'time_limit' => 30,
                'order' => 6,
            ],
            [
                'question' => 'Where are they going for their honeymoon?',
                'options' => ['Paris', 'Bali', 'Tokyo', 'New York'],
                'correct_answer' => 1,
                'points' => 150,
                'time_limit' => 35,
                'order' => 7,
            ],
            [
                'question' => 'What is their favorite restaurant?',
                'options' => ['Italian', 'Japanese', 'Mexican', 'American'],
                'correct_answer' => 0,
                'points' => 100,
                'time_limit' => 30,
                'order' => 8,
            ],
            [
                'question' => 'How many pets do they have?',
                'options' => ['0', '1', '2', '3'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 25,
                'order' => 9,
            ],
            [
                'question' => 'What is their favorite movie genre?',
                'options' => ['Comedy', 'Romance', 'Action', 'Drama'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 30,
                'order' => 10,
            ],
        ];

        foreach ($sampleQuestions as $questionData) {
            TriviaQuestion::firstOrCreate(
                [
                    'game_config_id' => $gameConfig->id,
                    'question' => $questionData['question'],
                ],
                [
                    'options' => $questionData['options'],
                    'correct_answer' => $questionData['correct_answer'],
                    'points' => $questionData['points'],
                    'time_limit' => $questionData['time_limit'],
                    'order' => $questionData['order'],
                    'is_active' => true,
                ]
            );
        }

        $this->command->info('Enhanced minigame system seeded successfully!');
        $this->command->info('Test client login: <EMAIL> / password');
        $this->command->info('Sample guest URLs:');
        $this->command->info('- /game/trivia/smith-wedding-2024/john-doe-guest');
        $this->command->info('- /game/trivia/smith-wedding-2024/jane-smith-guest');
        $this->command->info('- /game/trivia/smith-wedding-2024/bob-johnson-guest');
    }
}
