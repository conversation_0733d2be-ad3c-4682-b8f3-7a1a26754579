<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\Guest;
use App\Models\GuestScore;
use App\Models\TriviaQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MinigameController extends Controller
{
    /**
     * Display trivia game
     */
    public function trivia(Request $request)
    {
        $clientId = $request->get('client_id');
        $guestUrl = $request->get('guest_url');

        if (!$clientId) {
            abort(400, 'Client ID is required');
        }

        $client = Client::find($clientId);
        if (!$client || !$client->is_have_minigame) {
            abort(403, 'Minigames are not enabled for this client');
        }

        // Get trivia game config
        $triviaGame = Game::where('minigame_name', 'trivia')->first();
        if (!$triviaGame) {
            abort(404, 'Trivia game not found');
        }

        $gameConfig = GameConfig::where('client_id', $clientId)
                                ->where('game_id', $triviaGame->id)
                                ->first();

        if (!$gameConfig) {
            abort(404, 'Trivia game not configured for this client');
        }

        // Check if game is active
        if (!$gameConfig->is_starting) {
            return view('minigame.trivia.not-active', compact('gameConfig', 'client'));
        }

        // Get guest if URL provided
        $guest = null;
        if ($guestUrl) {
            $guest = Guest::where('client_id', $clientId)
                         ->where('unique_url', $guestUrl)
                         ->first();

            if ($guest) {
                // Check if guest has already completed the game
                $existingScore = GuestScore::where('client_id', $clientId)
                                         ->where('guest_id', $guest->id)
                                         ->where('game_config_id', $gameConfig->id)
                                         ->where('is_completed', true)
                                         ->first();

                if ($existingScore) {
                    return redirect()->route('minigame.leaderboard', [
                        'client_id' => $clientId,
                        'game_config_id' => $gameConfig->id,
                        'guest_url' => $guestUrl
                    ]);
                }
            }
        }

        // Get trivia questions
        $questions = $gameConfig->triviaQuestions()
                               ->active()
                               ->ordered()
                               ->get();

        return view('minigame.trivia.index', compact('gameConfig', 'client', 'guest', 'questions'));
    }

    /**
     * Submit trivia answers and calculate score
     */
    public function submitTrivia(Request $request)
    {
        $request->validate([
            'client_id' => 'required|exists:clients,id',
            'game_config_id' => 'required|exists:game_configs,id',
            'guest_id' => 'nullable|exists:guests,id',
            'guest_name' => 'required_without:guest_id|string|max:255',
            'answers' => 'required|array',
            'time_taken' => 'required|array',
        ]);

        $client = Client::find($request->client_id);
        $gameConfig = GameConfig::find($request->game_config_id);

        // Verify game is still active
        if (!$gameConfig->is_starting) {
            return response()->json(['error' => 'Game is no longer active'], 403);
        }

        // Get or create guest
        $guest = null;
        if ($request->guest_id) {
            $guest = Guest::find($request->guest_id);
        } else {
            // Create temporary guest for non-registered players
            $guest = Guest::create([
                'client_id' => $request->client_id,
                'name' => $request->guest_name,
                'unique_url' => 'temp_' . uniqid(),
                'is_guest' => false,
            ]);
        }

        // Check if guest has already completed this game
        $existingScore = GuestScore::where('client_id', $request->client_id)
                                 ->where('guest_id', $guest->id)
                                 ->where('game_config_id', $request->game_config_id)
                                 ->where('is_completed', true)
                                 ->first();

        if ($existingScore) {
            return response()->json(['error' => 'You have already completed this game'], 403);
        }

        // Calculate score
        $questions = $gameConfig->triviaQuestions()->active()->ordered()->get();
        $totalScore = 0;
        $correctAnswers = 0;
        $gameData = [];

        foreach ($questions as $index => $question) {
            $userAnswer = $request->answers[$index] ?? null;
            $timeTaken = $request->time_taken[$index] ?? $question->time_limit;
            $isCorrect = $userAnswer !== null && $userAnswer == $question->correct_answer;

            if ($isCorrect) {
                $correctAnswers++;
                // Calculate score with time bonus
                $timeBonus = max(0, ($question->time_limit - $timeTaken) / $question->time_limit);
                $questionScore = $question->points + ($question->points * $timeBonus * 0.5);
                $totalScore += $questionScore;
            }

            $gameData[] = [
                'question_id' => $question->id,
                'user_answer' => $userAnswer,
                'correct_answer' => $question->correct_answer,
                'is_correct' => $isCorrect,
                'time_taken' => $timeTaken,
                'points_earned' => $isCorrect ? $questionScore : 0,
            ];
        }

        // Save or update guest score
        $guestScore = GuestScore::updateOrCreate(
            [
                'client_id' => $request->client_id,
                'guest_id' => $guest->id,
                'game_config_id' => $request->game_config_id,
            ],
            [
                'score' => round($totalScore),
                'is_completed' => true,
                'completed_at' => now(),
                'game_data' => [
                    'answers' => $gameData,
                    'total_questions' => $questions->count(),
                    'correct_answers' => $correctAnswers,
                    'accuracy' => $questions->count() > 0 ? ($correctAnswers / $questions->count()) * 100 : 0,
                ],
            ]
        );

        // Update rank
        $guestScore->updateRank();

        return response()->json([
            'success' => true,
            'score' => $guestScore->score,
            'rank' => $guestScore->rank,
            'correct_answers' => $correctAnswers,
            'total_questions' => $questions->count(),
            'leaderboard_url' => route('minigame.leaderboard', [
                'client_id' => $request->client_id,
                'game_config_id' => $request->game_config_id,
                'guest_url' => $guest->unique_url
            ])
        ]);
    }

    /**
     * Display leaderboard
     */
    public function leaderboard(Request $request)
    {
        $clientId = $request->get('client_id');
        $gameConfigId = $request->get('game_config_id');
        $guestUrl = $request->get('guest_url');

        if (!$clientId || !$gameConfigId) {
            abort(400, 'Client ID and Game Config ID are required');
        }

        $client = Client::find($clientId);
        $gameConfig = GameConfig::find($gameConfigId);

        if (!$client || !$gameConfig || $gameConfig->client_id !== $client->id) {
            abort(404, 'Game configuration not found');
        }

        // Get leaderboard
        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        // Get current guest info if URL provided
        $currentGuest = null;
        $currentGuestScore = null;
        if ($guestUrl) {
            $currentGuest = Guest::where('client_id', $clientId)
                                ->where('unique_url', $guestUrl)
                                ->first();

            if ($currentGuest) {
                $currentGuestScore = GuestScore::where('client_id', $clientId)
                                             ->where('guest_id', $currentGuest->id)
                                             ->where('game_config_id', $gameConfigId)
                                             ->where('is_completed', true)
                                             ->first();
            }
        }

        return view('minigame.leaderboard', compact(
            'client', 'gameConfig', 'leaderboard', 'currentGuest', 'currentGuestScore'
        ));
    }

    /**
     * Get live leaderboard data (for AJAX updates)
     */
    public function liveLeaderboard(Request $request)
    {
        $gameConfigId = $request->get('game_config_id');
        $gameConfig = GameConfig::find($gameConfigId);

        if (!$gameConfig) {
            return response()->json(['error' => 'Game configuration not found'], 404);
        }

        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        return response()->json([
            'leaderboard' => $leaderboard->map(function ($score) {
                return [
                    'id' => $score->id,
                    'guest_name' => $score->guest->name,
                    'score' => $score->score,
                    'rank' => $score->rank,
                    'completed_at' => $score->completed_at->format('M d, Y H:i'),
                    'is_winner' => $score->rank <= 3,
                ];
            }),
            'total_participants' => $gameConfig->guestScores()->where('is_completed', true)->count(),
            'game_status' => [
                'is_starting' => $gameConfig->is_starting,
                'is_done' => $gameConfig->is_done,
            ]
        ]);
    }

    /**
     * Get trivia questions for a game (API endpoint)
     */
    public function getTriviaQuestions(Request $request)
    {
        $gameConfigId = $request->get('game_config_id');
        $gameConfig = GameConfig::find($gameConfigId);

        if (!$gameConfig || !$gameConfig->is_starting) {
            return response()->json(['error' => 'Game not available'], 403);
        }

        $questions = $gameConfig->triviaQuestions()
                               ->active()
                               ->ordered()
                               ->get()
                               ->map(function ($question) {
                                   return [
                                       'id' => $question->id,
                                       'question' => $question->question,
                                       'options' => $question->options,
                                       'points' => $question->points,
                                       'time_limit' => $question->time_limit,
                                   ];
                               });

        return response()->json([
            'questions' => $questions,
            'game_title' => $gameConfig->game_title,
            'total_questions' => $questions->count(),
        ]);
    }
}
