<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $gameConfig->game_title }} - Leaderboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #f3e8ff 100%);
            min-height: 100vh;
        }
        
        .floral-bg {
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(251, 207, 232, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(251, 207, 232, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(251, 207, 232, 0.2) 0%, transparent 50%);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(251, 207, 232, 0.3);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(251, 207, 232, 0.2);
        }
        
        .winner-glow {
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(251, 207, 232, 0.5); }
            to { box-shadow: 0 0 30px rgba(251, 207, 232, 0.8); }
        }
        
        .rank-badge {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            position: relative;
        }
        
        .rank-gold {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #8b6914;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
        }
        
        .rank-silver {
            background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
            color: #666;
            box-shadow: 0 4px 15px rgba(192, 192, 192, 0.4);
        }
        
        .rank-bronze {
            background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
            color: #5d4e37;
            box-shadow: 0 4px 15px rgba(205, 127, 50, 0.4);
        }
        
        .rank-regular {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }
        
        .score-badge {
            background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(236, 72, 153, 0.3);
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="floral-bg">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="card p-6 mb-6">
                <h1 class="text-4xl md:text-5xl font-bold text-pink-600 mb-2" style="font-family: 'Playfair Display', serif;">
                    <i class="fas fa-trophy text-yellow-500 mr-3"></i>
                    {{ $gameConfig->game_title }}
                </h1>
                <p class="text-lg text-gray-600">{{ $client->client_name }} Wedding Celebration</p>
                <div class="flex justify-center items-center mt-4 space-x-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-pink-600">{{ $leaderboard->count() }}</div>
                        <div class="text-sm text-gray-500">Players</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-pink-600">{{ $gameConfig->top_players }}</div>
                        <div class="text-sm text-gray-500">Winners</div>
                    </div>
                    <div class="text-center">
                        @if($gameConfig->is_starting)
                            <div class="text-green-600 pulse">
                                <i class="fas fa-play-circle text-2xl"></i>
                                <div class="text-sm">Game Active</div>
                            </div>
                        @elseif($gameConfig->is_done)
                            <div class="text-blue-600">
                                <i class="fas fa-check-circle text-2xl"></i>
                                <div class="text-sm">Completed</div>
                            </div>
                        @else
                            <div class="text-gray-500">
                                <i class="fas fa-pause-circle text-2xl"></i>
                                <div class="text-sm">Inactive</div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Guest Score (if applicable) -->
        @if($currentGuestScore)
        <div class="card p-6 mb-6 winner-glow">
            <div class="text-center">
                <h3 class="text-xl font-bold text-pink-600 mb-2">Your Result</h3>
                <div class="flex justify-center items-center space-x-4">
                    <div class="rank-badge {{ $currentGuestScore->rank <= 3 ? ($currentGuestScore->rank == 1 ? 'rank-gold' : ($currentGuestScore->rank == 2 ? 'rank-silver' : 'rank-bronze')) : 'rank-regular' }}">
                        @if($currentGuestScore->rank == 1)
                            <i class="fas fa-crown"></i>
                        @elseif($currentGuestScore->rank == 2)
                            <i class="fas fa-medal"></i>
                        @elseif($currentGuestScore->rank == 3)
                            <i class="fas fa-award"></i>
                        @endif
                        <span class="ml-1">{{ $currentGuestScore->rank }}</span>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-800">{{ $currentGuest->name }}</div>
                        <div class="score-badge">{{ $currentGuestScore->score }} points</div>
                    </div>
                </div>
                @if($currentGuestScore->rank <= $gameConfig->top_players)
                    <div class="mt-3 text-yellow-600 font-bold">
                        <i class="fas fa-star mr-1"></i>Congratulations! You're a winner!
                    </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Leaderboard -->
        <div class="card p-6">
            <h2 class="text-2xl font-bold text-pink-600 mb-6 text-center">
                <i class="fas fa-list-ol mr-2"></i>Top {{ $gameConfig->top_players }} Winners
            </h2>
            
            @if($leaderboard->count() > 0)
                <div class="space-y-4" id="leaderboard-container">
                    @foreach($leaderboard as $index => $score)
                    <div class="flex items-center justify-between p-4 rounded-lg {{ $index < 3 ? 'bg-gradient-to-r from-yellow-50 to-pink-50 border-2 border-pink-200' : 'bg-gray-50' }} transition-all duration-300 hover:transform hover:scale-105">
                        <div class="flex items-center space-x-4">
                            <div class="rank-badge {{ $index == 0 ? 'rank-gold' : ($index == 1 ? 'rank-silver' : ($index == 2 ? 'rank-bronze' : 'rank-regular')) }}">
                                @if($index == 0)
                                    <i class="fas fa-crown"></i>
                                @elseif($index == 1)
                                    <i class="fas fa-medal"></i>
                                @elseif($index == 2)
                                    <i class="fas fa-award"></i>
                                @endif
                                <span class="ml-1">{{ $index + 1 }}</span>
                            </div>
                            <div>
                                <div class="font-bold text-lg text-gray-800">{{ $score->guest->name }}</div>
                                <div class="text-sm text-gray-500">
                                    Completed: {{ $score->completed_at->format('M d, Y H:i') }}
                                </div>
                                @if($index < 3)
                                    <div class="text-yellow-600 text-sm font-bold">
                                        <i class="fas fa-star mr-1"></i>Winner
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="score-badge text-lg">{{ $score->score }} pts</div>
                            @if($score->game_data && isset($score->game_data['accuracy']))
                                <div class="text-sm text-gray-500 mt-1">
                                    {{ number_format($score->game_data['accuracy'], 1) }}% accuracy
                                </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-trophy text-6xl text-pink-300 mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-600 mb-2">No Players Yet</h3>
                    <p class="text-gray-500">Waiting for guests to play the game...</p>
                    @if($gameConfig->is_starting)
                        <div class="mt-4">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
                            <p class="text-pink-600 mt-2 font-bold">Game is active - players can join now!</p>
                        </div>
                    @endif
                </div>
            @endif
        </div>

        <!-- Auto-refresh notice -->
        @if($gameConfig->is_starting)
        <div class="text-center mt-6">
            <div class="card p-4">
                <p class="text-sm text-gray-600">
                    <i class="fas fa-sync-alt mr-1 animate-spin"></i>
                    Leaderboard updates automatically every 30 seconds
                </p>
            </div>
        </div>
        @endif
    </div>

    <script>
        // Auto-refresh leaderboard if game is active
        @if($gameConfig->is_starting)
        setInterval(function() {
            location.reload();
        }, 30000); // Refresh every 30 seconds
        @endif

        // Add entrance animations
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('#leaderboard-container > div');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
