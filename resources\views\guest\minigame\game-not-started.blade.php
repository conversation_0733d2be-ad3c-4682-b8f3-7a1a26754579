<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Not Started - {{ $client->client_name ?? 'Wedding Minigame' }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Jakarta Sans Font -->
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-pink: #ec4899;
            --secondary-pink: #f8d7da;
            --light-pink: #fdf2f8;
            --accent-pink: #be185d;
            --text-dark: #721c24;
            --text-light: #6b7280;
            --white: #ffffff;
            --shadow-pink: 0 8px 32px rgba(251, 207, 232, 0.2);
            --gradient-bg: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #f3e8ff 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Plus Jakarta Sans', sans-serif !important;
            background: var(--gradient-bg);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        .waiting-container {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(251, 207, 232, 0.3) !important;
            border-radius: 25px !important;
            box-shadow: var(--shadow-pink) !important;
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }

        .waiting-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            color: white;
            font-size: 3rem;
            animation: pulse 2s ease-in-out infinite;
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .waiting-title {
            color: var(--text-dark);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .waiting-subtitle {
            color: var(--text-light);
            font-size: 1.2rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .client-info {
            background: var(--light-pink);
            border: 2px solid var(--secondary-pink);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .client-name {
            color: var(--accent-pink);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .guest-name {
            color: var(--text-dark);
            font-size: 1.1rem;
            font-weight: 500;
        }

        .waiting-message {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--primary-pink);
        }

        .refresh-btn {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%) !important;
            border: none !important;
            border-radius: 15px !important;
            color: white !important;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(236, 72, 153, 0.3);
            padding: 12px 30px;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(236, 72, 153, 0.4) !important;
            background: linear-gradient(135deg, var(--accent-pink) 0%, var(--primary-pink) 100%) !important;
            color: white !important;
            text-decoration: none;
        }

        .home-btn {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 2px solid var(--primary-pink) !important;
            border-radius: 15px !important;
            color: var(--primary-pink) !important;
            font-weight: 600;
            transition: all 0.3s ease;
            padding: 12px 30px;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }

        .home-btn:hover {
            background: var(--primary-pink) !important;
            color: white !important;
            transform: translateY(-2px);
            text-decoration: none;
        }

        .floating-hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .heart {
            position: absolute;
            color: var(--secondary-pink);
            font-size: 20px;
            animation: float 6s ease-in-out infinite;
            opacity: 0.6;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .heart:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .heart:nth-child(2) { top: 20%; right: 10%; animation-delay: 1s; }
        .heart:nth-child(3) { bottom: 20%; left: 15%; animation-delay: 2s; }
        .heart:nth-child(4) { bottom: 10%; right: 15%; animation-delay: 3s; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .waiting-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .waiting-title {
                font-size: 2rem;
            }
            
            .waiting-subtitle {
                font-size: 1rem;
            }
            
            .waiting-icon {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }
        }

        @media (max-width: 576px) {
            .waiting-container {
                padding: 1.5rem;
            }
            
            .waiting-title {
                font-size: 1.8rem;
            }
            
            .refresh-btn, .home-btn {
                display: block;
                width: 100%;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="floating-hearts">
        <div class="heart">💕</div>
        <div class="heart">💖</div>
        <div class="heart">💗</div>
        <div class="heart">💝</div>
    </div>

    <div class="waiting-container">
        <div class="waiting-icon">
            <i class="fas fa-clock"></i>
        </div>
        
        <h1 class="waiting-title">Game Starting Soon!</h1>
        <p class="waiting-subtitle">The {{ ucfirst($gameType) }} game hasn't started yet. Please wait for the host to begin the game.</p>
        
        <div class="client-info">
            <div class="client-name">
                <i class="fas fa-heart me-2"></i>{{ $client->client_name ?? 'Wedding Event' }}
            </div>
            @if($guest)
            <div class="guest-name">
                Welcome, {{ $guest->name }}!
            </div>
            @endif
        </div>
        
        <div class="waiting-message">
            <i class="fas fa-info-circle me-2" style="color: var(--primary-pink);"></i>
            <strong>What's happening?</strong><br>
            The game host will start the {{ $gameType }} game when everyone is ready. This page will automatically update when the game begins, or you can refresh manually.
        </div>
        
        <div class="action-buttons">
            <button onclick="window.location.reload()" class="refresh-btn">
                <i class="fas fa-sync-alt me-2"></i>Refresh Page
            </button>
            <a href="/" class="home-btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Auto-refresh script -->
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            window.location.reload();
        }, 30000);
        
        // Add a subtle notification
        let refreshCount = 0;
        setInterval(function() {
            refreshCount++;
            if (refreshCount % 30 === 0) {
                console.log('Auto-refreshing to check game status...');
            }
        }, 1000);
    </script>
</body>
</html>
