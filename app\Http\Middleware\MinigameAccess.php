<?php

namespace App\Http\Middleware;

use App\Models\Client;
use App\Models\GameConfig;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class MinigameAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $clientId = $request->get('client_id');

        if (!$clientId) {
            abort(400, 'Client ID is required to access minigames');
        }

        $client = Client::find($clientId);

        if (!$client) {
            abort(404, 'Client not found');
        }

        if (!$client->is_have_minigame) {
            abort(403, 'Minigames are not enabled for this client');
        }

        // Add client to request for use in controllers
        $request->merge(['client' => $client]);

        return $next($request);
    }
}
