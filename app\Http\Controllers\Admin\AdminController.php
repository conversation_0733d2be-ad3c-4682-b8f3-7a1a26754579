<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\Guest;
use App\Models\GuestScore;
use Auth;
use Alert;
use Illuminate\Http\Request;
use Carbon\Carbon;


class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        
        // Tambahkan validasi untuk memeriksa role admin
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'admin') {
                abort(403, 'Aks<PERSON>. Selain role Admin tidak diizinkan mengakses halaman ini.');
            }
            return $next($request);
        });
    }
    /**
     * Display the admin dashboard with comprehensive statistics.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Basic statistics
        $totalClients = Client::count();
        $totalUsers = User::count();
        $totalGames = Game::count();
        $totalGuests = Guest::count();

        // Active game configurations
        $activeGameConfigs = GameConfig::where('is_starting', true)->count();
        $totalGameConfigs = GameConfig::count();

        // Guest participation statistics
        $guestsWithScores = GuestScore::distinct('guest_id')->count();
        $totalGameSessions = GuestScore::count();
        $averageScore = GuestScore::avg('score') ?? 0;

        // Recent activity (last 7 days)
        $recentClients = Client::where('created_at', '>=', Carbon::now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentGameSessions = GuestScore::with(['guest', 'gameConfig.game'])
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Monthly statistics for charts
        $monthlyStats = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthlyStats[] = [
                'month' => $month->format('M Y'),
                'clients' => Client::whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->count(),
                'games_played' => GuestScore::whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->count(),
                'guests' => Guest::whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->count(),
            ];
        }

        // Top performing games
        $topGames = GameConfig::withCount(['guestScores'])
            ->with('game')
            ->orderBy('guest_scores_count', 'desc')
            ->limit(5)
            ->get();

        // Clients with minigames enabled
        $clientsWithMinigames = Client::where('is_have_minigame', true)->count();
        $minigameParticipationRate = $totalClients > 0 ?
            round(($clientsWithMinigames / $totalClients) * 100, 1) : 0;

        return view('admin.dashboard', compact(
            'totalClients',
            'totalUsers',
            'totalGames',
            'totalGuests',
            'activeGameConfigs',
            'totalGameConfigs',
            'guestsWithScores',
            'totalGameSessions',
            'averageScore',
            'recentClients',
            'recentGameSessions',
            'monthlyStats',
            'topGames',
            'clientsWithMinigames',
            'minigameParticipationRate'
        ));
    }

}
