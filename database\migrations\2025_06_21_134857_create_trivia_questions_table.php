<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trivia_questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('game_config_id')->constrained()->onDelete('cascade');
            $table->text('question');
            $table->json('options'); // Array of answer options
            $table->integer('correct_answer'); // Index of correct answer (0-based)
            $table->integer('points')->default(10);
            $table->integer('time_limit')->default(30); // seconds
            $table->integer('order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['game_config_id', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trivia_questions');
    }
};
