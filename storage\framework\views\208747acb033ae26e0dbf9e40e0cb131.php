<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($gameConfig->game_title ?? 'Wedding Trivia'); ?>!</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.7.77/Tone.js"></script>
    <link
        href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Poppins:wght@400;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="<?php echo e(asset('vendor/minigames/trivia/style.css')); ?>">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <style>
        body {
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #f3e8ff 100%);
            font-family: 'Poppins', sans-serif;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(251, 207, 232, 0.3);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(251, 207, 232, 0.2);
        }

        .btn {
            background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
        }
    </style>
</head>

<body class="min-h-screen flex items-center justify-center p-4">

    <!-- Animated background will be created here by JS -->
    <div class="background-container"></div>

    <div id="main-container" class="w-full max-w-2xl mx-auto">

        <!-- Welcome Screen -->
        <div id="welcome-screen" class="screen active card p-8 md:p-12 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-pink-600 mb-4"
                style="font-family: 'Playfair Display', serif;">
                Welcome to <?php echo e($gameConfig->game_title); ?>!
            </h1>
            <p class="text-lg text-gray-600 mb-2"><?php echo e($client->client_name); ?> Wedding Celebration</p>
            <p class="text-md text-gray-500 mb-8">Let's see how well you know the happy couple.</p>

            <?php if($guest): ?>
            <!-- Guest is pre-identified -->
            <div class="mb-6">
                <div class="bg-pink-50 border-2 border-pink-200 rounded-lg p-4">
                    <p class="text-lg font-semibold text-pink-800">Welcome, <?php echo e($guest->name); ?>!</p>
                    <p class="text-sm text-pink-600">You're ready to play</p>
                </div>
                <input type="hidden" id="guest-id" value="<?php echo e($guest->id); ?>">
                <input type="hidden" id="guest-name" value="<?php echo e($guest->name); ?>">
            </div>
            <?php else: ?>
            <!-- Manual name entry -->
            <div class="mb-6">
                <input type="text" id="name-input" placeholder="Enter your name"
                    class="w-full max-w-sm mx-auto p-4 border-2 border-pink-200 rounded-lg text-lg focus:ring-2 focus:ring-pink-400 focus:outline-none focus:border-pink-400">
            </div>
            <?php endif; ?>

            <button id="start-btn" class="btn text-white font-bold py-3 px-12 rounded-full text-xl">
                Start Game
            </button>
            <p id="name-error" class="text-red-500 mt-4 hidden">Please enter your name to start!</p>

            <div class="mt-6 text-sm text-gray-500">
                <p>🏆 Top <?php echo e($gameConfig->top_players); ?> players will be winners!</p>
                <?php if($questions->count() > 0): ?>
                <p>📝 <?php echo e($questions->count()); ?> questions waiting for you</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Get Ready Screen -->
        <div id="get-ready-screen" class="screen text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-8"
                style="text-shadow: 2px 2px 4px rgba(0,0,0,0.2);">Get Ready...</h2>
            <div id="countdown-number">3</div>
        </div>

        <!-- Game Screen -->
        <div id="game-screen" class="screen w-full">
            <div id="score-popup"></div>
            <div
                class="flex justify-between items-center mb-4 text-white p-3 bg-violet-500/80 rounded-lg backdrop-blur-sm">
                <h2 id="player-name" class="text-lg font-bold">Player</h2>
                <div class="text-right">
                    <p class="text-xs">SCORE</p>
                    <p id="score" class="text-2xl font-bold">0</p>
                </div>
            </div>

            <div class="card p-6 md:p-8">
                <div class="text-center">
                    <p class="text-md text-gray-500">Question <span id="question-number">1</span> of <span
                            id="total-questions">10</span></p>
                    <h2 id="question"
                        class="text-xl md:text-2xl font-semibold mt-2 mb-4 min-h-[90px] flex items-center justify-center">
                        Question text goes here...</h2>
                </div>
                <div class="timer-bar-container mb-6">
                    <div id="timer-bar" class="timer-bar"></div>
                </div>
                <div id="answers" class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <!-- Answer buttons generated by JS -->
                </div>
            </div>
        </div>

        <!-- End Screen -->
        <div id="end-screen" class="screen card p-8 md:p-12 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-violet-600 mb-2">Congratulations!</h1>
            <p class="text-lg text-gray-600 mb-6">You've completed the trivia!</p>
            <div class="bg-violet-100 p-6 rounded-lg mb-8">
                <p class="text-xl text-violet-800">Your Final Score</p>
                <p id="final-score" class="text-6xl font-bold text-violet-600 mt-2">0</p>
            </div>
            <button id="restart-btn"
                class="btn bg-violet-500 hover:bg-violet-600 text-white font-bold py-3 px-12 rounded-full text-xl">
                Play Again
            </button>
        </div>
    </div>

    <!-- Game Configuration -->
    <script>
        // Game configuration from server
        window.gameConfig = {
            clientId: <?php echo e($client->id); ?>,
            gameConfigId: <?php echo e($gameConfig->id); ?>,
            guestId: <?php echo e($guest->id ?? 'null'); ?>,
            gameTitle: "<?php echo e($gameConfig->game_title); ?>",
            questions: <?php echo json_encode($questions, 15, 512) ?>,
            submitUrl: "<?php echo e(route('minigame.trivia.submit')); ?>",
            csrfToken: "<?php echo e(csrf_token()); ?>"
        };
    </script>
    <script src="<?php echo e(asset('vendor/minigames/trivia/dynamic-script.js')); ?>"></script>
</body>

</html><?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/minigame/trivia/index.blade.php ENDPATH**/ ?>