<?php

namespace App\Http\Controllers\Auth;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Client;
use Illuminate\Http\Request;
use Auth;
use Session;
use Alert;

class AuthController extends Controller
{
    public function loginPage() {
        return view('auth.login');
    }

    public function loginUser(Request $req)
    {
        $req->validate([
            'username' => 'required',
            'password' => 'required',
            
        ]);   
        $username = $req->get('username');
        $credentials = $req->only('username', 'password');
        $user = User::where('username', $username)->first();
        $client = Client::where('user_id', $user->id)->first();
        if (auth()->guard('web')->attempt($credentials) && $user->role == 'admin') {
            session(["username" => $username]);
            session(["role" => $user->role]);
            // toastr()->success('Login Success');
            return redirect('/admin');
        } else if (auth()->guard('web')->attempt($credentials) && $user->role == 'client' && $client->is_have_minigame == 1) {
            session(["username" => $username]);
            session(["role" => $user->role]);
            // toastr()->success('Login Success');
            return redirect('/client/dashboard');
        }else if(auth()->guard('web')->attempt($credentials) && $user->role == 'client' && $client->is_have_minigame == 0) {
            // toastr()->error('You dont have access to Minigame yet!');
            return redirect()->back();
        } else {
            // toastr()->error('Username or Password is incorrect!');

            return redirect()->back();
        }
    }

    public function logout() {
        session()->flush();
        Auth::logout();
        return redirect('/login');
    }

}