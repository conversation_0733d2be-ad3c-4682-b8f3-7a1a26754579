<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\Guest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ClientController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'admin') {
                abort(403, 'Access Denied. Only admin role is allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $clients = Client::with(['user', 'guests', 'gameConfigs.game'])
                        ->paginate(15);

        return view('admin.clients.index', compact('clients'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $games = Game::where('is_active', true)->get();
        return view('admin.clients.create', compact('games'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'client_name' => 'required|string|max:255',
            'unique_url' => 'required|string|unique:clients,unique_url',
            'is_have_minigame' => 'boolean',
            'minigame_options' => 'array',
        ]);

        $client = Client::create($request->all());

        alert()->success('Success', 'Client created successfully!');
        return redirect()->route('admin.clients.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(Client $client)
    {
        $client->load(['guests', 'gameConfigs.game', 'gameConfigs.guestScores.guest']);

        return view('admin.clients.show', compact('client'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Client $client)
    {
        $games = Game::where('is_active', true)->get();
        $client->load('gameConfigs.game');

        return view('admin.clients.edit', compact('client', 'games'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Client $client)
    {
        $request->validate([
            'client_name' => 'required|string|max:255',
            'is_have_minigame' => 'boolean',
            'minigame_options' => 'array',
        ]);

        $client->update($request->all());

        alert()->success('Success', 'Client updated successfully!');
        return redirect()->route('admin.clients.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Client $client)
    {
        $client->delete();

        alert()->success('Success', 'Client deleted successfully!');
        return redirect()->route('admin.clients.index');
    }

    /**
     * Manage minigame settings for a client
     */
    public function minigameSettings(Client $client)
    {
        $games = Game::where('is_active', true)->get();
        $client->load('gameConfigs.game');

        return view('admin.clients.minigame-settings', compact('client', 'games'));
    }

    /**
     * Update minigame settings for a client
     */
    public function updateMinigameSettings(Request $request, Client $client)
    {
        $request->validate([
            'is_have_minigame' => 'boolean',
            'minigame_options' => 'array',
            'game_configs' => 'array',
            'game_configs.*.game_id' => 'required|exists:games,id',
            'game_configs.*.game_title' => 'required|string|max:255',
            'game_configs.*.top_players' => 'required|integer|min:1|max:50',
        ]);

        // Update client minigame settings
        $client->update([
            'is_have_minigame' => $request->is_have_minigame ?? false,
            'minigame_options' => $request->minigame_options ?? [],
        ]);

        // Update or create game configurations
        if ($request->has('game_configs')) {
            foreach ($request->game_configs as $gameConfigData) {
                GameConfig::updateOrCreate(
                    [
                        'client_id' => $client->id,
                        'game_id' => $gameConfigData['game_id']
                    ],
                    [
                        'game_title' => $gameConfigData['game_title'],
                        'top_players' => $gameConfigData['top_players'],
                        'config_data' => $gameConfigData['config_data'] ?? null,
                    ]
                );
            }
        }

        alert()->success('Success', 'Minigame settings updated successfully!');
        return redirect()->route('admin.clients.minigame-settings', $client);
    }
}