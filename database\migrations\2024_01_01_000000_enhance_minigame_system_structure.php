<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ensure trivia_questions table has all required columns
        if (Schema::hasTable('trivia_questions')) {
            Schema::table('trivia_questions', function (Blueprint $table) {
                if (!Schema::hasColumn('trivia_questions', 'options')) {
                    $table->json('options')->nullable()->after('question');
                }
                if (!Schema::hasColumn('trivia_questions', 'correct_answer')) {
                    $table->integer('correct_answer')->default(0)->after('options');
                }
                if (!Schema::hasColumn('trivia_questions', 'points')) {
                    $table->integer('points')->default(100)->after('correct_answer');
                }
                if (!Schema::hasColumn('trivia_questions', 'time_limit')) {
                    $table->integer('time_limit')->default(30)->after('points');
                }
                if (!Schema::hasColumn('trivia_questions', 'order')) {
                    $table->integer('order')->default(0)->after('time_limit');
                }
                if (!Schema::hasColumn('trivia_questions', 'is_active')) {
                    $table->boolean('is_active')->default(true)->after('order');
                }
            });
        }

        // Ensure guest_scores table has required columns for enhanced functionality
        if (Schema::hasTable('guest_scores')) {
            Schema::table('guest_scores', function (Blueprint $table) {
                if (!Schema::hasColumn('guest_scores', 'client_id')) {
                    $table->unsignedBigInteger('client_id')->nullable()->after('id');
                    $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
                }
                if (!Schema::hasColumn('guest_scores', 'is_completed')) {
                    $table->boolean('is_completed')->default(false)->after('score');
                }
                if (!Schema::hasColumn('guest_scores', 'completed_at')) {
                    $table->timestamp('completed_at')->nullable()->after('is_completed');
                }
                if (!Schema::hasColumn('guest_scores', 'game_data')) {
                    $table->json('game_data')->nullable()->after('completed_at');
                }
                if (!Schema::hasColumn('guest_scores', 'rank')) {
                    $table->integer('rank')->nullable()->after('game_data');
                }
            });
        }

        // Ensure game_configs table has config_data column for enhanced settings
        if (Schema::hasTable('game_configs')) {
            Schema::table('game_configs', function (Blueprint $table) {
                if (!Schema::hasColumn('game_configs', 'config_data')) {
                    $table->json('config_data')->nullable()->after('top_players');
                }
            });
        }

        // Ensure guests table has unique_url column
        if (Schema::hasTable('guests')) {
            Schema::table('guests', function (Blueprint $table) {
                if (!Schema::hasColumn('guests', 'unique_url')) {
                    $table->string('unique_url')->nullable()->unique()->after('name');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove added columns if they exist
        if (Schema::hasTable('trivia_questions')) {
            Schema::table('trivia_questions', function (Blueprint $table) {
                $columnsToRemove = ['options', 'correct_answer', 'points', 'time_limit', 'order', 'is_active'];
                foreach ($columnsToRemove as $column) {
                    if (Schema::hasColumn('trivia_questions', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }

        if (Schema::hasTable('guest_scores')) {
            Schema::table('guest_scores', function (Blueprint $table) {
                $columnsToRemove = ['client_id', 'is_completed', 'completed_at', 'game_data', 'rank'];
                foreach ($columnsToRemove as $column) {
                    if (Schema::hasColumn('guest_scores', $column)) {
                        if ($column === 'client_id') {
                            $table->dropForeign(['client_id']);
                        }
                        $table->dropColumn($column);
                    }
                }
            });
        }

        if (Schema::hasTable('game_configs')) {
            Schema::table('game_configs', function (Blueprint $table) {
                if (Schema::hasColumn('game_configs', 'config_data')) {
                    $table->dropColumn('config_data');
                }
            });
        }

        if (Schema::hasTable('guests')) {
            Schema::table('guests', function (Blueprint $table) {
                if (Schema::hasColumn('guests', 'unique_url')) {
                    $table->dropColumn('unique_url');
                }
            });
        }
    }
};
