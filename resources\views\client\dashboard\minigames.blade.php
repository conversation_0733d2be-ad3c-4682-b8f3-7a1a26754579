@extends('client.layout.master')

@section('title', 'Manage Minigames')
@section('page-title', 'Manage Minigames')
@section('breadcrumb')
<li class="breadcrumb-item active">Minigames</li>
@endsection

@section('content')
<div class="row">
    <!-- Header -->
    <div class="col-12 mb-4">
        <div class="card"
            style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border: none; border-radius: 15px;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 style="color: #721c24; font-weight: 700; margin-bottom: 5px;">
                            <i class="fas fa-gamepad me-2" style="color: #d63384;"></i>
                            Minigame Control Panel
                        </h2>
                        <p style="color: #721c24; margin-bottom: 0;">
                            Start, stop, and monitor your wedding games
                        </p>
                    </div>
                    <a href="{{ route('client.dashboard.index') }}" class="btn btn-outline-dark"
                        style="border-radius: 20px;">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Controls -->
    @foreach($client->gameConfigs as $config)
    <div class="col-lg-6 mb-4">
        <div class="card" style="border: 2px solid #f8d7da; border-radius: 15px; height: 100%;">
            <div class="card-header"
                style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-radius: 13px 13px 0 0;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0" style="color: #721c24; font-weight: 600;">
                        {{ $config->game_title }}
                    </h5>
                    <div class="d-flex gap-2">
                        @if($config->is_starting)
                        <span class="badge bg-success">
                            <i class="fas fa-play me-1"></i>Active
                        </span>
                        @elseif($config->is_done)
                        <span class="badge bg-info">
                            <i class="fas fa-check me-1"></i>Completed
                        </span>
                        @else
                        <span class="badge bg-secondary">
                            <i class="fas fa-pause me-1"></i>Inactive
                        </span>
                        @endif
                    </div>
                </div>
                <small class="text-muted">{{ $config->game->display_name }}</small>
            </div>
            <div class="card-body">
                <!-- Game Statistics -->
                <div class="row mb-3">
                    <div class="col-4 text-center">
                        <div class="p-2"
                            style="background: linear-gradient(135deg, #fefefe 0%, #fdf8f9 100%); border-radius: 10px;">
                            <h4 style="color: #d63384; font-weight: 700; margin-bottom: 5px;">
                                {{ $config->guestScores->where('is_completed', true)->count() }}
                            </h4>
                            <small class="text-muted">Participants</small>
                        </div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="p-2"
                            style="background: linear-gradient(135deg, #fefefe 0%, #fdf8f9 100%); border-radius: 10px;">
                            <h4 style="color: #d63384; font-weight: 700; margin-bottom: 5px;">
                                {{ $config->top_players }}
                            </h4>
                            <small class="text-muted">Winners</small>
                        </div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="p-2"
                            style="background: linear-gradient(135deg, #fefefe 0%, #fdf8f9 100%); border-radius: 10px;">
                            <h4 style="color: #d63384; font-weight: 700; margin-bottom: 5px;">
                                {{ $config->guestScores->max('score') ?? 0 }}
                            </h4>
                            <small class="text-muted">High Score</small>
                        </div>
                    </div>
                </div>

                <!-- Game URL -->
                <div class="mb-3">
                    <label class="form-label" style="color: #721c24; font-weight: 600;">Game URL:</label>
                    <div class="input-group">
                        <input type="text" class="form-control"
                            value="{{ url('/minigame/' . $config->game->minigame_name . '?client_id=' . $client->id) }}"
                            readonly style="background: #f8f9fa;">
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard(this)">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <small class="text-muted">Share this URL with your guests to play the game</small>
                </div>

                <!-- Control Buttons -->
                <div class="d-flex gap-2 mb-3">
                    @if(!$config->is_starting && !$config->is_done)
                    <form action="{{ route('client.dashboard.start-game', $config) }}" method="POST" class="flex-fill">
                        @csrf
                        <button type="submit" class="btn btn-success w-100" style="border-radius: 10px;">
                            <i class="fas fa-play me-1"></i>Start Game
                        </button>
                    </form>
                    @endif

                    @if($config->is_starting)
                    <form action="{{ route('client.dashboard.stop-game', $config) }}" method="POST" class="flex-fill">
                        @csrf
                        <button type="submit" class="btn btn-danger w-100" style="border-radius: 10px;">
                            <i class="fas fa-stop me-1"></i>Stop Game
                        </button>
                    </form>
                    @endif

                    @if($config->is_done || $config->guestScores->count() > 0)
                    <form action="{{ route('client.dashboard.restart-game', $config) }}" method="POST" class="flex-fill"
                        onsubmit="return confirm('Are you sure? This will delete all existing scores!')">
                        @csrf
                        <button type="submit" class="btn btn-warning w-100" style="border-radius: 10px;">
                            <i class="fas fa-redo me-1"></i>Restart
                        </button>
                    </form>
                    @endif
                </div>

                <!-- Leaderboard Button -->
                <div class="d-grid">
                    <a href="{{ route('client.dashboard.leaderboard', $config) }}" class="btn"
                        style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); color: white; border: none; border-radius: 10px;">
                        <i class="fas fa-trophy me-1"></i>View Leaderboard
                    </a>
                </div>

                <!-- Top 3 Preview -->
                @if($config->guestScores->where('is_completed', true)->count() > 0)
                <div class="mt-3">
                    <h6 style="color: #721c24; font-weight: 600;">Top 3 Players:</h6>
                    @php
                    $topPlayers = $config->guestScores()
                    ->with('guest')
                    ->where('is_completed', true)
                    ->orderBy('score', 'desc')
                    ->orderBy('completed_at', 'asc')
                    ->limit(3)
                    ->get();
                    @endphp
                    @foreach($topPlayers as $index => $score)
                    <div class="d-flex justify-content-between align-items-center py-1">
                        <div class="d-flex align-items-center">
                            @if($index == 0)
                            <i class="fas fa-crown text-warning me-2"></i>
                            @elseif($index == 1)
                            <i class="fas fa-medal text-secondary me-2"></i>
                            @else
                            <i class="fas fa-award text-warning me-2"></i>
                            @endif
                            <span>{{ $score->guest->name }}</span>
                        </div>
                        <span class="badge"
                            style="background: linear-gradient(135deg, #d63384 0%, #c2185b 100%); color: white;">
                            {{ $score->score }} pts
                        </span>
                    </div>
                    @endforeach
                </div>
                @endif
            </div>
        </div>
    </div>
    @endforeach

    @if($client->gameConfigs->count() == 0)
    <div class="col-12">
        <div class="card" style="border: 2px solid #f8d7da; border-radius: 15px;">
            <div class="card-body text-center py-5">
                <i class="fas fa-gamepad fa-4x mb-3" style="color: #d63384; opacity: 0.3;"></i>
                <h4 style="color: #721c24;">No Games Configured</h4>
                <p class="text-muted">Contact your administrator to set up minigames for your event.</p>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
    function copyToClipboard(button) {
    const input = button.parentElement.querySelector('input');
    input.select();
    input.setSelectionRange(0, 99999);
    navigator.clipboard.writeText(input.value);
    
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.remove('btn-outline-secondary');
    button.classList.add('btn-success');
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-secondary');
    }, 2000);
}
</script>

<style>
    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(214, 51, 132, 0.15);
    }

    .btn:hover {
        transform: translateY(-1px);
    }

    .input-group .form-control:focus {
        border-color: #d63384;
        box-shadow: 0 0 0 0.2rem rgba(214, 51, 132, 0.25);
    }

    .badge {
        font-size: 11px;
        padding: 5px 10px;
        border-radius: 15px;
    }
</style>
@endsection