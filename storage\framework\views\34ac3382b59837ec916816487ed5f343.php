<nav class="pc-sidebar" id="sidebar">
    <div class="navbar-wrapper">
        <div class="m-header">
            <a href="<?php echo e(route('client.dashboard.index')); ?>" class="b-brand text-primary">
                <img src="<?php echo e(asset('logo.png')); ?>" style="max-width: 100%; height: auto;"
                    alt="Wedding Minigame System" />
            </a>
            <!-- Mobile close button -->
            <button class="btn btn-link d-lg-none mobile-close-btn" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="navbar-content">
            <div class="card pc-user-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="user-avatar">
                                <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3 me-2">
                            <h6 class="mb-0 heading-font"><?php echo e(Auth::user()->name); ?></h6>
                            <small style="color: var(--primary-pink);"><?php echo e(ucfirst(Auth::user()->role)); ?></small>
                        </div>
                        <a class="btn btn-icon btn-link-secondary avtar" data-bs-toggle="collapse"
                            href="#pc_sidebar_userlink">
                            <i class="fas fa-chevron-down"></i>
                        </a>
                    </div>
                    <div class="collapse pc-user-links" id="pc_sidebar_userlink">
                        <div class="pt-3">
                            <a href="#!" class="user-link">
                                <i class="fas fa-user me-2"></i>
                                <span>My Account</span>
                            </a>
                            <a href="#!" class="user-link">
                                <i class="fas fa-cog me-2"></i>
                                <span>Settings</span>
                            </a>
                            <a href="<?php echo e(route('sign-out')); ?>" class="user-link">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                <span>Logout</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <ul class="pc-navbar">
                <li class="pc-item pc-caption">
                    <label>Dashboard</label>
                    <i class="fas fa-heart" style="color: var(--primary-pink);"></i>
                </li>
                <li class="pc-item <?php echo e(request()->routeIs('client.dashboard.index') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('client.dashboard.index')); ?>" class="pc-link">
                        <span class="pc-micon"><i class="fas fa-home"></i></span>
                        <span class="pc-mtext">Dashboard</span>
                    </a>
                </li>

                <li class="pc-item pc-caption">
                    <label>Minigame Management</label>
                    <i class="fas fa-gamepad" style="color: var(--primary-pink);"></i>
                </li>
                <li class="pc-item <?php echo e(request()->routeIs('client.dashboard.minigames') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('client.dashboard.minigames')); ?>" class="pc-link">
                        <span class="pc-micon"><i class="fas fa-gamepad"></i></span>
                        <span class="pc-mtext">My Games</span>
                    </a>
                </li>

                <?php if(Auth::user()->client && Auth::user()->client->gameConfigs->count() > 0): ?>
                <li
                    class="pc-item pc-hasmenu <?php echo e(request()->routeIs('client.dashboard.game-config') || request()->routeIs('client.dashboard.leaderboard') ? 'pc-trigger active' : ''); ?>">
                    <a href="#!" class="pc-link">
                        <span class="pc-micon"><i class="fas fa-cogs"></i></span>
                        <span class="pc-mtext">Game Controls</span>
                        <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="pc-submenu">
                        <?php $__currentLoopData = Auth::user()->client->gameConfigs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $config): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="pc-item">
                            <a class="pc-link" href="<?php echo e(route('client.dashboard.game-config', $config)); ?>">
                                <span class="pc-micon"><i class="fas fa-play-circle"></i></span>
                                <span class="pc-mtext"><?php echo e(Str::limit($config->game_title, 20)); ?></span>
                                <?php if($config->is_starting): ?>
                                <span class="badge bg-success ms-2">Live</span>
                                <?php elseif($config->is_done): ?>
                                <span class="badge bg-info ms-2">Done</span>
                                <?php else: ?>
                                <span class="badge bg-secondary ms-2">Pending</span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </li>
                <?php endif; ?>

                <li class="pc-item pc-caption">
                    <label>Analytics</label>
                    <i class="fas fa-chart-bar" style="color: var(--primary-pink);"></i>
                </li>
                <li class="pc-item">
                    <a href="#!" class="pc-link">
                        <span class="pc-micon"><i class="fas fa-chart-line"></i></span>
                        <span class="pc-mtext">Game Statistics</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="#!" class="pc-link">
                        <span class="pc-micon"><i class="fas fa-users"></i></span>
                        <span class="pc-mtext">Guest Analytics</span>
                    </a>
                </li>

                <li class="pc-item pc-caption">
                    <label>Support</label>
                    <i class="fas fa-life-ring" style="color: var(--primary-pink);"></i>
                </li>
                <li class="pc-item">
                    <a href="#!" class="pc-link">
                        <span class="pc-micon"><i class="fas fa-question-circle"></i></span>
                        <span class="pc-mtext">Help & Support</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="#!" class="pc-link">
                        <span class="pc-micon"><i class="fas fa-book"></i></span>
                        <span class="pc-mtext">Documentation</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav><?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/client/partials/navbar.blade.php ENDPATH**/ ?>