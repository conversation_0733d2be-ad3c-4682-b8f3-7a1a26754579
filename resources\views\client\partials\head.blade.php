<style>
    /* Client-specific Wedding Theme Variables */
    :root {
        --primary-pink: #ec4899;
        --secondary-pink: #f8d7da;
        --light-pink: #fdf2f8;
        --accent-pink: #be185d;
        --text-dark: #721c24;
        --text-light: #9ca3af;
    }

    /* Typography */
    .heading-font {
        font-family: 'Playfair Display', serif;
        font-weight: 600;
    }

    body {
        font-family: 'Poppins', sans-serif;
        background: linear-gradient(135deg, var(--light-pink) 0%, rgba(251, 207, 232, 0.1) 100%);
    }

    /* Client Branding */
    .b-brand h6 {
        color: var(--primary-pink) !important;
        font-family: 'Playfair Display', serif;
        font-weight: 700;
    }

    /* User Avatar Styling */
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 16px;
    }

    .user-avatar-header {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 14px;
    }

    /* Sidebar Styling */
    .pc-sidebar {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px);
        border-right: 2px solid rgba(251, 207, 232, 0.3);
    }

    .pc-navbar .pc-item.active > .pc-link,
    .pc-navbar .pc-item.pc-trigger > .pc-link {
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        color: white;
        border-radius: 12px;
        margin: 2px 8px;
    }

    .pc-navbar .pc-item .pc-link:hover {
        background: rgba(236, 72, 153, 0.1);
        color: var(--primary-pink);
        border-radius: 12px;
        margin: 2px 8px;
    }

    .pc-navbar .pc-item .pc-link {
        padding: 12px 16px;
        margin: 2px 8px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .pc-navbar .pc-caption {
        color: var(--primary-pink);
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 20px;
        margin-bottom: 8px;
    }

    .pc-navbar .pc-caption label {
        color: var(--primary-pink);
    }

    /* User Card Styling */
    .pc-user-card {
        background: linear-gradient(135deg, var(--light-pink) 0%, rgba(251, 207, 232, 0.3) 100%);
        border: 2px solid rgba(251, 207, 232, 0.5);
        border-radius: 15px;
        margin: 15px;
    }

    .user-link {
        display: flex;
        align-items: center;
        padding: 8px 0;
        color: var(--text-dark);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .user-link:hover {
        color: var(--primary-pink);
        transform: translateX(5px);
    }

    /* Header Styling */
    .pc-header {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px);
        border-bottom: 2px solid rgba(251, 207, 232, 0.3);
    }

    .pc-head-link {
        color: var(--text-dark) !important;
        transition: all 0.3s ease;
    }

    .pc-head-link:hover {
        color: var(--primary-pink) !important;
        transform: scale(1.05);
    }

    /* Dropdown Styling */
    .dropdown-menu {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(236, 72, 153, 0.15);
    }

    .dropdown-item:hover {
        background: rgba(236, 72, 153, 0.1);
        color: var(--primary-pink);
    }

    .dropdown-header {
        color: var(--primary-pink);
        font-weight: 600;
    }

    /* Badge Styling */
    .badge {
        font-size: 0.7rem;
        padding: 4px 8px;
        border-radius: 10px;
    }

    .bg-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    }

    .bg-info {
        background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    }

    .bg-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    }

    /* Card Styling */
    .card {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 15px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(236, 72, 153, 0.15);
    }

    .card-header {
        background: linear-gradient(135deg, var(--light-pink) 0%, rgba(251, 207, 232, 0.3) 100%);
        border-bottom: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 13px 13px 0 0 !important;
    }

    /* Button Styling */
    .btn-primary {
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        border: none;
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
    }

    .btn-outline-primary {
        border: 2px solid var(--primary-pink);
        color: var(--primary-pink);
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-outline-primary:hover {
        background: var(--primary-pink);
        color: white;
        transform: translateY(-2px);
    }

    /* Responsive Mobile Styles */
    @media (max-width: 991.98px) {
        .pc-sidebar {
            position: fixed !important;
            left: -280px !important;
            top: 0 !important;
            height: 100vh !important;
            width: 280px !important;
            z-index: 1000 !important;
            transition: left 0.3s ease !important;
        }
        
        .pc-sidebar.pc-sidebar-show {
            left: 0 !important;
        }
        
        .pc-container {
            margin-left: 0 !important;
        }
        
        .pc-header {
            left: 0 !important;
            width: 100% !important;
        }

        /* Mobile overlay */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: block;
            backdrop-filter: blur(4px);
            transition: opacity 0.3s ease;
        }
        
        .mobile-close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            color: var(--primary-pink);
            font-size: 1.2rem;
            z-index: 1001;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .mobile-close-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }

        /* Touch-friendly navigation */
        .pc-navbar .pc-item .pc-link {
            padding: 15px 20px !important;
            min-height: 44px !important;
            display: flex !important;
            align-items: center !important;
            font-size: 16px !important;
        }
        
        .pc-navbar .pc-item .pc-micon {
            width: 44px !important;
            height: 44px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin-right: 12px !important;
        }
        
        .pc-navbar .pc-item .pc-micon i {
            font-size: 18px !important;
        }

        /* Header mobile improvements */
        .pc-head-link {
            min-width: 44px !important;
            min-height: 44px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }
        
        .dropdown-menu {
            position: absolute !important;
            right: 0 !important;
            left: auto !important;
            min-width: 200px !important;
            margin-top: 5px !important;
        }
        
        .dropdown-item {
            padding: 12px 20px !important;
            font-size: 14px !important;
            min-height: 44px !important;
            display: flex !important;
            align-items: center !important;
        }
    }

    /* Animation Classes */
    .fade-in-up {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Page Header */
    .page-header {
        margin-bottom: 30px;
    }

    .breadcrumb {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 25px;
        padding: 10px 20px;
        border: 2px solid rgba(251, 207, 232, 0.3);
    }

    .breadcrumb-item.active {
        color: var(--primary-pink);
    }

    .breadcrumb-item + .breadcrumb-item::before {
        color: var(--primary-pink);
        content: "❯";
    }
</style>

<!-- Mobile Navigation JavaScript -->
<script>
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-overlay');
        const body = document.body;
        
        if (sidebar) {
            const isShowing = sidebar.classList.contains('pc-sidebar-show');
            
            if (isShowing) {
                // Hide sidebar
                sidebar.classList.remove('pc-sidebar-show');
                body.style.overflow = '';
                if (overlay) {
                    overlay.remove();
                }
            } else {
                // Show sidebar
                sidebar.classList.add('pc-sidebar-show');
                body.style.overflow = 'hidden'; // Prevent background scrolling
                
                // Create overlay
                const mobileOverlay = document.createElement('div');
                mobileOverlay.id = 'mobile-overlay';
                mobileOverlay.className = 'mobile-overlay';
                mobileOverlay.onclick = toggleSidebar;
                body.appendChild(mobileOverlay);
            }
        }
    }
    
    // Enhanced mobile navigation handling
    document.addEventListener('DOMContentLoaded', function() {
        // Handle existing sidebar toggle buttons
        const sidebarHide = document.getElementById('sidebar-hide');
        const mobileCollapse = document.getElementById('mobile-collapse');
        
        if (sidebarHide) {
            sidebarHide.addEventListener('click', function(e) {
                e.preventDefault();
                toggleSidebar();
            });
        }
        
        if (mobileCollapse) {
            mobileCollapse.addEventListener('click', function(e) {
                e.preventDefault();
                toggleSidebar();
            });
        }
        
        // Close sidebar on window resize if mobile
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('mobile-overlay');
                
                if (sidebar && sidebar.classList.contains('pc-sidebar-show')) {
                    sidebar.classList.remove('pc-sidebar-show');
                    document.body.style.overflow = '';
                    if (overlay) {
                        overlay.remove();
                    }
                }
            }
        });
        
        // Handle touch events for better mobile experience
        let touchStartX = 0;
        let touchEndX = 0;
        
        document.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });
        
        document.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
        
        function handleSwipe() {
            const sidebar = document.getElementById('sidebar');
            if (!sidebar) return;
            
            const swipeThreshold = 50;
            const swipeDistance = touchEndX - touchStartX;
            
            // Swipe right to open sidebar (only if starting from left edge)
            if (swipeDistance > swipeThreshold && touchStartX < 50 && window.innerWidth < 992) {
                if (!sidebar.classList.contains('pc-sidebar-show')) {
                    toggleSidebar();
                }
            }
            
            // Swipe left to close sidebar
            if (swipeDistance < -swipeThreshold && sidebar.classList.contains('pc-sidebar-show')) {
                toggleSidebar();
            }
        }
    });
</script>
