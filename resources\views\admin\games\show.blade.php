@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <!-- Game Details Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0 heading-font">
                        <i class="fas fa-gamepad me-2"></i>{{ $game->display_name }}
                    </h5>
                    <small class="text-muted">Game Details and Configuration</small>
                </div>
                <div>
                    @if($game->is_active)
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>Active
                        </span>
                    @else
                        <span class="badge bg-secondary">
                            <i class="fas fa-times me-1"></i>Inactive
                        </span>
                    @endif
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-semibold">Game Name:</td>
                                <td><code class="game-name">{{ $game->minigame_name }}</code></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Display Name:</td>
                                <td>{{ $game->display_name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">View Path:</td>
                                <td><code class="view-path">{{ $game->view_path }}</code></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Created:</td>
                                <td>{{ $game->created_at->format('M d, Y \a\t H:i') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Last Updated:</td>
                                <td>{{ $game->updated_at->format('M d, Y \a\t H:i') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="heading-font mb-3">Description</h6>
                        @if($game->description)
                            <p class="text-muted">{{ $game->description }}</p>
                        @else
                            <p class="text-muted fst-italic">No description provided</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon bg-primary mx-auto mb-2">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="heading-font mb-0">{{ $game->gameConfigs->count() }}</h3>
                        <p class="text-muted mb-0">Total Configurations</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon bg-success mx-auto mb-2">
                            <i class="fas fa-play"></i>
                        </div>
                        <h3 class="heading-font mb-0">{{ $game->gameConfigs->where('is_starting', true)->count() }}</h3>
                        <p class="text-muted mb-0">Active Configs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon bg-info mx-auto mb-2">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="heading-font mb-0">{{ $game->gameConfigs->sum(function($config) { return $config->guestScores->count(); }) }}</h3>
                        <p class="text-muted mb-0">Total Plays</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon bg-warning mx-auto mb-2">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <h3 class="heading-font mb-0">{{ $game->gameConfigs->flatMap->guestScores->avg('score') ? number_format($game->gameConfigs->flatMap->guestScores->avg('score'), 1) : '0' }}</h3>
                        <p class="text-muted mb-0">Average Score</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Configurations -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0 heading-font">
                    <i class="fas fa-list me-2"></i>Game Configurations
                </h6>
                <a href="{{ route('admin.game-configs.create') }}?game_id={{ $game->id }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>Add Configuration
                </a>
            </div>
            <div class="card-body">
                @if($game->gameConfigs->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Game Title</th>
                                    <th>Top Players</th>
                                    <th>Status</th>
                                    <th>Plays</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($game->gameConfigs as $config)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="client-avatar me-2">
                                                {{ substr($config->client->client_name, 0, 1) }}
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $config->client->client_name }}</h6>
                                                <small class="text-muted">{{ $config->client->unique_url }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $config->game_title }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ $config->top_players }}</span>
                                    </td>
                                    <td>
                                        @if($config->is_starting)
                                            <span class="badge bg-success">Running</span>
                                        @elseif($config->is_done)
                                            <span class="badge bg-secondary">Completed</span>
                                        @else
                                            <span class="badge bg-warning">Pending</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $config->guestScores->count() }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.game-configs.show', $config) }}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.game-configs.edit', $config) }}" class="btn btn-sm btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-cogs fa-3x text-muted mb-3" style="opacity: 0.3;"></i>
                        <p class="text-muted mb-2">No configurations found</p>
                        <small class="text-muted">Create a configuration to start using this game</small>
                    </div>
                @endif
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="d-flex justify-content-between mt-4">
            <a href="{{ route('admin.games.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Games
            </a>
            <div>
                <a href="{{ route('admin.games.edit', $game) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-1"></i>Edit Game
                </a>
                <form action="{{ route('admin.games.destroy', $game) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this game? This will also delete all configurations.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Delete Game
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
/* Game details specific styles */
.game-name, .view-path {
    background: var(--light-pink);
    color: var(--text-dark);
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.9rem;
}

.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.client-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--secondary-pink) 0%, #f5c6cb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    font-weight: 600;
    font-size: 14px;
}
</style>
@endsection
