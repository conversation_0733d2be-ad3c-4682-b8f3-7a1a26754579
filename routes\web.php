<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Home\HomeController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('auth.login');
});

// Guest Minigame Routes - Updated URL structure with client and guest unique URLs
Route::prefix('game/trivia/{client_unique_url}/{guest_unique_url}')->name('guest.')->group(function () {
    Route::get('/', [App\Http\Controllers\Guest\MinigameController::class, 'trivia'])->name('minigame.trivia');
    Route::post('/submit', [App\Http\Controllers\Guest\MinigameController::class, 'submitTrivia'])->name('minigame.trivia.submit');
    Route::get('/leaderboard', [App\Http\Controllers\Guest\MinigameController::class, 'leaderboard'])->name('minigame.leaderboard');
});

// Legacy guest routes for backward compatibility (if needed)
Route::prefix('game/{client_id}/{unique_url}')->name('guest.legacy.')->group(function () {
    Route::get('/', [App\Http\Controllers\Guest\MinigameController::class, 'index'])->name('minigame.index');
    Route::get('/trivia', [App\Http\Controllers\Guest\MinigameController::class, 'triviaLegacy'])->name('minigame.trivia');
    Route::post('/trivia/submit', [App\Http\Controllers\Guest\MinigameController::class, 'submitTriviaLegacy'])->name('minigame.trivia.submit');
    Route::get('/leaderboard', [App\Http\Controllers\Guest\MinigameController::class, 'leaderboardLegacy'])->name('minigame.leaderboard');
});


Route::get('/auth-user', [App\Http\Controllers\Auth\AuthController::class, 'loginPage'])->name('auth.page');
Route::post('/auth-user', [App\Http\Controllers\Auth\AuthController::class, 'loginUser'])->name('auth.submit');
Route::get('/auth-logout', [App\Http\Controllers\Auth\AuthController::class, 'logout'])->name('auth.logout');


Auth::routes();
Route::get('/sign-out', [AuthController::class, 'logout'])->name('sign-out');

// Admin routes
Route::get('/admin', [AdminController::class, 'index']);
Route::resource('/admin/user', UserController::class);

// Admin Minigame routes - Only accessible by admin role
Route::prefix('admin')->name('admin.')->middleware(['auth', 'role:admin'])->group(function () {
    Route::get('dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');

    Route::resource('clients', App\Http\Controllers\Admin\ClientController::class);
    Route::get('clients/{client}/minigame-settings', [App\Http\Controllers\Admin\ClientController::class, 'minigameSettings'])->name('clients.minigame-settings');
    Route::put('clients/{client}/minigame-settings', [App\Http\Controllers\Admin\ClientController::class, 'updateMinigameSettings'])->name('clients.update-minigame-settings');

    Route::resource('games', App\Http\Controllers\Admin\GameController::class);
    Route::get('games-statistics', [App\Http\Controllers\Admin\GameController::class, 'statistics'])->name('games.statistics');

    Route::resource('game-configs', App\Http\Controllers\Admin\GameConfigController::class);
    Route::post('game-configs/{gameConfig}/start', [App\Http\Controllers\Admin\GameConfigController::class, 'start'])->name('game-configs.start');
    Route::post('game-configs/{gameConfig}/stop', [App\Http\Controllers\Admin\GameConfigController::class, 'stop'])->name('game-configs.stop');
    Route::post('game-configs/{gameConfig}/reset', [App\Http\Controllers\Admin\GameConfigController::class, 'reset'])->name('game-configs.reset');

    Route::resource('trivia-questions', App\Http\Controllers\Admin\TriviaQuestionController::class);
});

// Client Dashboard routes - Only accessible by client role
Route::prefix('client')->name('client.dashboard.')->middleware(['auth', 'role:client'])->group(function () {
    Route::get('dashboard', [App\Http\Controllers\Client\DashboardController::class, 'index'])->name('index');
    Route::get('minigames', [App\Http\Controllers\Client\DashboardController::class, 'minigames'])->name('minigames');
    Route::get('games/{gameConfig}', [App\Http\Controllers\Client\DashboardController::class, 'gameConfig'])->name('game-config');
    Route::post('games/{gameConfig}/start', [App\Http\Controllers\Client\DashboardController::class, 'startGame'])->name('start-game');
    Route::post('games/{gameConfig}/stop', [App\Http\Controllers\Client\DashboardController::class, 'stopGame'])->name('stop-game');
    Route::post('games/{gameConfig}/restart', [App\Http\Controllers\Client\DashboardController::class, 'restartGame'])->name('restart-game');
    Route::get('games/{gameConfig}/leaderboard', [App\Http\Controllers\Client\DashboardController::class, 'leaderboard'])->name('leaderboard');
    Route::get('games/{gameConfig}/live-leaderboard', [App\Http\Controllers\Client\DashboardController::class, 'liveLeaderboard'])->name('live-leaderboard');

    // Question management routes
    Route::get('games/{gameConfig}/questions', [App\Http\Controllers\Client\DashboardController::class, 'questions'])->name('questions');
    Route::post('games/{gameConfig}/questions', [App\Http\Controllers\Client\DashboardController::class, 'storeQuestion'])->name('questions.store');
    Route::put('questions/{question}', [App\Http\Controllers\Client\DashboardController::class, 'updateQuestion'])->name('questions.update');
    Route::delete('questions/{question}', [App\Http\Controllers\Client\DashboardController::class, 'deleteQuestion'])->name('questions.delete');
    Route::post('games/{gameConfig}/questions/bulk-import', [App\Http\Controllers\Client\DashboardController::class, 'bulkImportQuestions'])->name('questions.bulk-import');

    // Game configuration management
    Route::put('games/{gameConfig}/config', [App\Http\Controllers\Client\DashboardController::class, 'updateGameConfig'])->name('game-config.update');
});

// Public Minigame routes (for guests)
Route::prefix('minigame')->name('minigame.')->middleware('minigame.access')->group(function () {
    Route::get('trivia', [App\Http\Controllers\MinigameController::class, 'trivia'])->name('trivia');
    Route::post('trivia/submit', [App\Http\Controllers\MinigameController::class, 'submitTrivia'])->name('trivia.submit');
    Route::get('leaderboard', [App\Http\Controllers\MinigameController::class, 'leaderboard'])->name('leaderboard');
    Route::get('api/leaderboard', [App\Http\Controllers\MinigameController::class, 'liveLeaderboard'])->name('api.leaderboard');
    Route::get('api/trivia-questions', [App\Http\Controllers\MinigameController::class, 'getTriviaQuestions'])->name('api.trivia-questions');
});
