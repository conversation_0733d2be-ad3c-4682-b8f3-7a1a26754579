<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('guest_scores', function (Blueprint $table) {
            // Remove rank column since we'll calculate rankings dynamically
            if (Schema::hasColumn('guest_scores', 'rank')) {
                $table->dropColumn('rank');
            }
            
            // Add optimized indexes for better performance
            $table->index(['game_config_id', 'score', 'completed_at'], 'idx_leaderboard_ranking');
            $table->index(['guest_id', 'game_config_id', 'is_completed'], 'idx_guest_game_completion');
            $table->index(['client_id', 'is_completed'], 'idx_client_completed_scores');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('guest_scores', function (Blueprint $table) {
            // Re-add rank column
            $table->integer('rank')->nullable()->after('score');
            
            // Drop the optimized indexes
            $table->dropIndex('idx_leaderboard_ranking');
            $table->dropIndex('idx_guest_game_completion');
            $table->dropIndex('idx_client_completed_scores');
        });
    }
};
