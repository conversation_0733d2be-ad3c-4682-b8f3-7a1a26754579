<?php

namespace Database\Seeders;

use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\TriviaQuestion;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TriviaQuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first client with minigames enabled for demo purposes
        $client = Client::where('is_have_minigame', true)->first();

        if (!$client) {
            // Try to get any existing client and enable minigames
            $client = Client::first();

            if ($client) {
                $this->command->info('Enabling minigames for existing client: ' . $client->client_name);
                $client->update([
                    'is_have_minigame' => true,
                    'minigame_options' => ['trivia'],
                ]);
            } else {
                $this->command->error('No clients found in database. Please create a client first.');
                return;
            }
        }

        // Get trivia game
        $triviaGame = Game::where('minigame_name', 'trivia')->first();

        if (!$triviaGame) {
            $this->command->error('Trivia game not found. Please run GameSeeder first.');
            return;
        }

        // Create or get game config
        $gameConfig = GameConfig::firstOrCreate(
            [
                'client_id' => $client->id,
                'game_id' => $triviaGame->id,
            ],
            [
                'game_title' => 'Wedding Trivia Challenge',
                'top_players' => 10,
                'is_starting' => false,
                'is_done' => false,
            ]
        );

        // Sample trivia questions
        $questions = [
            [
                'question' => 'Where did the couple have their first date?',
                'options' => ['A Movie Theater', 'A Coffee Shop', 'A Fancy Restaurant', 'A Park Picnic'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 30,
                'order' => 1,
            ],
            [
                'question' => 'What is the couple\'s favorite movie to watch together?',
                'options' => ['The Notebook', 'Star Wars', 'The Princess Bride', 'Shrek'],
                'correct_answer' => 2,
                'points' => 100,
                'time_limit' => 30,
                'order' => 2,
            ],
            [
                'question' => 'How many countries have they traveled to together?',
                'options' => ['One', 'Three', 'Five', 'None yet!'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 30,
                'order' => 3,
            ],
            [
                'question' => 'What\'s the bride\'s favorite pizza topping?',
                'options' => ['Pepperoni', 'Mushrooms', 'Pineapple', 'Extra Cheese'],
                'correct_answer' => 2,
                'points' => 100,
                'time_limit' => 30,
                'order' => 4,
            ],
            [
                'question' => 'What\'s the groom\'s hidden talent?',
                'options' => ['Juggling', 'Singing Opera', 'Magic Tricks', 'Playing the Ukulele'],
                'correct_answer' => 3,
                'points' => 100,
                'time_limit' => 30,
                'order' => 5,
            ],
            [
                'question' => 'Which season did the proposal happen in?',
                'options' => ['Spring', 'Summer', 'Autumn', 'Winter'],
                'correct_answer' => 2,
                'points' => 100,
                'time_limit' => 30,
                'order' => 6,
            ],
        ];

        foreach ($questions as $questionData) {
            TriviaQuestion::updateOrCreate(
                [
                    'game_config_id' => $gameConfig->id,
                    'order' => $questionData['order'],
                ],
                array_merge($questionData, ['game_config_id' => $gameConfig->id])
            );
        }

        $this->command->info('Trivia questions seeded successfully for client: ' . $client->client_name);
        $this->command->info('Game Config ID: ' . $gameConfig->id);
        $this->command->info('Test URL: /minigame/trivia?client_id=' . $client->id);
    }
}
