<?php $__env->startSection('content'); ?>
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-edit me-2"></i>Edit Game: <?php echo e($game->display_name); ?>

                </h5>
                <small class="text-muted">Update game configuration and settings</small>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('admin.games.update', $game)); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="minigame_name" class="form-label">Game Name <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['minigame_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="minigame_name" name="minigame_name"
                                value="<?php echo e(old('minigame_name', $game->minigame_name)); ?>"
                                placeholder="e.g., trivia, memory_game" required>
                            <small class="form-text text-muted">Unique identifier for the game (lowercase, underscores
                                allowed)</small>
                            <?php $__errorArgs = ['minigame_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="display_name" class="form-label">Display Name <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['display_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="display_name" name="display_name"
                                value="<?php echo e(old('display_name', $game->display_name)); ?>"
                                placeholder="e.g., Wedding Trivia, Memory Challenge" required>
                            <small class="form-text text-muted">Human-readable name shown to users</small>
                            <?php $__errorArgs = ['display_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="view_path" class="form-label">View Path <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['view_path'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="view_path" name="view_path" value="<?php echo e(old('view_path', $game->view_path)); ?>"
                                placeholder="e.g., minigame.trivia.index" required>
                            <small class="form-text text-muted">Blade view path for the game interface</small>
                            <?php $__errorArgs = ['view_path'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="is_active" class="form-label">Status</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                    value="1" <?php echo e(old('is_active', $game->is_active) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_active">
                                    Active (available for use)
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="description"
                            name="description" rows="4"
                            placeholder="Describe the game mechanics, rules, or special features..."><?php echo e(old('description', $game->description)); ?></textarea>
                        <small class="form-text text-muted">Optional description for admin reference</small>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="mb-3">
                        <label for="game_thumbnail" class="form-label">Game Thumbnail</label>

                        <?php if($game->game_thumbnail): ?>
                        <div class="mb-3">
                            <img src="<?php echo e(asset('storage/' . $game->game_thumbnail)); ?>" alt="Current Thumbnail"
                                style="max-width: 200px; max-height: 150px; border-radius: 10px; border: 2px solid var(--secondary-pink);">
                            <small class="d-block text-muted mt-1">Current thumbnail</small>
                        </div>
                        <?php endif; ?>

                        <input type="file" class="form-control <?php $__errorArgs = ['game_thumbnail'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            id="game_thumbnail" name="game_thumbnail"
                            accept="image/jpeg,image/png,image/jpg,image/webp">
                        <small class="form-text text-muted">Upload a new thumbnail image (JPG, PNG, WebP - Max: 2MB) -
                            Leave empty to keep current</small>
                        <?php $__errorArgs = ['game_thumbnail'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                        <!-- Image Preview -->
                        <div id="thumbnail-preview" class="mt-3" style="display: none;">
                            <img id="preview-image" src="" alt="New Thumbnail Preview"
                                style="max-width: 200px; max-height: 150px; border-radius: 10px; border: 2px solid var(--primary-pink);">
                            <small class="d-block text-muted mt-1">New thumbnail preview</small>
                        </div>
                    </div>

                    <!-- Game Statistics -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0 heading-font">
                                <i class="fas fa-chart-bar me-2"></i>Game Statistics
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="heading-font text-primary"><?php echo e($game->gameConfigs->count()); ?></h4>
                                        <small class="text-muted">Total Configurations</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="heading-font text-success"><?php echo e($game->gameConfigs->where('is_starting', true)->count()); ?></h4>
                                        <small class="text-muted">Active Configs</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="heading-font text-info"><?php echo e($game->gameConfigs->sum(function($config)
                                            { return $config->guestScores->count(); })); ?></h4>
                                        <small class="text-muted">Total Plays</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="heading-font text-warning"><?php echo e($game->created_at->format('M Y')); ?>

                                        </h4>
                                        <small class="text-muted">Created</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?php echo e(route('admin.games.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Games
                        </a>
                        <div>
                            <a href="<?php echo e(route('admin.games.show', $game)); ?>" class="btn btn-info me-2">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Game
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Form specific styles */
    .form-check-input:checked {
        background-color: var(--primary-pink) !important;
        border-color: var(--primary-pink) !important;
    }

    .form-check-input:focus {
        border-color: var(--primary-pink) !important;
        box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25) !important;
    }

    .is-invalid {
        border-color: #dc3545 !important;
    }

    .invalid-feedback {
        display: block;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const thumbnailInput = document.getElementById('game_thumbnail');
        const previewDiv = document.getElementById('thumbnail-preview');
        const previewImage = document.getElementById('preview-image');

        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];

            if (file) {
                // Validate file type
                const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG, PNG, WebP)');
                    this.value = '';
                    previewDiv.style.display = 'none';
                    return;
                }

                // Validate file size (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    alert('File size must be less than 2MB');
                    this.value = '';
                    previewDiv.style.display = 'none';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                    previewDiv.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                previewDiv.style.display = 'none';
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/admin/games/edit.blade.php ENDPATH**/ ?>