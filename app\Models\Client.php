<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    protected $fillable = [
        'user_id',
        'package_id',
        'template_id',
        'client_name',
        'unique_url',
        'bride_fullname',
        'bride_nickname',
        'bride_father_name',
        'bride_mother_name',
        'groom_fullname',
        'groom_nickname',
        'groom_father_name',
        'groom_mother_name',
        'is_multi_prefix',
        'is_whatsapp_broadcast',
        'prefixes',
        'is_have_minigame',
        'minigame_options',
    ];

    protected $casts = [
        'is_have_minigame' => 'boolean',
        'minigame_options' => 'array',
        'is_multi_prefix' => 'boolean',
        'is_whatsapp_broadcast' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function package()
    {
        return $this->belongsTo(Package::class);
    }

    public function template()
    {
        return $this->belongsTo(Template::class);
    }

    public function guests()
    {
        return $this->hasMany(Guest::class);
    }

    public function clientConfig()
    {
        return $this->hasOne(ClientConfig::class);
    }

    public function tasks()
    {
        return $this->hasMany(Task::class);
    }

    public function gameConfigs()
    {
        return $this->hasMany(GameConfig::class);
    }

    public function guestScores()
    {
        return $this->hasMany(GuestScore::class);
    }
}
