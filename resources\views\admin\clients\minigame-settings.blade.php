@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-gamepad me-2"></i>Minigame Settings for {{ $client->client_name }}
                </h5>
                <small class="text-muted">Configure minigame options and game-specific settings</small>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.clients.update-minigame-settings', $client) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <!-- Minigame Enable/Disable -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="heading-font">
                                        <i class="fas fa-toggle-on me-2"></i>Minigame Status
                                    </h6>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_have_minigame"
                                            name="is_have_minigame" value="1" {{ $client->is_have_minigame ? 'checked' :
                                        '' }}>
                                        <label class="form-check-label" for="is_have_minigame">
                                            Enable Minigames for this client
                                        </label>
                                    </div>
                                    <small class="text-muted">When enabled, guests can access and play minigames</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="heading-font">
                                        <i class="fas fa-list me-2"></i>Available Games
                                    </h6>
                                    @foreach($games as $game)
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="minigame_options[]"
                                            value="{{ $game->minigame_name }}" id="game_{{ $game->id }}" {{
                                            in_array($game->minigame_name, $client->minigame_options ?? []) ? 'checked'
                                        : '' }}>
                                        <label class="form-check-label" for="game_{{ $game->id }}">
                                            {{ $game->display_name }}
                                            <small class="text-muted d-block">{{ $game->description }}</small>
                                        </label>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Game Configurations -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="heading-font" style="margin-bottom: 20px;">
                                <i class="fas fa-cogs me-2"></i>Game Configurations
                            </h6>

                            @foreach($games as $game)
                            @php
                            $existingConfig = $client->gameConfigs->where('game_id', $game->id)->first();
                            @endphp
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0 heading-font">
                                        <i class="fas fa-gamepad me-2"></i>{{ $game->display_name }}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="form-label">Game Title</label>
                                                <input type="text" class="form-control"
                                                    name="game_configs[{{ $game->id }}][game_title]"
                                                    value="{{ $existingConfig->game_title ?? $game->display_name }}"
                                                    placeholder="Enter custom game title">
                                                <input type="hidden" name="game_configs[{{ $game->id }}][game_id]"
                                                    value="{{ $game->id }}">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="form-label">Top Players (Winners)</label>
                                                <select class="form-control"
                                                    name="game_configs[{{ $game->id }}][top_players]">
                                                    @for($i = 1; $i <= 20; $i++) <option value="{{ $i }}" {{
                                                        ($existingConfig->top_players ?? 10) == $i ? 'selected' : '' }}>
                                                        Top {{ $i }} {{ $i == 1 ? 'Winner' : 'Winners' }}
                                                        </option>
                                                        @endfor
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="form-label">Game Status</label>
                                                <div class="d-flex gap-2">
                                                    <span
                                                        class="badge {{ $existingConfig && $existingConfig->is_starting ? 'bg-success' : 'bg-secondary' }}">
                                                        {{ $existingConfig && $existingConfig->is_starting ? 'Active' :
                                                        'Inactive' }}
                                                    </span>
                                                    <span
                                                        class="badge {{ $existingConfig && $existingConfig->is_done ? 'bg-info' : 'bg-light text-dark' }}">
                                                        {{ $existingConfig && $existingConfig->is_done ? 'Completed' :
                                                        'Not Started' }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    @if($game->minigame_name === 'trivia')
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <h6 class="heading-font">Trivia Specific Settings</h6>
                                            <div class="form-group">
                                                <label class="form-label">Questions Configuration</label>
                                                <textarea class="form-control" rows="3"
                                                    name="game_configs[{{ $game->id }}][config_data][questions_note]"
                                                    placeholder="Add notes about trivia questions or special instructions">{{ $existingConfig->config_data['questions_note'] ?? '' }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('admin.clients.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to Clients
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Save Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Additional styles for minigame settings */
    .form-check-input:checked {
        background-color: var(--primary-pink) !important;
        border-color: var(--primary-pink) !important;
    }

    .form-check-input:focus {
        border-color: var(--primary-pink) !important;
        box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25) !important;
    }
</style>
@endsection