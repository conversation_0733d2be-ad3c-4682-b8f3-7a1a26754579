<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GameConfig extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'game_id',
        'game_title',
        'top_players',
        'is_starting',
        'is_done',
        'config_data',
    ];

    protected $casts = [
        'is_starting' => 'boolean',
        'is_done' => 'boolean',
        'config_data' => 'array',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function game()
    {
        return $this->belongsTo(Game::class);
    }

    public function guestScores()
    {
        return $this->hasMany(GuestScore::class);
    }

    public function triviaQuestions()
    {
        return $this->hasMany(TriviaQuestion::class);
    }

    public function leaderboard($limit = 10)
    {
        return GuestScore::getLeaderboard($this->id, $limit);
    }
}
