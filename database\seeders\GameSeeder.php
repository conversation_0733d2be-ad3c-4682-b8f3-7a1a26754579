<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Game;

class GameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $games = [
            [
                'minigame_name' => 'trivia',
                'view_path' => 'minigame.trivia.index',
                'display_name' => 'Wedding Trivia',
                'description' => 'Test your knowledge about the happy couple with fun trivia questions!',
                'is_active' => true,
            ],
            [
                'minigame_name' => 'spinwheel',
                'view_path' => 'minigame.spinwheel.index',
                'display_name' => 'Spin the Wheel',
                'description' => 'Spin the wheel for exciting prizes and surprises!',
                'is_active' => false, // Not implemented yet
            ],
        ];

        foreach ($games as $game) {
            Game::updateOrCreate(
                ['minigame_name' => $game['minigame_name']],
                $game
            );
        }
    }
}
