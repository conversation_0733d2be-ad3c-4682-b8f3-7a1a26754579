@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-plus-circle me-2"></i>Create New Client
                </h5>
                <small class="text-muted">Add a new client to the wedding minigame system</small>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.clients.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <!-- Client Name -->
                        <div class="col-md-6 mb-3">
                            <label for="client_name" class="form-label">Client Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('client_name') is-invalid @enderror" 
                                   id="client_name" name="client_name" value="{{ old('client_name') }}" 
                                   placeholder="Enter client's full name" required>
                            <small class="form-text text-muted">This will be displayed throughout the system</small>
                            @error('client_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Unique URL -->
                        <div class="col-md-6 mb-3">
                            <label for="unique_url" class="form-label">Unique URL <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('unique_url') is-invalid @enderror" 
                                   id="unique_url" name="unique_url" value="{{ old('unique_url') }}" 
                                   placeholder="e.g., john-jane-wedding" required>
                            <small class="form-text text-muted">Used in guest access URLs (letters, numbers, hyphens only)</small>
                            @error('unique_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Minigame Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0 heading-font">
                                <i class="fas fa-gamepad me-2"></i>Minigame Settings
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_have_minigame" 
                                       name="is_have_minigame" value="1" {{ old('is_have_minigame') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_have_minigame">
                                    <strong>Enable Minigames</strong>
                                    <small class="d-block text-muted">Allow this client to access and use minigames</small>
                                </label>
                            </div>

                            <div id="minigame-options" style="display: {{ old('is_have_minigame') ? 'block' : 'none' }};">
                                <h6 class="mb-3">Available Games:</h6>
                                <div class="row">
                                    @foreach($games as $game)
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-light">
                                            <div class="card-body p-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="game_{{ $game->id }}" name="minigame_options[]" 
                                                           value="{{ $game->id }}"
                                                           {{ in_array($game->id, old('minigame_options', [])) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="game_{{ $game->id }}">
                                                        <strong>{{ $game->display_name }}</strong>
                                                        <small class="d-block text-muted">{{ $game->description ?? 'No description available' }}</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Client Information -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0 heading-font">
                                <i class="fas fa-info-circle me-2"></i>Additional Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email') }}" 
                                           placeholder="<EMAIL>">
                                    <small class="form-text text-muted">Optional - for notifications and updates</small>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone') }}" 
                                           placeholder="+****************">
                                    <small class="form-text text-muted">Optional - for contact purposes</small>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="event_date" class="form-label">Event Date</label>
                                <input type="date" class="form-control @error('event_date') is-invalid @enderror" 
                                       id="event_date" name="event_date" value="{{ old('event_date') }}">
                                <small class="form-text text-muted">Optional - wedding or event date</small>
                                @error('event_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="3" 
                                          placeholder="Any additional notes or special requirements...">{{ old('notes') }}</textarea>
                                <small class="form-text text-muted">Optional - internal notes for reference</small>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.clients.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Clients
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Create Client
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
/* Form specific styles */
.form-check-input:checked {
    background-color: var(--primary-pink) !important;
    border-color: var(--primary-pink) !important;
}

.form-check-input:focus {
    border-color: var(--primary-pink) !important;
    box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25) !important;
}

.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    display: block;
}

/* Enhanced form styling */
.form-control:focus {
    border-color: var(--primary-pink);
    box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25);
}

.form-check-label {
    cursor: pointer;
}

.form-check-label strong {
    color: var(--text-dark);
}

/* Card styling */
.card .card-header h6 {
    color: var(--primary-pink);
}

.border-light {
    border-color: rgba(251, 207, 232, 0.5) !important;
}
</style>

<script>
// Toggle minigame options visibility
document.getElementById('is_have_minigame').addEventListener('change', function() {
    const minigameOptions = document.getElementById('minigame-options');
    if (this.checked) {
        minigameOptions.style.display = 'block';
    } else {
        minigameOptions.style.display = 'none';
        // Uncheck all game options
        document.querySelectorAll('input[name="minigame_options[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });
    }
});

// Auto-generate unique URL from client name
document.getElementById('client_name').addEventListener('input', function() {
    const uniqueUrlField = document.getElementById('unique_url');
    if (!uniqueUrlField.value) {
        const cleanName = this.value
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
        uniqueUrlField.value = cleanName;
    }
});

// Validate unique URL format
document.getElementById('unique_url').addEventListener('input', function() {
    this.value = this.value
        .toLowerCase()
        .replace(/[^a-z0-9-]/g, '')
        .replace(/-+/g, '-');
});
</script>
@endsection
