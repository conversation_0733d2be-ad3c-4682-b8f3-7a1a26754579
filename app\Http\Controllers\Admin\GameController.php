<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Game;
use App\Models\GameConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GameController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'admin') {
                abort(403, 'Access Denied. Only admin role is allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $games = Game::withCount('gameConfigs')->paginate(15);

        return view('admin.games.index', compact('games'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.games.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'minigame_name' => 'required|string|unique:games,minigame_name|max:255',
            'view_path' => 'required|string|max:255',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'game_thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        // Handle thumbnail upload
        if ($request->hasFile('game_thumbnail')) {
            $file = $request->file('game_thumbnail');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('game-thumbnails', $filename, 'public');
            $data['game_thumbnail'] = $path;
        }

        Game::create($data);

        alert()->success('Success', 'Game created successfully!');
        return redirect()->route('admin.games.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(Game $game)
    {
        $game->load(['gameConfigs.client', 'gameConfigs.guestScores']);

        return view('admin.games.show', compact('game'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Game $game)
    {
        return view('admin.games.edit', compact('game'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Game $game)
    {
        $request->validate([
            'minigame_name' => 'required|string|max:255|unique:games,minigame_name,' . $game->id,
            'view_path' => 'required|string|max:255',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'game_thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        // Handle thumbnail upload
        if ($request->hasFile('game_thumbnail')) {
            // Delete old thumbnail if exists
            if ($game->game_thumbnail && \Storage::disk('public')->exists($game->game_thumbnail)) {
                \Storage::disk('public')->delete($game->game_thumbnail);
            }

            $file = $request->file('game_thumbnail');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('game-thumbnails', $filename, 'public');
            $data['game_thumbnail'] = $path;
        }

        $game->update($data);

        alert()->success('Success', 'Game updated successfully!');
        return redirect()->route('admin.games.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Game $game)
    {
        $game->delete();

        alert()->success('Success', 'Game deleted successfully!');
        return redirect()->route('admin.games.index');
    }

    /**
     * Get game statistics
     */
    public function statistics()
    {
        $totalGames = Game::count();
        $activeGames = Game::where('is_active', true)->count();
        $totalConfigs = GameConfig::count();
        $activeConfigs = GameConfig::where('is_starting', true)->count();

        return view('admin.games.statistics', compact(
            'totalGames', 'activeGames', 'totalConfigs', 'activeConfigs'
        ));
    }
}
