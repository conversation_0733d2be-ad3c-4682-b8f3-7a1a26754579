<?php

namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Enrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class StudentController extends Controller
{
    public function index()
    {
        // Pastikan pengguna sudah login
        if (!Auth::check()) {
            return redirect()->route('home.sign-in');
        }

        // Ambil data pengguna yang sedang login
        $user = Auth::user();

        $courseIds = Enrollment::where([
                    ['payment_status', 'paid'],
                    ['user_id', auth()->user()->id]
                ])->pluck('course_id');

        $courses = Course::whereIn('id', $courseIds)->with(['instructor.detailInstructor', 'reviews.user', 'enrolled', 'category', 'lessons'])->get();


        // Logika untuk mengambil data kursus yang diikuti, aktif, dan selesai
        $enrolledCourses = Enrollment::where(['payment_status' => 'paid', 'user_id' => auth()->user()->id])->count();
        $activeCourses = Enrollment::where(['payment_status' => 'paid', 'user_id' => auth()->user()->id, 'status' => 'on progress'])->count();
        $completedCourses = Enrollment::where(['payment_status' => 'paid', 'user_id' => auth()->user()->id, 'status' => 'passed'])->count();
        $waitingConfirm = Enrollment::where(['payment_status' => 'checking', 'user_id' => auth()->user()->id])->count();
        // Logika untuk mengambil kursus yang baru-baru ini diikuti
        $recentlyEnrolledCourses = []; // Ganti dengan logika sebenarnya

        return view('student.index', compact('courses', 'user', 'enrolledCourses', 'activeCourses', 'completedCourses', 'recentlyEnrolledCourses', 'waitingConfirm'));
    }
}
