<?php $__env->startSection('title', 'Demo Results - Pecatu Minigame'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .results-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
    }

    .score-header {
        text-align: center;
        margin-bottom: 3rem;
        padding: 3rem 2rem;
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        color: white;
        border-radius: 20px;
        position: relative;
        overflow: hidden;
    }

    .score-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0%, 100% { transform: rotate(0deg); }
        50% { transform: rotate(180deg); }
    }

    .score-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        position: relative;
        z-index: 1;
    }

    .score-text {
        font-size: 2.5rem;
        font-weight: 700;
        position: relative;
        z-index: 1;
    }

    .score-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        position: relative;
        z-index: 1;
    }

    .performance-badge {
        display: inline-block;
        padding: 8px 20px;
        border-radius: 20px;
        font-weight: 600;
        margin-top: 1rem;
        position: relative;
        z-index: 1;
    }

    .badge-excellent {
        background: rgba(40, 167, 69, 0.2);
        color: #28a745;
        border: 2px solid rgba(40, 167, 69, 0.3);
    }

    .badge-good {
        background: rgba(255, 193, 7, 0.2);
        color: #ffc107;
        border: 2px solid rgba(255, 193, 7, 0.3);
    }

    .badge-fair {
        background: rgba(255, 152, 0, 0.2);
        color: #ff9800;
        border: 2px solid rgba(255, 152, 0, 0.3);
    }

    .badge-needs-improvement {
        background: rgba(220, 53, 69, 0.2);
        color: #dc3545;
        border: 2px solid rgba(220, 53, 69, 0.3);
    }

    .question-review {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
    }

    .question-review.correct {
        border-color: #28a745;
        background: rgba(40, 167, 69, 0.05);
    }

    .question-review.incorrect {
        border-color: #dc3545;
        background: rgba(220, 53, 69, 0.05);
    }

    .question-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.9rem;
        margin-right: 1rem;
    }

    .question-number.correct {
        background: #28a745;
        color: white;
    }

    .question-number.incorrect {
        background: #dc3545;
        color: white;
    }

    .option-review {
        padding: 0.5rem 1rem;
        margin: 0.25rem 0;
        border-radius: 10px;
        font-size: 0.9rem;
    }

    .option-review.correct-answer {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
        font-weight: 600;
    }

    .option-review.user-answer.incorrect {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        font-weight: 600;
    }

    .cta-section {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        margin-top: 3rem;
    }

    .btn-action {
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        margin: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-outline {
        background: transparent;
        border: 2px solid var(--primary-pink);
        color: var(--primary-pink);
    }

    .btn-outline:hover {
        background: var(--primary-pink);
        color: white;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <div class="results-container">
        <!-- Score Header -->
        <div class="score-header">
            <div class="score-circle">
                <div class="score-text"><?php echo e($percentage); ?>%</div>
            </div>
            <h1 style="margin-bottom: 0.5rem;">Great Job, <?php echo e($request->player_name); ?>!</h1>
            <p class="score-subtitle">You scored <?php echo e($score); ?> out of <?php echo e($totalQuestions); ?> questions correctly</p>
            
            <?php if($percentage >= 80): ?>
                <div class="performance-badge badge-excellent">
                    <i class="fas fa-star me-2"></i>Excellent Performance!
                </div>
            <?php elseif($percentage >= 60): ?>
                <div class="performance-badge badge-good">
                    <i class="fas fa-thumbs-up me-2"></i>Good Job!
                </div>
            <?php elseif($percentage >= 40): ?>
                <div class="performance-badge badge-fair">
                    <i class="fas fa-smile me-2"></i>Not Bad!
                </div>
            <?php else: ?>
                <div class="performance-badge badge-needs-improvement">
                    <i class="fas fa-heart me-2"></i>Keep Learning!
                </div>
            <?php endif; ?>
        </div>

        <!-- Question Review -->
        <div class="mb-4">
            <h3 style="color: var(--text-dark); margin-bottom: 1.5rem; text-align: center;">
                <i class="fas fa-clipboard-list me-2"></i>Review Your Answers
            </h3>
            
            <?php $__currentLoopData = $results; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="question-review <?php echo e($result['is_correct'] ? 'correct' : 'incorrect'); ?>">
                <div class="d-flex align-items-start">
                    <div class="question-number <?php echo e($result['is_correct'] ? 'correct' : 'incorrect'); ?>">
                        <?php echo e($index + 1); ?>

                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold mb-2" style="color: var(--text-dark);">
                            <?php echo e($result['question']); ?>

                        </div>
                        
                        <?php $__currentLoopData = $result['options']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optionIndex => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="option-review 
                            <?php if($optionIndex === $result['correct_answer']): ?> correct-answer <?php endif; ?>
                            <?php if($optionIndex === $result['user_answer'] && !$result['is_correct']): ?> user-answer incorrect <?php endif; ?>">
                            <strong><?php echo e(chr(65 + $optionIndex)); ?>.</strong> <?php echo e($option); ?>

                            <?php if($optionIndex === $result['correct_answer']): ?>
                                <i class="fas fa-check ms-2"></i>
                            <?php endif; ?>
                            <?php if($optionIndex === $result['user_answer'] && !$result['is_correct']): ?>
                                <i class="fas fa-times ms-2"></i>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- CTA Section -->
        <div class="cta-section">
            <h3 style="color: var(--text-dark); margin-bottom: 1rem;">Ready to Create Your Own?</h3>
            <p style="color: var(--text-light); margin-bottom: 2rem;">
                This was just a demo! Create personalized trivia games for your wedding with custom questions about you and your partner.
            </p>
            
            <div class="d-flex gap-3 justify-content-center flex-wrap">
                <a href="<?php echo e(route('auth.page')); ?>" class="btn-action">
                    <i class="fas fa-rocket me-2"></i>Get Started
                </a>
                <a href="<?php echo e(route('landing.trivia-demo')); ?>" class="btn-action btn-outline">
                    <i class="fas fa-redo me-2"></i>Try Again
                </a>
                <a href="<?php echo e(route('landing.index')); ?>" class="btn-action btn-outline">
                    <i class="fas fa-home me-2"></i>Back to Home
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Add confetti effect for high scores
    <?php if($percentage >= 80): ?>
    document.addEventListener('DOMContentLoaded', function() {
        // Simple confetti effect
        function createConfetti() {
            const colors = ['#ec4899', '#f8d7da', '#fdf2f8', '#be185d'];
            const confettiCount = 50;
            
            for (let i = 0; i < confettiCount; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'fixed';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';
                confetti.style.zIndex = '9999';
                confetti.style.pointerEvents = 'none';
                confetti.style.borderRadius = '50%';
                
                document.body.appendChild(confetti);
                
                const animation = confetti.animate([
                    { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
                    { transform: `translateY(100vh) rotate(${Math.random() * 360}deg)`, opacity: 0 }
                ], {
                    duration: Math.random() * 3000 + 2000,
                    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
                });
                
                animation.onfinish = () => confetti.remove();
            }
        }
        
        // Trigger confetti after a short delay
        setTimeout(createConfetti, 500);
    });
    <?php endif; ?>
    
    // Smooth scroll to review section
    document.addEventListener('DOMContentLoaded', function() {
        const reviewSection = document.querySelector('.question-review');
        if (reviewSection) {
            setTimeout(() => {
                reviewSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 2000);
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('landing.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\pecatu-minigame\resources\views/landing/trivia-results.blade.php ENDPATH**/ ?>