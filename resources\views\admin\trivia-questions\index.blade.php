@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-question-circle me-2"></i>Trivia Questions Management
                </h5>
                <a href="{{ route('admin.trivia-questions.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>Add New Question
                </a>
            </div>
            <div class="card-body">
                <!-- Filter Section -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <select class="form-control" id="gameConfigFilter">
                            <option value="">All Game Configurations</option>
                            @foreach($gameConfigs as $config)
                                <option value="{{ $config->id }}">{{ $config->client->client_name }} - {{ $config->game_title }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchQuestions" placeholder="Search questions...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Question</th>
                                <th>Game Configuration</th>
                                <th>Correct Answer</th>
                                <th>Options</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($questions as $question)
                            <tr>
                                <td>{{ $loop->iteration + ($questions->currentPage() - 1) * $questions->perPage() }}</td>
                                <td>
                                    <div class="question-preview">
                                        <h6 class="mb-1">{{ Str::limit($question->question_text, 60) }}</h6>
                                        @if($question->question_image)
                                            <small class="text-info">
                                                <i class="fas fa-image me-1"></i>Has image
                                            </small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="config-info">
                                        <h6 class="mb-0">{{ $question->gameConfig->game_title }}</h6>
                                        <small class="text-muted">{{ $question->gameConfig->client->client_name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ Str::limit($question->correct_answer, 20) }}</span>
                                </td>
                                <td>
                                    @if($question->option_a || $question->option_b || $question->option_c || $question->option_d)
                                        <div class="options-preview">
                                            @if($question->option_a)<span class="badge bg-light text-dark me-1">A</span>@endif
                                            @if($question->option_b)<span class="badge bg-light text-dark me-1">B</span>@endif
                                            @if($question->option_c)<span class="badge bg-light text-dark me-1">C</span>@endif
                                            @if($question->option_d)<span class="badge bg-light text-dark me-1">D</span>@endif
                                        </div>
                                    @else
                                        <span class="text-muted">Open-ended</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.trivia-questions.show', $question) }}" class="btn btn-sm btn-outline-info" title="View Question">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.trivia-questions.edit', $question) }}" class="btn btn-sm btn-outline-warning" title="Edit Question">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.trivia-questions.destroy', $question) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this question?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Question">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div style="color: #6c757d;">
                                        <i class="fas fa-question-circle fa-3x mb-3" style="opacity: 0.3;"></i>
                                        <p class="mb-0">No trivia questions found</p>
                                        <small>Create your first question to get started</small>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                
                @if($questions->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $questions->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
/* Trivia questions specific styles */
.question-preview h6 {
    color: var(--text-dark);
    font-weight: 600;
}

.config-info h6 {
    color: var(--primary-pink);
    font-weight: 600;
}

.options-preview .badge {
    font-size: 0.7rem;
    padding: 3px 6px;
}

.btn-group .btn {
    margin-right: 2px;
}

/* Filter styles */
#gameConfigFilter, #searchQuestions {
    border: 2px solid rgba(251, 207, 232, 0.5);
    border-radius: 12px;
    transition: all 0.3s ease;
}

#gameConfigFilter:focus, #searchQuestions:focus {
    border-color: var(--primary-pink);
    box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25);
}
</style>

<script>
// Simple client-side filtering
document.getElementById('gameConfigFilter').addEventListener('change', function() {
    // Implementation for filtering by game config
    console.log('Filter by game config:', this.value);
});

document.getElementById('searchQuestions').addEventListener('input', function() {
    // Implementation for search functionality
    console.log('Search questions:', this.value);
});
</script>
@endsection
