@extends('landing.layout')

@section('title', 'Pecatu Minigame - Interactive Wedding Entertainment')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h1 class="hero-title">Interactive Wedding Games</h1>
                <p class="hero-subtitle">Create unforgettable moments with engaging trivia games and interactive entertainment for your special day</p>
                <a href="{{ route('landing.trivia-demo') }}" class="btn-demo">
                    <i class="fas fa-play me-2"></i>Try Demo
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Games Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 style="color: var(--text-dark); font-weight: 700; margin-bottom: 1rem;">Available Games</h2>
                <p style="color: var(--text-light); font-size: 1.1rem;">Choose from our collection of interactive wedding games designed to entertain your guests</p>
            </div>
        </div>
        
        <div class="row">
            @forelse($games as $game)
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="game-card">
                    @if($game->game_thumbnail)
                        <img src="{{ asset('storage/' . $game->game_thumbnail) }}" alt="{{ $game->display_name }}" class="game-thumbnail">
                    @else
                        <div class="game-thumbnail d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, var(--secondary-pink) 0%, var(--light-pink) 100%);">
                            <i class="fas fa-gamepad" style="font-size: 3rem; color: var(--primary-pink);"></i>
                        </div>
                    @endif
                    
                    <h3 class="game-title">{{ $game->display_name }}</h3>
                    <p class="game-description">{{ $game->description ?: 'Engage your guests with this interactive game experience.' }}</p>
                    
                    @if($game->minigame_name === 'trivia')
                        <a href="{{ route('landing.trivia-demo') }}" class="btn-demo">
                            <i class="fas fa-play me-2"></i>Try Demo
                        </a>
                    @else
                        <span class="btn-demo" style="opacity: 0.6; cursor: not-allowed;">
                            <i class="fas fa-lock me-2"></i>Coming Soon
                        </span>
                    @endif
                </div>
            </div>
            @empty
            <div class="col-12 text-center">
                <div class="game-card">
                    <div class="game-thumbnail d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, var(--secondary-pink) 0%, var(--light-pink) 100%);">
                        <i class="fas fa-question-circle" style="font-size: 3rem; color: var(--primary-pink);"></i>
                    </div>
                    <h3 class="game-title">Wedding Trivia</h3>
                    <p class="game-description">Test your guests' knowledge with customizable trivia questions about the happy couple and wedding traditions.</p>
                    <a href="{{ route('landing.trivia-demo') }}" class="btn-demo">
                        <i class="fas fa-play me-2"></i>Try Demo
                    </a>
                </div>
            </div>
            @endforelse
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section" style="background: rgba(255, 255, 255, 0.5);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 style="color: var(--text-dark); font-weight: 700; margin-bottom: 1rem;">Why Choose Our Platform?</h2>
                <p style="color: var(--text-light); font-size: 1.1rem;">Everything you need to create engaging entertainment for your wedding guests</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Mobile Friendly</h4>
                    <p style="color: var(--text-light);">Guests can play directly from their smartphones - no app downloads required.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Easy Setup</h4>
                    <p style="color: var(--text-light);">Create and customize your games in minutes with our intuitive dashboard.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Real-time Leaderboards</h4>
                    <p style="color: var(--text-light);">Keep guests engaged with live scoring and competitive leaderboards.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Customizable</h4>
                    <p style="color: var(--text-light);">Personalize questions and themes to match your wedding style.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Guest Management</h4>
                    <p style="color: var(--text-light);">Easily manage guest access with unique links for each attendee.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Analytics</h4>
                    <p style="color: var(--text-light);">Track engagement and see how your guests are enjoying the games.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5" style="background: linear-gradient(135deg, var(--light-pink) 0%, rgba(251, 207, 232, 0.3) 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 style="color: var(--text-dark); font-weight: 700; margin-bottom: 1rem;">Ready to Get Started?</h2>
                <p style="color: var(--text-light); font-size: 1.1rem; margin-bottom: 2rem;">Join couples who have made their weddings unforgettable with interactive entertainment</p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="{{ route('landing.trivia-demo') }}" class="btn-demo">
                        <i class="fas fa-play me-2"></i>Try Demo
                    </a>
                    <a href="{{ route('auth.page') }}" class="btn-demo" style="background: transparent; border: 2px solid var(--primary-pink); color: var(--primary-pink);">
                        <i class="fas fa-sign-in-alt me-2"></i>Get Started
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // Add animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all game cards and feature items
    document.querySelectorAll('.game-card, .feature-icon').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.6s ease';
        observer.observe(el);
    });
</script>
@endsection
