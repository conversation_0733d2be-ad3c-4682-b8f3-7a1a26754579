@extends('landing.layout')

@section('title', 'Pecatu Minigame - Interactive Wedding Entertainment')

@section('styles')
<style>
    .game-card {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(251, 207, 232, 0.3);
        border-radius: 25px;
        padding: 2rem;
        text-align: center;
        transition: all 0.4s ease;
        height: 100%;
        backdrop-filter: blur(15px);
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(236, 72, 153, 0.1);
    }

    .game-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(236, 72, 153, 0.05) 0%, transparent 70%);
        transition: all 0.4s ease;
        transform: scale(0);
    }

    .game-card:hover::before {
        transform: scale(1);
    }

    .game-card:hover {
        transform: translateY(-15px);
        box-shadow: 0 20px 40px rgba(236, 72, 153, 0.25);
        border-color: var(--primary-pink);
    }

    .game-thumbnail {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 20px;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .game-title {
        color: var(--text-dark);
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        position: relative;
        z-index: 1;
    }

    .game-description {
        color: var(--text-light);
        margin-bottom: 1.5rem;
        line-height: 1.6;
        position: relative;
        z-index: 1;
    }

    .btn-demo {
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
    }

    .btn-demo:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(236, 72, 153, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-demo.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    }

    .btn-demo.disabled:hover {
        transform: none;
        box-shadow: none;
    }

    .features-section {
        padding: 100px 0;
        background: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .feature-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: white;
        font-size: 2.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .feature-icon::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transition: all 0.3s ease;
        transform: translate(-50%, -50%);
    }

    .feature-icon:hover::before {
        width: 100%;
        height: 100%;
    }

    .feature-icon:hover {
        transform: scale(1.1) rotate(5deg);
    }

    .cta-section {
        padding: 100px 0;
        background: linear-gradient(135deg, var(--light-pink) 0%, rgba(251, 207, 232, 0.4) 100%);
        position: relative;
    }

    .cta-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ec4899' fill-opacity='0.05'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        z-index: 0;
    }

    .section-title {
        color: var(--text-dark);
        font-weight: 700;
        margin-bottom: 1rem;
        position: relative;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        width: 60px;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
        border-radius: 2px;
        transform: translateX(-50%);
    }

    .wedding-rings {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 2rem;
        color: rgba(236, 72, 153, 0.2);
        animation: rotate 10s linear infinite;
    }

    @keyframes rotate {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    .floral-border {
        border: 3px solid transparent;
        background: linear-gradient(white, white) padding-box,
            linear-gradient(45deg, var(--primary-pink), var(--accent-pink)) border-box;
        border-radius: 25px;
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1200px) {
        .game-card {
            padding: 1.8rem;
        }

        .feature-icon {
            width: 90px;
            height: 90px;
            font-size: 2.2rem;
        }
    }

    @media (max-width: 992px) {
        .game-card {
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .game-thumbnail {
            height: 180px;
        }

        .features-section {
            padding: 80px 0;
        }

        .cta-section {
            padding: 80px 0;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            font-size: 2rem;
        }
    }

    @media (max-width: 768px) {
        .game-card {
            padding: 1.2rem;
            margin-bottom: 1.5rem;
        }

        .game-thumbnail {
            height: 160px;
            margin-bottom: 1rem;
        }

        .game-title {
            font-size: 1.3rem;
            margin-bottom: 0.8rem;
        }

        .game-description {
            font-size: 0.9rem;
            margin-bottom: 1.2rem;
        }

        .btn-demo {
            padding: 10px 25px;
            font-size: 0.9rem;
        }

        .features-section {
            padding: 60px 0;
        }

        .cta-section {
            padding: 60px 0;
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            font-size: 1.8rem;
            margin-bottom: 1rem;
        }

        .section-title {
            font-size: 1.8rem;
        }

        .wedding-rings {
            font-size: 1.5rem;
            top: 15px;
            right: 15px;
        }
    }

    @media (max-width: 576px) {
        .game-card {
            padding: 1rem;
            border-radius: 20px;
        }

        .game-thumbnail {
            height: 140px;
            border-radius: 15px;
        }

        .game-title {
            font-size: 1.2rem;
        }

        .game-description {
            font-size: 0.85rem;
            line-height: 1.5;
        }

        .btn-demo {
            padding: 8px 20px;
            font-size: 0.85rem;
            border-radius: 20px;
        }

        .features-section {
            padding: 50px 0;
        }

        .cta-section {
            padding: 50px 0;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .section-title {
            font-size: 1.6rem;
        }

        .section-title::after {
            width: 40px;
            height: 3px;
        }

        .wedding-rings {
            font-size: 1.2rem;
            top: 10px;
            right: 10px;
        }

        /* Stack buttons vertically on small screens */
        .hero-section .btn-hero {
            display: block;
            width: 100%;
            max-width: 250px;
            margin: 0.5rem auto;
        }
    }

    @media (max-width: 400px) {
        .game-card {
            padding: 0.8rem;
        }

        .game-thumbnail {
            height: 120px;
        }

        .game-title {
            font-size: 1.1rem;
        }

        .btn-demo {
            padding: 6px 16px;
            font-size: 0.8rem;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            font-size: 1.3rem;
        }

        .section-title {
            font-size: 1.4rem;
        }
    }

    /* Enhanced animations for mobile */
    @media (prefers-reduced-motion: no-preference) {
        .game-card:hover {
            animation: cardPulse 0.6s ease-in-out;
        }

        @keyframes cardPulse {

            0%,
            100% {
                transform: translateY(-15px) scale(1);
            }

            50% {
                transform: translateY(-15px) scale(1.02);
            }
        }
    }

    /* Accessibility improvements */
    @media (prefers-reduced-motion: reduce) {

        .game-card,
        .feature-icon,
        .btn-demo,
        .fade-in-up {
            transition: none;
            animation: none;
        }

        .game-card:hover {
            transform: none;
        }

        .feature-icon:hover {
            transform: none;
        }
    }
</style>
@endsection

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h1 class="hero-title">
                    <i class="fas fa-heart me-3" style="color: rgba(255,255,255,0.8);"></i>
                    Interactive Wedding Games
                    <i class="fas fa-heart ms-3" style="color: rgba(255,255,255,0.8);"></i>
                </h1>
                <p class="hero-subtitle">Create unforgettable moments with engaging trivia games and interactive
                    entertainment for your special day</p>
                <div class="mt-4">
                    <a href="{{ route('demo.trivia') }}" class="btn-hero primary">
                        <i class="fas fa-play me-2"></i>Try Demo
                    </a>
                    <a href="{{ route('auth.page') }}" class="btn-hero">
                        <i class="fas fa-rocket me-2"></i>Get Started
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Games Section -->
<section class="py-5 fade-in-up">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title">Available Games</h2>
                <p style="color: var(--text-light); font-size: 1.1rem;">Choose from our collection of interactive
                    wedding games designed to entertain your guests</p>
            </div>
        </div>

        <div class="row">
            @forelse($games as $game)
            <div class="col-lg-4 col-md-6 mb-4 fade-in-up">
                <div class="game-card floral-border">
                    <div class="wedding-rings">💍</div>
                    @if($game->game_thumbnail)
                    <img src="{{ asset('storage/' . $game->game_thumbnail) }}" alt="{{ $game->display_name }}"
                        class="game-thumbnail">
                    @else
                    <div class="game-thumbnail d-flex align-items-center justify-content-center"
                        style="background: linear-gradient(135deg, var(--secondary-pink) 0%, var(--light-pink) 100%);">
                        <i class="fas fa-gamepad" style="font-size: 3rem; color: var(--primary-pink);"></i>
                    </div>
                    @endif

                    <h3 class="game-title">{{ $game->display_name }}</h3>
                    <p class="game-description">{{ $game->description ?: 'Engage your guests with this interactive game
                        experience.' }}</p>

                    @if($game->minigame_name === 'trivia')
                    <a href="{{ route('demo.trivia') }}" class="btn-demo">
                        <i class="fas fa-play me-2"></i>Try Demo
                    </a>
                    @else
                    <span class="btn-demo disabled">
                        <i class="fas fa-lock me-2"></i>Coming Soon
                    </span>
                    @endif
                </div>
            </div>
            @empty
            <div class="col-12 text-center fade-in-up">
                <div class="game-card floral-border">
                    <div class="wedding-rings">💍</div>
                    <div class="game-thumbnail d-flex align-items-center justify-content-center"
                        style="background: linear-gradient(135deg, var(--secondary-pink) 0%, var(--light-pink) 100%);">
                        <i class="fas fa-question-circle" style="font-size: 3rem; color: var(--primary-pink);"></i>
                    </div>
                    <h3 class="game-title">Wedding Trivia</h3>
                    <p class="game-description">Test your guests' knowledge with customizable trivia questions about the
                        happy couple and wedding traditions.</p>
                    <a href="{{ route('demo.trivia') }}" class="btn-demo">
                        <i class="fas fa-play me-2"></i>Try Demo
                    </a>
                </div>
            </div>
            @endforelse
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section fade-in-up">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title">Why Choose Our Platform?</h2>
                <p style="color: var(--text-light); font-size: 1.1rem;">Everything you need to create engaging
                    entertainment for your wedding guests</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4 fade-in-up">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Mobile Friendly</h4>
                    <p style="color: var(--text-light);">Guests can play directly from their smartphones - no app
                        downloads required.</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4 fade-in-up">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Easy Setup</h4>
                    <p style="color: var(--text-light);">Create and customize your games in minutes with our intuitive
                        dashboard.</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4 fade-in-up">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Real-time Leaderboards
                    </h4>
                    <p style="color: var(--text-light);">Keep guests engaged with live scoring and competitive
                        leaderboards.</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4 fade-in-up">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Customizable</h4>
                    <p style="color: var(--text-light);">Personalize questions and themes to match your wedding style.
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4 fade-in-up">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Guest Management</h4>
                    <p style="color: var(--text-light);">Easily manage guest access with unique links for each attendee.
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4 fade-in-up">
                <div class="text-center">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 style="color: var(--text-dark); font-weight: 600; margin-bottom: 1rem;">Analytics</h4>
                    <p style="color: var(--text-light);">Track engagement and see how your guests are enjoying the
                        games.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section fade-in-up">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" style="position: relative; z-index: 1;">
                <h2 class="section-title">Ready to Get Started?</h2>
                <p style="color: var(--text-light); font-size: 1.1rem; margin-bottom: 2rem;">Join couples who have made
                    their weddings unforgettable with interactive entertainment</p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="{{ route('demo.trivia') }}" class="btn-hero primary">
                        <i class="fas fa-play me-2"></i>Try Demo
                    </a>
                    <a href="{{ route('auth.page') }}" class="btn-hero">
                        <i class="fas fa-sign-in-alt me-2"></i>Get Started
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection