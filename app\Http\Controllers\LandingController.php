<?php

namespace App\Http\Controllers;

use App\Models\Game;
use App\Models\TriviaQuestion;
use Illuminate\Http\Request;

class LandingController extends Controller
{
    /**
     * Show the landing page
     */
    public function index()
    {
        // Get all available games with thumbnails
        $games = Game::where('is_active', true)
            ->orderBy('display_name')
            ->get();

        return view('landing.index', compact('games'));
    }

    /**
     * Show trivia demo
     */
    public function triviaDemo()
    {
        // Get demo trivia questions
        $demoQuestions = $this->getDemoQuestions();

        return view('landing.trivia-demo', compact('demoQuestions'));
    }

    /**
     * Submit trivia demo answers
     */
    public function submitTriviaDemo(Request $request)
    {
        $request->validate([
            'player_name' => 'required|string|max:255',
            'answers' => 'required|array',
            'answers.*' => 'required|integer|min:0|max:3',
        ]);

        $demoQuestions = $this->getDemoQuestions();
        $score = 0;
        $totalQuestions = count($demoQuestions);
        $results = [];

        foreach ($demoQuestions as $index => $question) {
            $userAnswer = $request->answers[$index] ?? null;
            $isCorrect = $userAnswer === $question['correct_answer'];
            
            if ($isCorrect) {
                $score++;
            }

            $results[] = [
                'question' => $question['question'],
                'options' => $question['options'],
                'user_answer' => $userAnswer,
                'correct_answer' => $question['correct_answer'],
                'is_correct' => $isCorrect,
            ];
        }

        $percentage = round(($score / $totalQuestions) * 100);

        return view('landing.trivia-results', compact(
            'results', 
            'score', 
            'totalQuestions', 
            'percentage',
            'request'
        ));
    }

    /**
     * Get demo trivia questions
     */
    private function getDemoQuestions()
    {
        return [
            [
                'question' => 'What is the most popular wedding month?',
                'options' => ['May', 'June', 'September', 'October'],
                'correct_answer' => 1,
            ],
            [
                'question' => 'Which hand is the wedding ring traditionally worn on?',
                'options' => ['Right hand', 'Left hand', 'Either hand', 'Both hands'],
                'correct_answer' => 1,
            ],
            [
                'question' => 'What does "something blue" represent in wedding tradition?',
                'options' => ['Love', 'Purity', 'Fidelity', 'Prosperity'],
                'correct_answer' => 2,
            ],
            [
                'question' => 'What is the traditional first anniversary gift?',
                'options' => ['Paper', 'Cotton', 'Wood', 'Iron'],
                'correct_answer' => 0,
            ],
            [
                'question' => 'In which country did the tradition of wedding cakes originate?',
                'options' => ['France', 'Italy', 'England', 'Germany'],
                'correct_answer' => 2,
            ],
            [
                'question' => 'What does the term "honeymoon" originally refer to?',
                'options' => ['Sweet beginning', 'First month of marriage', 'Wedding night', 'Romantic getaway'],
                'correct_answer' => 1,
            ],
            [
                'question' => 'Which flower is most commonly used in wedding bouquets?',
                'options' => ['Tulips', 'Roses', 'Lilies', 'Daisies'],
                'correct_answer' => 1,
            ],
            [
                'question' => 'What is the traditional wedding anniversary for 25 years?',
                'options' => ['Gold', 'Silver', 'Pearl', 'Diamond'],
                'correct_answer' => 1,
            ],
        ];
    }
}
