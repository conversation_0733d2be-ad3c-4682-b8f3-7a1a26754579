<?php

namespace App\Http\Controllers\Guest;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\Guest;
use App\Models\GuestScore;
use App\Models\TriviaQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class MinigameController extends Controller
{
    /**
     * Show the minigame selection page for a specific client
     */
    public function index($clientId, $uniqueUrl)
    {
        $clientResult = $this->validateClientAccess($clientId, $uniqueUrl);

        // Check if validation returned an error response
        if ($clientResult instanceof \Illuminate\Http\Response) {
            return $clientResult;
        }

        $client = $clientResult;

        if (!$client->is_have_minigame) {
            // Create dummy objects for error display
            $dummyClient = (object) [
                'id' => $client->id ?? 0,
                'client_name' => $client->client_name ?? 'Unknown Client',
                'unique_url' => $uniqueUrl
            ];
            return $this->showGameError($dummyClient, null, 'Minigames are not enabled for this client.');
        }

        $activeGames = $client->gameConfigs()
            ->where('is_starting', true)
            ->with('game')
            ->get();

        return view('guest.minigame.index', compact('client', 'activeGames'));
    }

    /**
     * Show the trivia game interface with new URL structure
     */
    public function trivia($clientUniqueUrl, $guestUniqueUrl)
    {
        // Validate client and guest access
        $validationResult = $this->validateClientGuestAccess($clientUniqueUrl, $guestUniqueUrl);
        $client = $validationResult['client'];
        $guest = $validationResult['guest'];

        $triviaConfig = $client->gameConfigs()
            ->whereHas('game', function($query) {
                $query->where('minigame_name', 'trivia');
            })
            ->where('is_starting', true)
            ->first();

        if (!$triviaConfig) {
            // Return a user-friendly "game not started" page instead of 404
            return $this->showGameNotStarted($client, $guest, 'trivia');
        }

        // Check if guest has already played
        $existingScore = GuestScore::where('guest_id', $guest->id)
            ->where('game_config_id', $triviaConfig->id)
            ->where('is_completed', true)
            ->first();

        if ($existingScore) {
            return redirect()->route('guest.minigame.leaderboard', [$clientUniqueUrl, $guestUniqueUrl])
                ->with('message', 'You have already played this game!');
        }

        // Get trivia questions with randomization if enabled
        $questionsQuery = TriviaQuestion::where('game_config_id', $triviaConfig->id)
            ->where('is_active', true);

        // Check if randomization is enabled in config
        $randomizeQuestions = $triviaConfig->config_data['randomize_questions'] ?? false;

        if ($randomizeQuestions) {
            $questions = $questionsQuery->inRandomOrder()->get();
        } else {
            $questions = $questionsQuery->orderBy('order')->get();
        }

        if ($questions->isEmpty()) {
            return $this->showGameError($client, $guest, 'No trivia questions available for this game. Please contact the event organizer.');
        }

        // Additional security: Ensure guest belongs to the client
        if ($guest->client_id !== $client->id) {
            return $this->showGameError($client, $guest, 'Access denied: Guest not associated with this client.');
        }

        return view('minigame.trivia.index', compact('client', 'guest', 'triviaConfig', 'questions'));
    }

    /**
     * Show trivia demo mode
     */
    public function triviaDemo()
    {
        $demoMode = true;
        $demoQuestions = $this->getDemoQuestions();
        $demoLeaderboard = $this->getDemoLeaderboard();

        // Create dummy objects for compatibility with the view
        $client = (object) [
            'id' => 0,
            'client_name' => 'Demo Client',
            'unique_url' => 'demo'
        ];

        $guest = null; // Demo mode doesn't need guest

        $triviaConfig = (object) [
            'id' => 0,
            'game_title' => 'Wedding Trivia Demo',
            'top_players' => 10
        ];

        $gameConfig = $triviaConfig; // Alias for backward compatibility
        $questions = collect($demoQuestions); // Convert to collection for compatibility

        return view('minigame.trivia.index', compact(
            'demoMode',
            'demoQuestions',
            'demoLeaderboard',
            'gameConfig',
            'client',
            'guest',
            'triviaConfig',
            'questions'
        ));
    }

    /**
     * Submit trivia demo (no database save)
     */
    public function submitTriviaDemo(Request $request)
    {
        $request->validate([
            'player_name' => 'required|string|max:255',
            'answers' => 'required|array',
            'answers.*' => 'required|integer|min:0|max:3',
        ]);

        $demoQuestions = $this->getDemoQuestions();
        $score = 0;
        $totalQuestions = count($demoQuestions);
        $results = [];

        foreach ($demoQuestions as $index => $question) {
            $userAnswer = $request->answers[$index] ?? null;
            $isCorrect = $userAnswer === $question['correct_answer'];

            if ($isCorrect) {
                $score += 100; // 100 points per correct answer
            }

            $results[] = [
                'question' => $question['question'],
                'options' => $question['options'],
                'user_answer' => $userAnswer,
                'correct_answer' => $question['correct_answer'],
                'is_correct' => $isCorrect,
            ];
        }

        $percentage = round(($score / ($totalQuestions * 100)) * 100);

        // Return JSON response for AJAX handling
        return response()->json([
            'success' => true,
            'score' => $score,
            'totalQuestions' => $totalQuestions,
            'percentage' => $percentage,
            'results' => $results,
            'playerName' => $request->player_name,
            'demoLeaderboard' => $this->getDemoLeaderboard()
        ]);
    }

    /**
     * Legacy trivia method for backward compatibility
     */
    public function triviaLegacy($clientId, $uniqueUrl)
    {
        $clientResult = $this->validateClientAccess($clientId, $uniqueUrl);

        // Check if validation returned an error response
        if ($clientResult instanceof \Illuminate\Http\Response) {
            return $clientResult;
        }

        $client = $clientResult;

        $triviaConfig = $client->gameConfigs()
            ->whereHas('game', function($query) {
                $query->where('minigame_name', 'trivia');
            })
            ->where('is_starting', true)
            ->first();

        if (!$triviaConfig) {
            return $this->showGameError($client, $guest, 'Trivia game is not available for this client.');
        }

        // Check if guest has already played
        $guestId = Session::get('guest_id');
        if ($guestId) {
            $existingScore = GuestScore::where('guest_id', $guestId)
                ->where('game_config_id', $triviaConfig->id)
                ->first();

            if ($existingScore) {
                return redirect()->route('guest.legacy.minigame.leaderboard', [$clientId, $uniqueUrl])
                    ->with('message', 'You have already played this game!');
            }
        }

        // Get trivia questions
        $questions = TriviaQuestion::where('game_config_id', $triviaConfig->id)
            ->inRandomOrder()
            ->limit(10)
            ->get();

        if ($questions->isEmpty()) {
            return $this->showGameError($client, $guest, 'No trivia questions available for this game.');
        }

        return view('guest.minigame.trivia', compact('client', 'triviaConfig', 'questions'));
    }

    /**
     * Submit trivia answers and calculate score with new URL structure
     */
    public function submitTrivia(Request $request, $clientUniqueUrl, $guestUniqueUrl)
    {
        // Validate client and guest access
        $validationResult = $this->validateClientGuestAccess($clientUniqueUrl, $guestUniqueUrl);
        $client = $validationResult['client'];
        $guest = $validationResult['guest'];

        $triviaConfig = $client->gameConfigs()
            ->whereHas('game', function($query) {
                $query->where('minigame_name', 'trivia');
            })
            ->where('is_starting', true)
            ->first();

        if (!$triviaConfig) {
            return $this->showGameError($client, $guest, 'Trivia game is not available.');
        }

        $request->validate([
            'answers' => 'required|array',
            'answers.*' => 'required|string',
        ]);

        // Check if guest has already played
        $existingScore = GuestScore::where('guest_id', $guest->id)
            ->where('game_config_id', $triviaConfig->id)
            ->where('is_completed', true)
            ->first();

        if ($existingScore) {
            return redirect()->route('guest.minigame.leaderboard', [$clientUniqueUrl, $guestUniqueUrl])
                ->with('error', 'You have already played this game!');
        }

        // Calculate score
        $questions = TriviaQuestion::where('game_config_id', $triviaConfig->id)
            ->where('is_active', true)
            ->orderBy('order')
            ->get();
        $score = 0;
        $totalQuestions = $questions->count();
        $gameData = [];

        foreach ($questions as $question) {
            $userAnswerIndex = $request->answers[$question->id] ?? null;
            $isCorrect = false;

            if ($userAnswerIndex !== null && isset($question->options[$userAnswerIndex])) {
                if ((int)$userAnswerIndex === (int)$question->correct_answer) {
                    $score += $question->points;
                    $isCorrect = true;
                }
            }

            $gameData[] = [
                'question_id' => $question->id,
                'user_answer_index' => $userAnswerIndex,
                'correct_answer_index' => $question->correct_answer,
                'is_correct' => $isCorrect,
                'points_earned' => $isCorrect ? $question->points : 0
            ];
        }

        // Save score
        GuestScore::create([
            'client_id' => $client->id,
            'guest_id' => $guest->id,
            'game_config_id' => $triviaConfig->id,
            'score' => $score,
            'is_completed' => true,
            'completed_at' => now(),
            'game_data' => $gameData,
        ]);

        return redirect()->route('guest.minigame.leaderboard', [$clientUniqueUrl, $guestUniqueUrl])
            ->with('success', "Congratulations! You scored {$score} points!");
    }

    /**
     * Legacy submit trivia method for backward compatibility
     */
    public function submitTriviaLegacy(Request $request, $clientId, $uniqueUrl)
    {
        $clientResult = $this->validateClientAccess($clientId, $uniqueUrl);

        // Check if validation returned an error response
        if ($clientResult instanceof \Illuminate\Http\Response) {
            return $clientResult;
        }

        $client = $clientResult;

        $triviaConfig = $client->gameConfigs()
            ->whereHas('game', function($query) {
                $query->where('minigame_name', 'trivia');
            })
            ->where('is_starting', true)
            ->first();

        if (!$triviaConfig) {
            return $this->showGameError($client, null, 'Trivia game is not available.');
        }

        $request->validate([
            'guest_name' => 'required|string|max:255',
            'answers' => 'required|array',
            'answers.*' => 'required|string',
        ]);

        // Create or get guest
        $guest = Guest::firstOrCreate([
            'name' => $request->guest_name,
            'client_id' => $client->id,
        ]);

        // Check if guest has already played
        $existingScore = GuestScore::where('guest_id', $guest->id)
            ->where('game_config_id', $triviaConfig->id)
            ->first();

        if ($existingScore) {
            return redirect()->route('guest.legacy.minigame.leaderboard', [$clientId, $uniqueUrl])
                ->with('error', 'You have already played this game!');
        }

        // Calculate score
        $questions = TriviaQuestion::where('game_config_id', $triviaConfig->id)->get();
        $score = 0;
        $totalQuestions = $questions->count();

        foreach ($questions as $question) {
            $userAnswer = $request->answers[$question->id] ?? '';
            if (strtolower(trim($userAnswer)) === strtolower(trim($question->correct_answer))) {
                $score++;
            }
        }

        $finalScore = ($score / $totalQuestions) * 100;

        // Save score
        GuestScore::create([
            'guest_id' => $guest->id,
            'game_config_id' => $triviaConfig->id,
            'score' => $finalScore,
        ]);

        // Store guest ID in session
        Session::put('guest_id', $guest->id);

        return redirect()->route('guest.legacy.minigame.leaderboard', [$clientId, $uniqueUrl])
            ->with('success', "Congratulations! You scored {$score} out of {$totalQuestions} questions correct!");
    }

    /**
     * Show the leaderboard with new URL structure
     */
    public function leaderboard($clientUniqueUrl, $guestUniqueUrl)
    {
        // Validate client and guest access
        $validationResult = $this->validateClientGuestAccess($clientUniqueUrl, $guestUniqueUrl);
        $client = $validationResult['client'];
        $guest = $validationResult['guest'];

        $triviaConfig = $client->gameConfigs()
            ->whereHas('game', function($query) {
                $query->where('minigame_name', 'trivia');
            })
            ->where('is_starting', true)
            ->first();

        if (!$triviaConfig) {
            return $this->showGameError($client, $guest, 'Trivia game is not available.');
        }

        $leaderboard = GuestScore::where('game_config_id', $triviaConfig->id)
            ->where('is_completed', true)
            ->with('guest')
            ->orderBy('score', 'desc')
            ->orderBy('completed_at', 'asc')
            ->limit($triviaConfig->top_players)
            ->get();

        $userScore = GuestScore::where('guest_id', $guest->id)
            ->where('game_config_id', $triviaConfig->id)
            ->where('is_completed', true)
            ->first();

        return view('guest.minigame.leaderboard', compact('client', 'guest', 'triviaConfig', 'leaderboard', 'userScore'));
    }

    /**
     * Legacy leaderboard method for backward compatibility
     */
    public function leaderboardLegacy($clientId, $uniqueUrl)
    {
        $clientResult = $this->validateClientAccess($clientId, $uniqueUrl);

        // Check if validation returned an error response
        if ($clientResult instanceof \Illuminate\Http\Response) {
            return $clientResult;
        }

        $client = $clientResult;

        $triviaConfig = $client->gameConfigs()
            ->whereHas('game', function($query) {
                $query->where('minigame_name', 'trivia');
            })
            ->where('is_starting', true)
            ->first();

        if (!$triviaConfig) {
            return $this->showGameError($client, null, 'Trivia game is not available.');
        }

        $leaderboard = GuestScore::where('game_config_id', $triviaConfig->id)
            ->with('guest')
            ->orderBy('score', 'desc')
            ->orderBy('created_at', 'asc')
            ->limit($triviaConfig->top_players)
            ->get();

        $guestId = Session::get('guest_id');
        $userScore = null;
        if ($guestId) {
            $userScore = GuestScore::where('guest_id', $guestId)
                ->where('game_config_id', $triviaConfig->id)
                ->first();
        }

        return view('guest.minigame.leaderboard', compact('client', 'triviaConfig', 'leaderboard', 'userScore'));
    }

    /**
     * Validate client and guest access using unique URLs
     */
    private function validateClientGuestAccess($clientUniqueUrl, $guestUniqueUrl)
    {
        // Decode URLs to handle special characters
        $decodedClientUrl = urldecode($clientUniqueUrl);
        $decodedGuestUrl = urldecode($guestUniqueUrl);

        // Find client by unique URL
        $client = Client::where(function($query) use ($clientUniqueUrl, $decodedClientUrl) {
                $query->where('unique_url', $clientUniqueUrl)
                      ->orWhere('unique_url', $decodedClientUrl);
            })
            ->first();

        if (!$client) {
            \Log::error('Client validation failed', [
                'client_unique_url' => $clientUniqueUrl,
                'decoded_client_url' => $decodedClientUrl,
                'available_clients' => Client::select('id', 'client_name', 'unique_url')->get()->toArray()
            ]);

            // Create a dummy client object for error display
            $dummyClient = (object) [
                'id' => 0,
                'client_name' => 'Unknown Client',
                'unique_url' => $clientUniqueUrl
            ];

            return $this->showGameError($dummyClient, null, 'Client not found. Please check the URL and try again.');
        }

        // Check if client has minigames enabled
        if (!$client->is_have_minigame) {
            return $this->showGameError($client, null, 'Minigames are not enabled for this client.');
        }

        // Find guest by unique URL and ensure they belong to this client
        $guest = Guest::where('client_id', $client->id)
            ->where(function($query) use ($guestUniqueUrl, $decodedGuestUrl) {
                $query->where('unique_url', $guestUniqueUrl)
                      ->orWhere('unique_url', $decodedGuestUrl);
            })
            ->first();

        if (!$guest) {
            \Log::error('Guest validation failed', [
                'client_id' => $client->id,
                'guest_unique_url' => $guestUniqueUrl,
                'decoded_guest_url' => $decodedGuestUrl,
                'available_guests' => Guest::where('client_id', $client->id)
                    ->select('id', 'name', 'unique_url')->get()->toArray()
            ]);

            return $this->showGameError($client, null, 'Guest not found or not associated with this client. Please check the URL and try again.');
        }

        return [
            'client' => $client,
            'guest' => $guest
        ];
    }

    /**
     * Validate client access (legacy method)
     */
    private function validateClientAccess($clientId, $uniqueUrl)
    {
        // Decode URL to handle special characters
        $decodedUrl = urldecode($uniqueUrl);

        $client = Client::where('id', $clientId)
            ->where(function($query) use ($uniqueUrl, $decodedUrl) {
                $query->where('unique_url', $uniqueUrl)
                      ->orWhere('unique_url', $decodedUrl);
            })
            ->first();

        if (!$client) {
            // Try to find client by ID only for debugging
            $clientById = Client::find($clientId);

            \Log::error('Client validation failed', [
                'client_id' => $clientId,
                'unique_url' => $uniqueUrl,
                'decoded_url' => $decodedUrl,
                'client_by_id' => $clientById ? $clientById->unique_url : 'not found',
                'available_clients' => Client::select('id', 'client_name', 'unique_url')->get()->toArray()
            ]);

            // Show helpful error message
            $dummyClient = (object) [
                'id' => $clientId,
                'client_name' => $clientById ? $clientById->client_name : 'Unknown Client',
                'unique_url' => $uniqueUrl
            ];

            $message = $clientById
                ? "Client found but URL mismatch. Expected: '{$clientById->unique_url}', Got: '{$uniqueUrl}'"
                : 'Client not found. Please check the URL and try again.';

            return $this->showGameError($dummyClient, null, $message);
        }

        // Check if client has minigames enabled
        if (!$client->is_have_minigame) {
            return $this->showGameError($client, null, 'Minigames are not enabled for this client.');
        }

        return $client;
    }

    /**
     * Show game not started page
     */
    private function showGameNotStarted($client, $guest, $gameType = 'trivia')
    {
        return response()->view('guest.minigame.game-not-started', compact('client', 'guest', 'gameType'), 200);
    }

    /**
     * Show game error page
     */
    private function showGameError($client, $guest, $message, $gameType = 'trivia')
    {
        return response()->view('guest.minigame.game-error', compact('client', 'guest', 'message', 'gameType'), 200);
    }

    /**
     * Get demo trivia questions
     */
    private function getDemoQuestions()
    {
        return [
            [
                'id' => 1,
                'question' => 'What is the most popular wedding month?',
                'options' => ['May', 'June', 'September', 'October'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 30
            ],
            [
                'id' => 2,
                'question' => 'Which hand is the wedding ring traditionally worn on?',
                'options' => ['Right hand', 'Left hand', 'Either hand', 'Both hands'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 30
            ],
            [
                'id' => 3,
                'question' => 'What does "something blue" represent in wedding tradition?',
                'options' => ['Love', 'Purity', 'Fidelity', 'Prosperity'],
                'correct_answer' => 2,
                'points' => 100,
                'time_limit' => 30
            ],
            [
                'id' => 4,
                'question' => 'What is the traditional first anniversary gift?',
                'options' => ['Paper', 'Cotton', 'Wood', 'Iron'],
                'correct_answer' => 0,
                'points' => 100,
                'time_limit' => 30
            ],
            [
                'id' => 5,
                'question' => 'In which country did the tradition of wedding cakes originate?',
                'options' => ['France', 'Italy', 'England', 'Germany'],
                'correct_answer' => 2,
                'points' => 100,
                'time_limit' => 30
            ],
            [
                'id' => 6,
                'question' => 'What does the term "honeymoon" originally refer to?',
                'options' => ['Sweet beginning', 'First month of marriage', 'Wedding night', 'Romantic getaway'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 30
            ],
            [
                'id' => 7,
                'question' => 'Which flower is most commonly used in wedding bouquets?',
                'options' => ['Tulips', 'Roses', 'Lilies', 'Daisies'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 30
            ],
            [
                'id' => 8,
                'question' => 'What is the traditional wedding anniversary for 25 years?',
                'options' => ['Gold', 'Silver', 'Pearl', 'Diamond'],
                'correct_answer' => 1,
                'points' => 100,
                'time_limit' => 30
            ],
        ];
    }

    /**
     * Get demo leaderboard
     */
    private function getDemoLeaderboard()
    {
        return [
            ['name' => 'Emma & James', 'score' => 800, 'rank' => 1],
            ['name' => 'Sarah M.', 'score' => 750, 'rank' => 2],
            ['name' => 'Michael R.', 'score' => 700, 'rank' => 3],
            ['name' => 'Lisa & Tom', 'score' => 650, 'rank' => 4],
            ['name' => 'Jennifer K.', 'score' => 600, 'rank' => 5],
            ['name' => 'David L.', 'score' => 550, 'rank' => 6],
            ['name' => 'Amanda S.', 'score' => 500, 'rank' => 7],
            ['name' => 'Chris & Amy', 'score' => 450, 'rank' => 8],
            ['name' => 'Rachel B.', 'score' => 400, 'rank' => 9],
            ['name' => 'Mark T.', 'score' => 350, 'rank' => 10],
        ];
    }
}
