<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GuestScore extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'guest_id',
        'game_config_id',
        'score',
        'is_completed',
        'completed_at',
        'game_data',
    ];

    protected $casts = [
        'is_completed' => 'boolean',
        'completed_at' => 'datetime',
        'game_data' => 'array',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function guest()
    {
        return $this->belongsTo(Guest::class);
    }

    public function gameConfig()
    {
        return $this->belongsTo(GameConfig::class);
    }

    /**
     * Get the dynamic rank for this score
     */
    public function getRank()
    {
        return GuestScore::where('game_config_id', $this->game_config_id)
                         ->where('is_completed', true)
                         ->where(function($query) {
                             $query->where('score', '>', $this->score)
                                   ->orWhere(function($subQuery) {
                                       $subQuery->where('score', '=', $this->score)
                                               ->where('completed_at', '<', $this->completed_at);
                                   });
                         })
                         ->count() + 1;
    }

    /**
     * Get leaderboard for a specific game configuration
     */
    public static function getLeaderboard($gameConfigId, $limit = 10)
    {
        return static::where('game_config_id', $gameConfigId)
                     ->where('is_completed', true)
                     ->with('guest')
                     ->orderBy('score', 'desc')
                     ->orderBy('completed_at', 'asc') // Earlier submission wins ties
                     ->limit($limit)
                     ->get()
                     ->map(function($score, $index) {
                         $score->dynamic_rank = $index + 1;
                         return $score;
                     });
    }

    /**
     * Get rank attribute dynamically
     */
    public function getRankAttribute()
    {
        return $this->getRank();
    }
}
