<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GuestScore extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'guest_id',
        'game_config_id',
        'score',
        'rank',
        'is_completed',
        'completed_at',
        'game_data',
    ];

    protected $casts = [
        'is_completed' => 'boolean',
        'completed_at' => 'datetime',
        'game_data' => 'array',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function guest()
    {
        return $this->belongsTo(Guest::class);
    }

    public function gameConfig()
    {
        return $this->belongsTo(GameConfig::class);
    }

    public function updateRank()
    {
        $rank = GuestScore::where('game_config_id', $this->game_config_id)
                          ->where('is_completed', true)
                          ->where('score', '>', $this->score)
                          ->count() + 1;

        $this->update(['rank' => $rank]);
        return $rank;
    }
}
