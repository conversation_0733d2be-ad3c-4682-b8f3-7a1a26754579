<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Guest extends Model
{
    protected $fillable = [
        'client_id',
        'name',
        'unique_url',
        'whatsapp_number',
        'wish',
        'number_of_guests',
        'is_presence',
        'is_guest',
        'present_status',
        'souvenir_status'
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function guestMeals()
    {
        return $this->hasMany(GuestMeal::class);
    }

    public function guestScores()
    {
        return $this->hasMany(GuestScore::class);
    }
}
