(function(){const darkMode=localStorage.getItem('darkMode');const themeClass=darkMode==='enabled'?'dark':'light';document.documentElement.className=themeClass;document.addEventListener('DOMContentLoaded',()=>{const darkModeToggle=document.getElementById('dark-mode-toggle');const lightModeToggle=document.getElementById('light-mode-toggle');const toggleMode=(isDarkMode)=>{document.documentElement.classList.toggle('dark',isDarkMode);localStorage.setItem('darkMode',isDarkMode?'enabled':'disabled');updateToggleButtons(isDarkMode);};const updateToggleButtons=(isDarkMode)=>{if(isDarkMode){darkModeToggle.classList.remove('activate');lightModeToggle.classList.add('activate');}else{lightModeToggle.classList.remove('activate');darkModeToggle.classList.add('activate');}};updateToggleButtons(themeClass==='dark');if(darkModeToggle&&lightModeToggle){darkModeToggle.addEventListener('click',()=>toggleMode(true));lightModeToggle.addEventListener('click',()=>toggleMode(false));}});})();