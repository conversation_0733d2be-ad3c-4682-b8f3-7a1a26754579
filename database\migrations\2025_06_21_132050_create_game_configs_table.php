<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('game_configs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('game_id')->constrained()->onDelete('cascade');
            $table->string('game_title');
            $table->integer('top_players')->default(10);
            $table->boolean('is_starting')->default(false);
            $table->boolean('is_done')->default(false);
            $table->json('config_data')->nullable(); // For flexible game-specific configurations
            $table->timestamps();

            $table->unique(['client_id', 'game_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_configs');
    }
};
