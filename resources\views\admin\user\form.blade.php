<div class="form-group{{ $errors->has('name') ? 'has-error' : '' }}">
    {!! Form::label('name', 'Name', ['class' => 'control-label']) !!}
    {!! Form::text(
        'name',
        null,
        '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('name', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('email') ? 'has-error' : '' }}">
    {!! Form::label('email', 'Email', ['class' => 'control-label']) !!}
    {!! Form::email(
        'email',
        null,
        '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('email', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('password') ? 'has-error' : '' }}">
    {!! Form::label('password', 'Password', ['class' => 'control-label']) !!}
    {!! Form::password(
        'password',
        '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('password', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('role') ? 'has-error' : '' }}">
    {!! Form::label('role', 'Role', ['class' => 'control-label']) !!}
    {!! Form::select(
        'role',
        json_decode('{"Admin":"Admin","Employee":"Employee","Supervisor":"Supervisor","Manager":"Manager"}', true),
        null,
        '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('role', '<p class="help-block">:message</p>') !!}
</div>

<br>
<div class="form-group" align="right">
    {!! Form::submit($formMode === 'edit' ? 'Update' : 'Create', ['class' => 'btn btn-primary']) !!}
    {!! Form::reset('Reset', ['class' => 'btn btn-warning']) !!}
    <a href="#" onClick="javascript:history.go(-1)" class="btn btn-danger">Cancel and Back</a>
</div>
