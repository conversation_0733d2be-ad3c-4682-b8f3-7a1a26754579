@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-edit me-2"></i>Edit Trivia Question
                </h5>
                <small class="text-muted">Update the trivia question details</small>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.trivia-questions.update', $triviaQuestion) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <!-- Game Configuration Selection -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="game_config_id" class="form-label">Game Configuration <span class="text-danger">*</span></label>
                            <select class="form-control @error('game_config_id') is-invalid @enderror" 
                                    id="game_config_id" name="game_config_id" required>
                                <option value="">Select Game Configuration</option>
                                @foreach($gameConfigs as $config)
                                    <option value="{{ $config->id }}" 
                                        {{ (old('game_config_id', $triviaQuestion->game_config_id) == $config->id) ? 'selected' : '' }}>
                                        {{ $config->client->client_name }} - {{ $config->game_title }}
                                    </option>
                                @endforeach
                            </select>
                            @error('game_config_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="question_image" class="form-label">Question Image (Optional)</label>
                            @if($triviaQuestion->question_image)
                                <div class="mb-2">
                                    <img src="{{ Storage::url($triviaQuestion->question_image) }}" 
                                         alt="Current question image" class="img-thumbnail" style="max-height: 100px;">
                                    <small class="d-block text-muted">Current image</small>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('question_image') is-invalid @enderror" 
                                   id="question_image" name="question_image" accept="image/*">
                            <small class="form-text text-muted">Upload a new image to replace the current one</small>
                            @error('question_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Question Text -->
                    <div class="mb-3">
                        <label for="question_text" class="form-label">Question Text <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('question_text') is-invalid @enderror" 
                                  id="question_text" name="question_text" rows="3" 
                                  placeholder="Enter your trivia question here..." required>{{ old('question_text', $triviaQuestion->question_text) }}</textarea>
                        @error('question_text')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Multiple Choice Options -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0 heading-font">
                                <i class="fas fa-list me-2"></i>Multiple Choice Options
                            </h6>
                            <small class="text-muted">Leave options blank for open-ended questions</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="option_a" class="form-label">Option A</label>
                                    <input type="text" class="form-control @error('option_a') is-invalid @enderror" 
                                           id="option_a" name="option_a" 
                                           value="{{ old('option_a', $triviaQuestion->option_a) }}" 
                                           placeholder="First option">
                                    @error('option_a')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="option_b" class="form-label">Option B</label>
                                    <input type="text" class="form-control @error('option_b') is-invalid @enderror" 
                                           id="option_b" name="option_b" 
                                           value="{{ old('option_b', $triviaQuestion->option_b) }}" 
                                           placeholder="Second option">
                                    @error('option_b')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="option_c" class="form-label">Option C</label>
                                    <input type="text" class="form-control @error('option_c') is-invalid @enderror" 
                                           id="option_c" name="option_c" 
                                           value="{{ old('option_c', $triviaQuestion->option_c) }}" 
                                           placeholder="Third option">
                                    @error('option_c')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="option_d" class="form-label">Option D</label>
                                    <input type="text" class="form-control @error('option_d') is-invalid @enderror" 
                                           id="option_d" name="option_d" 
                                           value="{{ old('option_d', $triviaQuestion->option_d) }}" 
                                           placeholder="Fourth option">
                                    @error('option_d')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Correct Answer -->
                    <div class="mb-3">
                        <label for="correct_answer" class="form-label">Correct Answer <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('correct_answer') is-invalid @enderror" 
                               id="correct_answer" name="correct_answer" 
                               value="{{ old('correct_answer', $triviaQuestion->correct_answer) }}" 
                               placeholder="Enter the correct answer" required>
                        <small class="form-text text-muted">
                            For multiple choice: enter the exact text of the correct option<br>
                            For open-ended: enter the expected answer (case-insensitive matching)
                        </small>
                        @error('correct_answer')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.trivia-questions.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Questions
                        </a>
                        <div>
                            <a href="{{ route('admin.trivia-questions.show', $triviaQuestion) }}" class="btn btn-info me-2">
                                <i class="fas fa-eye me-1"></i>View Question
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Question
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
/* Form specific styles */
.form-check-input:checked {
    background-color: var(--primary-pink) !important;
    border-color: var(--primary-pink) !important;
}

.form-check-input:focus {
    border-color: var(--primary-pink) !important;
    box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25) !important;
}

.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    display: block;
}

/* File input styling */
input[type="file"] {
    padding: 8px 12px;
}

/* Current image styling */
.img-thumbnail {
    border: 2px solid rgba(251, 207, 232, 0.5);
    border-radius: 8px;
}

/* Option inputs styling */
.card .form-control {
    transition: all 0.3s ease;
}

.card .form-control:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(236, 72, 153, 0.15);
}
</style>

<script>
// Auto-fill correct answer when option is selected
document.addEventListener('DOMContentLoaded', function() {
    const options = ['option_a', 'option_b', 'option_c', 'option_d'];
    const correctAnswerField = document.getElementById('correct_answer');
    
    options.forEach(optionId => {
        const optionField = document.getElementById(optionId);
        if (optionField) {
            optionField.addEventListener('blur', function() {
                if (this.value && !correctAnswerField.value) {
                    // Suggest this as correct answer if none is set
                    correctAnswerField.placeholder = `Suggestion: ${this.value}`;
                }
            });
        }
    });
});
</script>
@endsection
