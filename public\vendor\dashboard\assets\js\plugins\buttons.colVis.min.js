/*!
 * Column visibility buttons for Buttons and DataTables.
 * © SpryMedia Ltd - datatables.net/license
 */
!function(e){var o,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net","datatables.net-buttons"],function(n){return e(n,window,document)}):"object"==typeof exports?(o=require("jquery"),i=function(n,t){t.fn.dataTable||require("datatables.net")(n,t),t.fn.dataTable.Buttons||require("datatables.net-buttons")(n,t)},"undefined"==typeof window?module.exports=function(n,t){return n=n||window,t=t||o(n),i(n,t),e(t,0,n.document)}:(i(window,o),module.exports=e(o,window,window.document))):e(jQuery,window,document)}(function(n,t,e){"use strict";var o=n.fn.dataTable;return n.extend(o.ext.buttons,{colvis:function(n,t){var e=null,o={extend:"collection",init:function(n,t){e=t},text:function(n){return n.i18n("buttons.colvis","Column visibility")},className:"buttons-colvis",closeButton:!1,buttons:[{extend:"columnsToggle",columns:t.columns,columnText:t.columnText}]};return n.on("column-reorder.dt"+t.namespace,function(){n.button(null,n.button(null,e).node()).collectionRebuild([{extend:"columnsToggle",columns:t.columns,columnText:t.columnText}])}),o},columnsToggle:function(n,t){return n.columns(t.columns).indexes().map(function(n){return{extend:"columnToggle",columns:n,columnText:t.columnText}}).toArray()},columnToggle:function(n,t){return{extend:"columnVisibility",columns:t.columns,columnText:t.columnText}},columnsVisibility:function(n,t){return n.columns(t.columns).indexes().map(function(n){return{extend:"columnVisibility",columns:n,visibility:t.visibility,columnText:t.columnText}}).toArray()},columnVisibility:{columns:void 0,text:function(n,t,e){return e._columnText(n,e)},className:"buttons-columnVisibility",action:function(n,t,e,o){var t=t.columns(o.columns),i=t.visible();t.visible(void 0!==o.visibility?o.visibility:!(i.length&&i[0]))},init:function(e,n,o){var i=this;n.attr("data-cv-idx",o.columns),e.on("column-visibility.dt"+o.namespace,function(n,t){t.bDestroying||t.nTable!=e.settings()[0].nTable||i.active(e.column(o.columns).visible())}).on("column-reorder.dt"+o.namespace,function(){o.destroying||1===e.columns(o.columns).count()&&(i.text(o._columnText(e,o)),i.active(e.column(o.columns).visible()))}),this.active(e.column(o.columns).visible())},destroy:function(n,t,e){n.off("column-visibility.dt"+e.namespace).off("column-reorder.dt"+e.namespace)},_columnText:function(n,t){var e,o;return"string"==typeof t.text?t.text:(e=n.column(t.columns).index(),o=(o=(o=n.settings()[0].aoColumns[e].sTitle)||n.column(e).header().innerHTML).replace(/\n/g," ").replace(/<br\s*\/?>/gi," ").replace(/<select(.*?)<\/select>/g,"").replace(/<!\-\-.*?\-\->/g,"").replace(/<.*?>/g,"").replace(/^\s+|\s+$/g,""),t.columnText?t.columnText(n,e,o):o)}},colvisRestore:{className:"buttons-colvisRestore",text:function(n){return n.i18n("buttons.colvisRestore","Restore visibility")},init:function(n,t,e){n.columns().every(function(){var n=this.init();void 0===n.__visOriginal&&(n.__visOriginal=this.visible())})},action:function(n,t,e,o){t.columns().every(function(n){var t=this.init();this.visible(t.__visOriginal)})}},colvisGroup:{className:"buttons-colvisGroup",action:function(n,t,e,o){t.columns(o.show).visible(!0,!1),t.columns(o.hide).visible(!1,!1),t.columns.adjust()},show:[],hide:[]}}),o});