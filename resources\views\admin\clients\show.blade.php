@extends('admin.layout.master')

@section('content')
<div class="row fade-in-up">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 heading-font">
                    <i class="fas fa-user me-2"></i>Client Details
                </h5>
                <small class="text-muted">View client information and minigame settings</small>
            </div>
            <div class="card-body">
                <!-- Client Information -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-info-circle me-2"></i>Client Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Client Name:</label>
                                        <div class="client-info">
                                            <div class="d-flex align-items-center">
                                                <div class="client-avatar me-2">
                                                    {{ substr($client->client_name, 0, 1) }}
                                                </div>
                                                <h6 class="mb-0">{{ $client->client_name }}</h6>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Unique URL:</label>
                                        <div class="url-display">
                                            <code class="bg-light p-2 rounded">{{ $client->unique_url }}</code>
                                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('{{ $client->unique_url }}')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>

                                    @if($client->email)
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Email:</label>
                                        <div>{{ $client->email }}</div>
                                    </div>
                                    @endif

                                    @if($client->phone)
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Phone:</label>
                                        <div>{{ $client->phone }}</div>
                                    </div>
                                    @endif

                                    @if($client->event_date)
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Event Date:</label>
                                        <div>{{ \Carbon\Carbon::parse($client->event_date)->format('F d, Y') }}</div>
                                    </div>
                                    @endif

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">Minigames Status:</label>
                                        <div>
                                            @if($client->is_have_minigame)
                                                <span class="badge bg-success fs-6 p-2">
                                                    <i class="fas fa-check me-1"></i>Enabled
                                                </span>
                                            @else
                                                <span class="badge bg-secondary fs-6 p-2">
                                                    <i class="fas fa-times me-1"></i>Disabled
                                                </span>
                                            @endif
                                        </div>
                                    </div>

                                    @if($client->notes)
                                    <div class="col-md-12 mb-3">
                                        <label class="form-label fw-bold">Notes:</label>
                                        <div class="notes-display">
                                            {{ $client->notes }}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Game Configurations -->
                        @if($client->is_have_minigame && $client->gameConfigs->count() > 0)
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-gamepad me-2"></i>Game Configurations
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    @foreach($client->gameConfigs as $config)
                                    <div class="col-md-6 mb-3">
                                        <div class="game-config-card">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div>
                                                    <h6 class="game-title">{{ $config->game_title }}</h6>
                                                    <small class="game-type">{{ $config->game->display_name }}</small>
                                                </div>
                                                <div class="game-status">
                                                    @if($config->is_starting)
                                                        <span class="status-badge running">Running</span>
                                                    @elseif($config->is_done)
                                                        <span class="status-badge completed">Completed</span>
                                                    @else
                                                        <span class="status-badge pending">Pending</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="game-stats">
                                                <small class="text-muted">
                                                    {{ $config->guestScores->where('is_completed', true)->count() }} participants • 
                                                    Top {{ $config->top_players }} shown
                                                </small>
                                            </div>
                                            <div class="game-actions mt-2">
                                                <a href="{{ route('admin.game-configs.show', $config) }}" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Guest Access URLs -->
                        @if($client->is_have_minigame)
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-link me-2"></i>Guest Access URLs
                                </h6>
                            </div>
                            <div class="card-body">
                                @if($client->gameConfigs->count() > 0)
                                    @foreach($client->gameConfigs as $config)
                                    <div class="url-item mb-3">
                                        <label class="form-label fw-bold">{{ $config->game_title }}:</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" 
                                                   value="{{ url('/game/' . $client->id . '/' . $client->unique_url . '/trivia') }}" 
                                                   readonly id="url-{{ $config->id }}">
                                            <button class="btn btn-outline-primary" type="button" 
                                                    onclick="copyToClipboard('{{ url('/game/' . $client->id . '/' . $client->unique_url . '/trivia') }}')">
                                                <i class="fas fa-copy"></i> Copy
                                            </button>
                                        </div>
                                    </div>
                                    @endforeach
                                @else
                                    <p class="text-muted mb-0">No game configurations available. Create game configurations to generate guest URLs.</p>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>

                    <div class="col-md-4">
                        <!-- Client Statistics -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-chart-bar me-2"></i>Statistics
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Total Games:</span>
                                        <span class="fw-bold">{{ $client->gameConfigs->count() }}</span>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Active Games:</span>
                                        <span class="fw-bold">{{ $client->gameConfigs->where('is_starting', true)->count() }}</span>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Total Guests:</span>
                                        <span class="fw-bold">{{ $client->guests->count() }}</span>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Total Participants:</span>
                                        <span class="fw-bold">{{ $client->guestScores->where('is_completed', true)->count() }}</span>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Created:</span>
                                        <span class="fw-bold">{{ $client->created_at->format('M d, Y') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0 heading-font">
                                    <i class="fas fa-tools me-2"></i>Quick Actions
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="{{ route('admin.clients.edit', $client) }}" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>Edit Client
                                    </a>
                                    
                                    @if($client->is_have_minigame)
                                        <a href="{{ route('admin.clients.minigame-settings', $client) }}" class="btn btn-info">
                                            <i class="fas fa-gamepad me-1"></i>Minigame Settings
                                        </a>
                                    @endif

                                    <a href="{{ route('admin.game-configs.create') }}?client={{ $client->id }}" class="btn btn-success">
                                        <i class="fas fa-plus me-1"></i>Add Game Config
                                    </a>

                                    <form action="{{ route('admin.clients.destroy', $client) }}" method="POST" 
                                          onsubmit="return confirm('Are you sure? This will delete all associated data!')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger w-100">
                                            <i class="fas fa-trash me-1"></i>Delete Client
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('admin.clients.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Clients
                    </a>
                    <div>
                        <a href="{{ route('admin.clients.edit', $client) }}" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i>Edit Client
                        </a>
                        @if($client->is_have_minigame)
                            <a href="{{ route('admin.clients.minigame-settings', $client) }}" class="btn btn-info">
                                <i class="fas fa-gamepad me-1"></i>Manage Minigames
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Client display styles */
.client-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--secondary-pink) 0%, #f5c6cb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    font-weight: 600;
    font-size: 16px;
}

.url-display code {
    font-size: 0.9rem;
    color: var(--primary-pink);
}

.notes-display {
    background: rgba(251, 207, 232, 0.1);
    border: 2px solid rgba(251, 207, 232, 0.3);
    border-radius: 8px;
    padding: 15px;
    font-style: italic;
}

/* Game config cards */
.game-config-card {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(251, 207, 232, 0.3);
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s ease;
}

.game-config-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(236, 72, 153, 0.1);
}

.game-title {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 2px;
}

.game-type {
    color: var(--text-light);
}

.status-badge {
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-badge.running {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-badge.completed {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.status-badge.pending {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* Statistics styling */
.stat-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(251, 207, 232, 0.2);
}

.stat-item:last-child {
    border-bottom: none;
}

/* URL items */
.url-item {
    padding: 10px 0;
    border-bottom: 1px solid rgba(251, 207, 232, 0.2);
}

.url-item:last-child {
    border-bottom: none;
}
</style>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'alert alert-success position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
        toast.innerHTML = '<i class="fas fa-check me-2"></i>Copied to clipboard!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert('Failed to copy to clipboard');
    });
}
</script>
@endsection
