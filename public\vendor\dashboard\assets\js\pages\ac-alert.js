"use strict";document.querySelector(".bs-message").addEventListener("click",function(){Swal.fire("Any fool can use a computer")}),document.querySelector(".bs-tit-txt").addEventListener("click",function(){Swal.fire("The Internet?","That thing is still around?","question")}),document.querySelector(".bs-error-icon").addEventListener("click",function(){Swal.fire({icon:"error",title:"Oops...",text:"Something went wrong!",footer:"<a href>Why do I have this issue?</a>"})}),document.querySelector(".bs-long-content").addEventListener("click",function(){Swal.fire({imageUrl:"https://placeholder.pics/svg/300x1500",imageHeight:1500,imageAlt:"A tall image"})}),document.querySelector(".bs-cust-html").addEventListener("click",function(){Swal.fire({title:"<strong>HTML <u>example</u></strong>",icon:"info",html:'You can use <b>bold text</b>, <a href="//sweetalert2.github.io">links</a> and other HTML tags',showCloseButton:!0,showCancelButton:!0,focusConfirm:!1,confirmButtonText:'<i class="fa fa-thumbs-up"></i> Great!',confirmButtonAriaLabel:"Thumbs up, great!",cancelButtonText:'<i class="fa fa-thumbs-down"></i>',cancelButtonAriaLabel:"Thumbs down"})}),document.querySelector(".bs-tre-button").addEventListener("click",function(){Swal.fire({title:"Do you want to save the changes?",showDenyButton:!0,showCancelButton:!0,confirmButtonText:"Save",denyButtonText:"Don't save"}).then(e=>{e.isConfirmed?Swal.fire("Saved!","","success"):e.isDenied&&Swal.fire("Changes are not saved","","info")})}),document.querySelector(".bs-cust-position").addEventListener("click",function(){Swal.fire({position:"top-end",icon:"success",title:"Your work has been saved",showConfirmButton:!1,timer:1500})}),document.querySelector(".bs-cust-anim").addEventListener("click",function(){Swal.fire({title:"Custom animation with Animate.css",showClass:{popup:"animate__animated animate__fadeInDown"},hideClass:{popup:"animate__animated animate__fadeOutUp"}})}),document.querySelector(".bs-pass-para").addEventListener("click",function(){const t=Swal.mixin({customClass:{confirmButton:"btn btn-success",cancelButton:"btn btn-danger"},buttonsStyling:!1});t.fire({title:"Are you sure?",text:"You won't be able to revert this!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"No, cancel!",reverseButtons:!0}).then(e=>{e.isConfirmed?t.fire("Deleted!","Your file has been deleted.","success"):e.dismiss===Swal.DismissReason.cancel&&t.fire("Cancelled","Your imaginary file is safe :)","error")})}),document.querySelector(".bs-cust-img").addEventListener("click",function(){Swal.fire({title:"Sweet!",text:"Modal with a custom image.",imageUrl:"https://unsplash.it/400/200",imageWidth:400,imageHeight:200,imageAlt:"Custom image"})}),document.querySelector(".bs-auto-close").addEventListener("click",function(){let e;Swal.fire({title:"Auto close alert!",html:"I will close in <b></b> milliseconds.",timer:2e3,timerProgressBar:!0,willOpen:()=>{Swal.showLoading(),e=setInterval(()=>{var e=Swal.getContent();e&&(e=e.querySelector("b"))&&(e.textContent=Swal.getTimerLeft())},100)},onClose:()=>{clearInterval(e)}}).then(e=>{e.dismiss,Swal.DismissReason.timer})}),document.querySelector(".bs-rtl-lang").addEventListener("click",function(){Swal.fire({title:"هل تريد الاستمرار؟",icon:"question",iconHtml:"؟",confirmButtonText:"نعم",cancelButtonText:"لا",showCancelButton:!0,showCloseButton:!0})}),document.querySelector(".bs-ajex-req").addEventListener("click",function(){Swal.fire({title:"Submit your Github username",input:"text",inputAttributes:{autocapitalize:"off"},showCancelButton:!0,confirmButtonText:"Look up",showLoaderOnConfirm:!0,preConfirm:e=>fetch("//api.github.com/users/"+e).then(e=>{if(e.ok)return e.json();throw new Error(e.statusText)}).catch(e=>{Swal.showValidationMessage("Request failed: "+e)}),allowOutsideClick:()=>!Swal.isLoading()}).then(e=>{e.isConfirmed&&Swal.fire({title:e.value.login+"'s avatar",imageUrl:e.value.avatar_url})})}),document.querySelector(".bs-mixin-exp").addEventListener("click",function(){Swal.mixin({toast:!0,position:"top-end",showConfirmButton:!1,timer:3e3,timerProgressBar:!0,didOpen:e=>{e.addEventListener("mouseenter",Swal.stopTimer),e.addEventListener("mouseleave",Swal.resumeTimer)}}).fire({icon:"success",title:"Signed in successfully"})}),document.querySelector(".bs-success-ico").addEventListener("click",function(){Swal.fire({icon:"success",title:"Success modal"})}),document.querySelector(".bs-error-ico").addEventListener("click",function(){Swal.fire({icon:"error",title:"Error modal"})}),document.querySelector(".bs-warning-ico").addEventListener("click",function(){Swal.fire({icon:"warning",title:"warning modal"})}),document.querySelector(".bs-info-ico").addEventListener("click",function(){Swal.fire({icon:"info",title:"info modal"})}),document.querySelector(".bs-question-ico").addEventListener("click",function(){Swal.fire({icon:"question",title:"question modal"})}),document.querySelector(".bs-text-input").addEventListener("click",function(){(async()=>{var e=fetch("//api.ipify.org?format=json").then(e=>e.json()).then(e=>e.ip),e=(await Swal.fire({title:"Enter your IP address",input:"text",inputValue:e,showCancelButton:!0,inputValidator:e=>{if(!e)return"You need to write something!"}}))["value"];e&&Swal.fire("Your IP address is "+e)})()}),document.querySelector(".bs-email-input").addEventListener("click",function(){(async()=>{var e=(await Swal.fire({title:"Input email address",input:"email",inputPlaceholder:"Enter your email address"}))["value"];e&&Swal.fire("Entered email: "+e)})()}),document.querySelector(".bs-url-input").addEventListener("click",function(){(async()=>{var e=(await Swal.fire({input:"url",inputPlaceholder:"Enter the URL"}))["value"];e&&Swal.fire("Entered URL: "+e)})()}),document.querySelector(".bs-password-input").addEventListener("click",function(){(async()=>{var e=(await Swal.fire({title:"Enter your password",input:"password",inputPlaceholder:"Enter your password",inputAttributes:{maxlength:10,autocapitalize:"off",autocorrect:"off"}}))["value"];e&&Swal.fire("Entered password: "+e)})()}),document.querySelector(".bs-textarea-input").addEventListener("click",function(){(async()=>{var e=(await Swal.fire({input:"textarea",inputPlaceholder:"Type your message here...",inputAttributes:{"aria-label":"Type your message here"},showCancelButton:!0}))["value"];e&&Swal.fire(e)})()}),document.querySelector(".bs-select-input").addEventListener("click",function(){(async()=>{var e=(await Swal.fire({title:"Select field validation",input:"select",inputOptions:{Fruits:{apples:"Apples",bananas:"Bananas",grapes:"Grapes",oranges:"Oranges"},Vegetables:{potato:"Potato",broccoli:"Broccoli",carrot:"Carrot"},icecream:"Ice cream"},inputPlaceholder:"Select a fruit",showCancelButton:!0,inputValidator:t=>new Promise(e=>{"oranges"===t?e():e("You need to select oranges :)")})}))["value"];e&&Swal.fire("You selected: "+e)})()}),document.querySelector(".bs-radio-input").addEventListener("click",function(){(async()=>{var e=new Promise(e=>{setTimeout(()=>{e({"#ff0000":"Red","#00ff00":"Green","#0000ff":"Blue"})},1e3)}),e=(await Swal.fire({title:"Select color",input:"radio",inputOptions:e,inputValidator:e=>{if(!e)return"You need to choose something!"}}))["value"];e&&Swal.fire({html:"You selected: "+e})})()}),document.querySelector(".bs-checkbox-input").addEventListener("click",function(){(async()=>{var e=(await Swal.fire({title:"Terms and conditions",input:"checkbox",inputValue:1,inputPlaceholder:"I agree with the terms and conditions",confirmButtonText:'Continue<i class="fa fa-arrow-right"></i>',inputValidator:e=>!e&&"You need to agree with T&C"}))["value"];e&&Swal.fire("You agreed with T&C :)")})()}),document.querySelector(".bs-file-input").addEventListener("click",function(){(async()=>{var e,t=(await Swal.fire({title:"Select image",input:"file",inputAttributes:{accept:"image/*","aria-label":"Upload your profile picture"}}))["value"];t&&((e=new FileReader).onload=e=>{Swal.fire({title:"Your uploaded picture",imageUrl:e.target.result,imageAlt:"The uploaded picture"})},e.readAsDataURL(t))})()}),document.querySelector(".bs-range-input").addEventListener("click",function(){(async()=>{Swal.fire({title:"How old are you?",icon:"question",input:"range",inputAttributes:{min:8,max:120,step:1},inputValue:25})})()}),document.querySelector(".bs-multiple-input").addEventListener("click",function(){(async()=>{var e=(await Swal.fire({title:"Multiple inputs",html:'<input id="swal-input1" class="swal2-input"><input id="swal-input2" class="swal2-input">',focusConfirm:!1,preConfirm:()=>[document.getElementById("swal-input1").value,document.getElementById("swal-input2").value]}))["value"];e&&Swal.fire(JSON.stringify(e))})()});