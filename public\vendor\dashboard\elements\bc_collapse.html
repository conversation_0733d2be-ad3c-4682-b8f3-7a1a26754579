<!doctype html><html lang="en">
<!-- Mirrored from ableproadmin.com/elements/bc_collapse.html by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Oct 2024 04:55:24 GMT -->
<head><title>Collapse | Able Pro Dashboard Template</title><!-- [Meta] --><meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimal-ui"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="description" content="Able Pro is trending dashboard template made using Bootstrap 5 design framework. Able Pro is available in Bootstrap, React, CodeIgniter, Angular,  and .net Technologies."><meta name="keywords" content="Bootstrap admin template, Dashboard UI Kit, Dashboard Template, Backend Panel, react dashboard, angular dashboard"><meta name="author" content="Phoenixcoded"><!-- [Favicon] icon --><link rel="icon" href="../assets/images/favicon.svg" type="image/x-icon"><!-- [Page specific CSS] start --><link href="../assets/css/plugins/animate.min.css" rel="stylesheet" type="text/css"><!-- [Page specific CSS] end --><!-- [Font] Family --><link rel="stylesheet" href="../assets/fonts/inter/inter.css" id="main-font-link"><!-- [phosphor Icons] https://phosphoricons.com/ --><link rel="stylesheet" href="../assets/fonts/phosphor/duotone/style.css"><!-- [Tabler Icons] https://tablericons.com --><link rel="stylesheet" href="../assets/fonts/tabler-icons.min.css"><!-- [Feather Icons] https://feathericons.com --><link rel="stylesheet" href="../assets/fonts/feather.css"><!-- [Font Awesome Icons] https://fontawesome.com/icons --><link rel="stylesheet" href="../assets/fonts/fontawesome.css"><!-- [Material Icons] https://fonts.google.com/icons --><link rel="stylesheet" href="../assets/fonts/material.css"><!-- [Template CSS Files] --><link rel="stylesheet" href="../assets/css/style.css" id="main-style-link"><script src="../assets/js/tech-stack.js"></script><link rel="stylesheet" href="../assets/css/style-preset.css"><link rel="stylesheet" href="../assets/css/uikit.css"></head><body data-pc-preset="preset-1" data-pc-sidebar-caption="true" data-pc-layout="vertical" data-pc-direction="ltr" data-pc-theme_contrast="" data-pc-theme="light" class="component-page"><!-- [ Main Content ] start --><!-- [ Pre-loader ] start --><div class="loader-bg"><div class="loader-track"><div class="loader-fill"></div></div></div><!-- [ Pre-loader ] End --><!-- [ Nav ] start --><nav class="navbar navbar-expand-md navbar-light default"><div class="container"><a class="navbar-brand" href="../index-2.html"><img src="../assets/images/logo-dark.svg" alt="logo"> </a><button class="navbar-toggler rounded" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button><div class="collapse navbar-collapse" id="navbarTogglerDemo01"><ul class="navbar-nav ms-auto mb-2 mb-lg-0 align-items-center"><li class="nav-item px-1"><a class="nav-link" href="https://phoenixcoded.gitbook.io/able-pro/" target="_blank">Documentation</a></li><li class="nav-item px-1"><a class="nav-link" href="../dashboard/index.html">Live Preview</a></li><li class="nav-item px-1 me-2 mb-2 mb-md-0"><a class="btn btn-icon btn-light-dark" target="_blank" href="https://github.com/phoenixcoded/able-pro-free-admin-dashboard-template"><i class="ti ti-brand-github"></i></a></li><li class="nav-item"><a class="btn btn btn-success buynowlinks" target="_blank" href="https://1.envato.market/zNkqj6">Purchase Now <i class="ti ti-external-link"></i></a></li></ul></div></div></nav><!-- [ Nav ] start --><section class="component-block card-border-outside"><div class="container"><div class="row"><div class="col-xl-3"><div class="offcanvas-xl offcanvas-start component-offcanvas" tabindex="-1" id="offcanvas_component"><div class="offcanvas-header"><button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#offcanvas_component" aria-label="Close"></button></div><div class="offcanvas-body p-0"><div class="card component-list-card position-xl-fixed"><div class="card-header"><div class="form-search"><i class="ph-duotone ph-magnifying-glass icon-search"></i> <input type="search" class="form-control" placeholder="ex. alert, textbox... " id="compo-menu-search"></div></div><div class="card-body p-0"><ul class="list-group list-group-flush"><li class="list-group-item"><h5 class="mt-3">Basic Components</h5></li><li><a href="#" class="list-group-item list-group-item-action">All</a></li><li><a href="bc_alert.html" class="list-group-item list-group-item-action">Alert</a></li><li><a href="bc_button.html" class="list-group-item list-group-item-action">Button</a></li><li><a href="bc_badges.html" class="list-group-item list-group-item-action">Badges</a></li><li><a href="bc_breadcrumb.html" class="list-group-item list-group-item-action">Breadcrumb</a></li><li><a href="bc_card.html" class="list-group-item list-group-item-action">Cards</a></li><li><a href="bc_color.html" class="list-group-item list-group-item-action">Color</a></li><li><a href="bc_collapse.html" class="list-group-item list-group-item-action">Collapse</a></li><li><a href="bc_carousel.html" class="list-group-item list-group-item-action">Carousel</a></li><li><a href="bc_dropdowns.html" class="list-group-item list-group-item-action">Dropdowns</a></li><li><a href="bc_offcanvas.html" class="list-group-item list-group-item-action">Offcanvas</a></li><li><a href="bc_pagination.html" class="list-group-item list-group-item-action">Pagination</a></li><li><a href="bc_progress.html" class="list-group-item list-group-item-action">Progress</a></li><li><a href="bc_list-group.html" class="list-group-item list-group-item-action">List group</a></li><li><a href="bc_modal.html" class="list-group-item list-group-item-action">Modal</a></li><li><a href="bc_spinner.html" class="list-group-item list-group-item-action">Spinner</a></li><li><a href="bc_tabs.html" class="list-group-item list-group-item-action">Tabs & pills</a></li><li><a href="bc_tooltip-popover.html" class="list-group-item list-group-item-action">Tooltip</a></li><li><a href="bc_toasts.html" class="list-group-item list-group-item-action">Toasts</a></li><li><a href="bc_typography.html" class="list-group-item list-group-item-action">Typography</a></li><li><a href="bc_extra.html" class="list-group-item list-group-item-action">Other</a></li><li class="list-group-item"><h5 class="mt-3">Advance Components</h5></li><li><a href="ac_alert.html" class="list-group-item list-group-item-action">Sweet alert</a></li><li><a href="ac_datepicker-component.html" class="list-group-item list-group-item-action">Datepicker</a></li><li><a href="ac_lightbox.html" class="list-group-item list-group-item-action">Lightbox</a></li><li><a href="ac_modal.html" class="list-group-item list-group-item-action">Modal</a></li><li><a href="ac_notification.html" class="list-group-item list-group-item-action">Notification</a></li><li><a href="ac_rangeslider.html" class="list-group-item list-group-item-action">Rangeslider</a></li><li><a href="ac_slider.html" class="list-group-item list-group-item-action">Slider</a></li><li><a href="ac_syntax_highlighter.html" class="list-group-item list-group-item-action">Syntax Highlighter</a></li><li><a href="ac_tour.html" class="list-group-item list-group-item-action">Tour</a></li><li><a href="ac_treeview.html" class="list-group-item list-group-item-action">Tree view</a></li></ul></div></div></div></div></div><div class="col-xl-9"><div class="row"><!-- prettier-ignore --><div class="col-md-10 col-xxl-9 mb-4"><a href="#" class="d-inline-flex align-items-center d-xl-none btn btn-dark mb-3" data-bs-toggle="offcanvas" data-bs-target="#offcanvas_component"><i class="ti ti-menu-2 me-2"></i> Explore Components</a><h2>Collapse</h2><p class="text-muted">Toggle the visibility of content across your project with a few classes and our JavaScript plugins.</p><div><a class="btn btn-sm btn-light-dark rounded-pill px-2" role="button" target="_blank" href="https://getbootstrap.com/docs/5.2/components/collapse/"><i class="ti ti-external-link me-1"></i> Reference</a></div></div></div><!-- [ Main Content ] start --><div class="row"><!-- [ basic-collapse ] start --><div class="col-sm-12"><h5 class="mb-3">Basic Collapse</h5><hr><div class="pc-component"><div class="card"><div class="card-header border-bottom-0"><a class="btn btn-primary m-t-5" data-bs-toggle="collapse" href="#collapseExample" role="button" aria-expanded="false" aria-controls="collapseExample">Link with href</a> <button class="btn btn-primary m-t-5" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">Button with data-bs-target</button></div><div class="collapse" id="collapseExample"><div class="card-body border-top"><p class="mb-0">Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident.</p></div></div></div></div></div><!-- [ basic-collapse ] end --><!-- [ multiple-collapse ] start --><div class="col-sm-12 mb-3"><h5 class="mb-3">Multiple Targets</h5><hr><div class="pc-component"><a class="btn mb-1 btn-primary" data-bs-toggle="collapse" href="#multiCollapseExample1" role="button" aria-expanded="false" aria-controls="multiCollapseExample1">Toggle first element</a> <button class="btn mb-1 btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#multiCollapseExample2" aria-expanded="false" aria-controls="multiCollapseExample2">Toggle second element</button> <button class="btn mb-1 btn-primary" type="button" data-bs-toggle="collapse" data-bs-target=".multi-collapse" aria-expanded="false" aria-controls="multiCollapseExample1 multiCollapseExample2">Toggle both elements</button><div class="row"><div class="col-sm-6"><div class="collapse multi-collapse mt-2" id="multiCollapseExample1"><div class="card"><div class="card-body"><p class="mb-0">Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident.</p></div></div></div></div><div class="col-sm-6"><div class="collapse multi-collapse mt-2" id="multiCollapseExample2"><div class="card"><div class="card-body"><p class="mb-0">Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident.</p></div></div></div></div></div></div></div><!-- [ multiple-collapse ] end --><!-- [ accordion-collapse ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Accordion</h5></div><div class="card-body pc-component"><div class="accordion card" id="accordionExample"><div class="accordion-item"><h2 class="accordion-header" id="headingOne"><button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">Accordion Item #1</button></h2><div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionExample"><div class="accordion-body"><strong>This is the first item's accordion body.</strong> It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within the <code>.accordion-body</code>, though the transition does limit overflow.</div></div></div><div class="accordion-item"><h2 class="accordion-header" id="headingTwo"><button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">Accordion Item #2</button></h2><div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionExample"><div class="accordion-body"><strong>This is the second item's accordion body.</strong> It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within the <code>.accordion-body</code>, though the transition does limit overflow.</div></div></div><div class="accordion-item"><h2 class="accordion-header" id="headingThree"><button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">Accordion Item #3</button></h2><div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionExample"><div class="accordion-body"><strong>This is the third item's accordion body.</strong> It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within the <code>.accordion-body</code>, though the transition does limit overflow.</div></div></div></div></div></div><h5 class="mt-5 mb-3">Accordion Flush</h5><hr><div class="card"><div class="card-body pc-component"><div class="accordion accordion-flush" id="accordionFlushExample"><div class="accordion-item"><h2 class="accordion-header" id="flush-headingOne"><button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">Accordion Item #1</button></h2><div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne" data-bs-parent="#accordionFlushExample"><div class="accordion-body">Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably haven't heard of them accusamus labore sustainable VHS.</div></div></div><div class="accordion-item"><h2 class="accordion-header" id="flush-headingTwo"><button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">Accordion Item #2</button></h2><div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo" data-bs-parent="#accordionFlushExample"><div class="accordion-body">Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably haven't heard of them accusamus labore sustainable VHS.</div></div></div><div class="accordion-item"><h2 class="accordion-header" id="flush-headingThree"><button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">Accordion Item #3</button></h2><div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree" data-bs-parent="#accordionFlushExample"><div class="accordion-body">Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably haven't heard of them accusamus labore sustainable VHS.</div></div></div></div></div></div></div><!-- [ accordion-collapse ] end --></div><!-- [ Main Content ] end --></div></div></div></section><!-- [ Main Content ] end --><!-- Required Js --><script src="../assets/js/plugins/popper.min.js"></script><script src="../assets/js/plugins/simplebar.min.js"></script><script src="../assets/js/plugins/bootstrap.min.js"></script><script src="../assets/js/fonts/custom-font.js"></script><script src="../assets/js/pcoded.js"></script><script src="../assets/js/plugins/feather.min.js"></script><div class="floting-button"><a href="https://1.envato.market/zNkqj6" class="btn btn btn-danger buynowlinks d-inline-flex align-items-center gap-2" data-bs-toggle="tooltip" title="Buy Now"><i class="ph-duotone ph-shopping-cart"></i> <span>Buy Now</span></a></div><script>layout_change('light');</script><script>change_box_container('false');</script><script>layout_caption_change('true');</script><script>layout_rtl_change('false');</script><script>preset_change('preset-1');</script><script>main_layout_change('vertical');</script><!-- [ footer apps ] start --><footer class="footer"><div class="container title mb-0"><div class="row align-items-center"><div class="col-md-8"><h2 class="mb-3">Be the first to know</h2><p class="mb-4 mb-md-0">Simply submit your email, we share you the top news related to Able Pro feature updates, roadmap, and news.</p></div><div class="col-md-4"><div class="row"><div class="col"><input type="email" class="form-control" placeholder="Enter your email"></div><div class="col-auto"><button class="btn btn-primary">Subscribe</button></div></div></div></div></div><div class="footer-top"><div class="container"><div class="row"><div class="col-md-4"><img src="../assets/images/logo-dark.svg" alt="image" class="footer-logo img-fluid mb-3"><p class="mb-4">Since 2017, More than 50K+ Developers trust the Phoenixcoded Digital Product. Mantis React is Manage under their Experienced Team Players.</p></div><div class="col-md-8"><div class="row"><div class="col-sm-4 my-3 my-sm-0"><h5 class="mb-4">Company</h5><ul class="list-unstyled footer-link"><li><a href="#" target="_blank">About</a></li><li><a href="#" target="_blank">Blog</a></li><li><a href="#" target="_blank">Team</a></li><li><a href="#" target="_blank">Free Version</a></li></ul></div><div class="col-sm-4 my-3 my-sm-0"><h5 class="mb-4">Help & Support</h5><ul class="list-unstyled footer-link"><li><a href="#" target="_blank">Documentation</a></li><li><a href="#" target="_blank">Contact us</a></li><li><a href="#" target="_blank">Support</a></li><li><a href="#" target="_blank">RoadMap</a></li></ul></div><div class="col-sm-4 my-3 my-sm-0"><h5 class="mb-4">Useful Resources</h5><ul class="list-unstyled footer-link"><li><a href="#" target="_blank">Terms & Condition</a></li><li><a href="#" target="_blank">Privacy Policy</a></li><li><a href="#" target="_blank">Licencse</a></li><li><a href="#" target="_blank">FAQ</a></li></ul></div></div></div></div></div></div><div class="footer-bottom"><div class="container"><div class="row align-items-center"><div class="col my-1"><p class="mb-0">© Handcrafted by Team <a href="https://themeforest.net/user/phoenixcoded" target="_blank">Phoenixcoded</a></p></div><div class="col-auto my-1"><ul class="list-inline footer-sos-link mb-0"><li class="list-inline-item"><a href="https://fb.com/phoenixcoded"><i class="ph-duotone ph-facebook-logo f-20"></i></a></li></ul></div></div></div></div></footer><!-- [ footer apps ] End --><script src="../../cdnjs.cloudflare.com/ajax/libs/highlight.js/9.12.0/highlight.min.js"></script><script src="../assets/js/plugins/clipboard.min.js"></script><script src="../assets/js/component.js"></script><script>// pc-component
  var elem = document.querySelectorAll('.component-list-card a');
  for (var l = 0; l < elem.length; l++) {
    var pageUrl = window.location.href.split(/[?#]/)[0];
    if (elem[l].href == pageUrl && elem[l].getAttribute('href') != '') {
      elem[l].classList.add('active');
    }
  }
  document.querySelector('#compo-menu-search').addEventListener('keyup', function () {
    var tval = document.querySelector('#compo-menu-search').value.toLowerCase();
    var elem = document.querySelectorAll('.component-list-card a');
    for (var l = 0; l < elem.length; l++) {
      var aval = elem[l].innerHTML.toLowerCase();
      var n = aval.indexOf(tval);
      if (n !== -1) {
        elem[l].style.display = 'block';
      } else {
        elem[l].style.display = 'none';
      }
    }
  });</script><div class="pct-c-btn"><a href="#" data-bs-toggle="offcanvas" data-bs-target="#offcanvas_pc_layout"><i class="ph-duotone ph-gear-six"></i></a></div><div class="offcanvas border-0 pct-offcanvas offcanvas-end" tabindex="-1" id="offcanvas_pc_layout"><div class="offcanvas-header"><h5 class="offcanvas-title">Settings</h5><button type="button" class="btn btn-icon btn-link-danger ms-auto" data-bs-dismiss="offcanvas" aria-label="Close"><i class="ti ti-x"></i></button></div><div class="pct-body customizer-body"><div class="offcanvas-body py-0"><ul class="list-group list-group-flush"><li class="list-group-item"><div class="pc-dark"><h6 class="mb-1">Theme Mode</h6><p class="text-muted text-sm">Choose light or dark mode or Auto</p><div class="row theme-color theme-layout"><div class="col-4"><div class="d-grid"><button class="preset-btn btn active" data-value="true" onclick="layout_change('light');" data-bs-toggle="tooltip" title="Light"><svg class="pc-icon text-warning"><use xlink:href="#custom-sun-1"></use></svg></button></div></div><div class="col-4"><div class="d-grid"><button class="preset-btn btn" data-value="false" onclick="layout_change('dark');" data-bs-toggle="tooltip" title="Dark"><svg class="pc-icon"><use xlink:href="#custom-moon"></use></svg></button></div></div><div class="col-4"><div class="d-grid"><button class="preset-btn btn" data-value="default" onclick="layout_change_default();" data-bs-toggle="tooltip" title="Automatically sets the theme based on user's operating system's color scheme."><span class="pc-lay-icon d-flex align-items-center justify-content-center"><i class="ph-duotone ph-cpu"></i></span></button></div></div></div></div></li><li class="list-group-item"><h6 class="mb-1">Theme Contrast</h6><p class="text-muted text-sm">Choose theme contrast</p><div class="row theme-contrast"><div class="col-6"><div class="d-grid"><button class="preset-btn btn" data-value="true" onclick="layout_theme_contrast_change('true');" data-bs-toggle="tooltip" title="True"><svg class="pc-icon"><use xlink:href="#custom-mask"></use></svg></button></div></div><div class="col-6"><div class="d-grid"><button class="preset-btn btn active" data-value="false" onclick="layout_theme_contrast_change('false');" data-bs-toggle="tooltip" title="False"><svg class="pc-icon"><use xlink:href="#custom-mask-1-outline"></use></svg></button></div></div></div></li><li class="list-group-item"><h6 class="mb-1">Custom Theme</h6><p class="text-muted text-sm">Choose your primary theme color</p><div class="theme-color preset-color"><a href="#!" data-bs-toggle="tooltip" title="Blue" class="active" data-value="preset-1"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Indigo" data-value="preset-2"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Purple" data-value="preset-3"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Pink" data-value="preset-4"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Red" data-value="preset-5"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Orange" data-value="preset-6"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Yellow" data-value="preset-7"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Green" data-value="preset-8"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Teal" data-value="preset-9"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Cyan" data-value="preset-10"><i class="ti ti-checks"></i></a></div></li><li class="list-group-item"><h6 class="mb-1">Theme layout</h6><p class="text-muted text-sm">Choose your layout</p><div class="theme-main-layout d-flex align-center gap-1 w-100"><a href="#!" data-bs-toggle="tooltip" title="Vertical" class="active" data-value="vertical"><img src="../assets/images/customizer/caption-on.svg" alt="img" class="img-fluid"> </a><a href="#!" data-bs-toggle="tooltip" title="Horizontal" data-value="horizontal"><img src="../assets/images/customizer/horizontal.svg" alt="img" class="img-fluid"> </a><a href="#!" data-bs-toggle="tooltip" title="Color Header" data-value="color-header"><img src="../assets/images/customizer/color-header.svg" alt="img" class="img-fluid"> </a><a href="#!" data-bs-toggle="tooltip" title="Compact" data-value="compact"><img src="../assets/images/customizer/compact.svg" alt="img" class="img-fluid"> </a><a href="#!" data-bs-toggle="tooltip" title="Tab" data-value="tab"><img src="../assets/images/customizer/tab.svg" alt="img" class="img-fluid"></a></div></li><li class="list-group-item"><h6 class="mb-1">Sidebar Caption</h6><p class="text-muted text-sm">Sidebar Caption Hide/Show</p><div class="row theme-color theme-nav-caption"><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn active" data-value="true" onclick="layout_caption_change('true');" data-bs-toggle="tooltip" title="Caption Show"><img src="../assets/images/customizer/caption-on.svg" alt="img" class="img-fluid"></button></div></div><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn" data-value="false" onclick="layout_caption_change('false');" data-bs-toggle="tooltip" title="Caption Hide"><img src="../assets/images/customizer/caption-off.svg" alt="img" class="img-fluid"></button></div></div></div></li><li class="list-group-item"><div class="pc-rtl"><h6 class="mb-1">Theme Layout</h6><p class="text-muted text-sm">LTR/RTL</p><div class="row theme-color theme-direction"><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn active" data-value="false" onclick="layout_rtl_change('false');" data-bs-toggle="tooltip" title="LTR"><img src="../assets/images/customizer/ltr.svg" alt="img" class="img-fluid"></button></div></div><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn" data-value="true" onclick="layout_rtl_change('true');" data-bs-toggle="tooltip" title="RTL"><img src="../assets/images/customizer/rtl.svg" alt="img" class="img-fluid"></button></div></div></div></div></li><li class="list-group-item pc-box-width"><div class="pc-container-width"><h6 class="mb-1">Layout Width</h6><p class="text-muted text-sm">Choose Full or Container Layout</p><div class="row theme-color theme-container"><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn active" data-value="false" onclick="change_box_container('false')" data-bs-toggle="tooltip" title="Full Width"><img src="../assets/images/customizer/full.svg" alt="img" class="img-fluid"></button></div></div><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn" data-value="true" onclick="change_box_container('true')" data-bs-toggle="tooltip" title="Fixed Width"><img src="../assets/images/customizer/fixed.svg" alt="img" class="img-fluid"></button></div></div></div></div></li><li class="list-group-item"><div class="d-grid"><button class="btn btn-light-danger" id="layoutreset">Reset Layout</button></div></li></ul></div></div></div></body>
<!-- Mirrored from ableproadmin.com/elements/bc_collapse.html by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Oct 2024 04:55:24 GMT -->
</html>