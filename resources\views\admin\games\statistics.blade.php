@extends('admin.layout.master')

@section('content')
<div class="fade-in-up">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-4">
                    <h1 class="heading-font mb-2" style="color: var(--primary-pink);">
                        <i class="fas fa-chart-bar me-2"></i>Game Statistics
                    </h1>
                    <p class="text-muted mb-0">Comprehensive analytics and performance metrics for all minigames</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="heading-font mb-0">{{ $totalGames }}</h3>
                            <p class="text-muted mb-0">Total Games</p>
                            <small class="text-success">
                                <i class="fas fa-check me-1"></i>{{ $activeGames }} active
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="heading-font mb-0">{{ $totalConfigs }}</h3>
                            <p class="text-muted mb-0">Game Configurations</p>
                            <small class="text-info">
                                <i class="fas fa-play me-1"></i>{{ $activeConfigs }} running
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="heading-font mb-0">{{ $totalConfigs > 0 ? number_format(($activeConfigs / $totalConfigs) * 100, 1) : 0 }}%</h3>
                            <p class="text-muted mb-0">Active Rate</p>
                            <small class="text-primary">
                                <i class="fas fa-chart-line me-1"></i>Configuration usage
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="heading-font mb-0">{{ $totalGames > 0 ? number_format($totalConfigs / $totalGames, 1) : 0 }}</h3>
                            <p class="text-muted mb-0">Avg Configs/Game</p>
                            <small class="text-success">
                                <i class="fas fa-star me-1"></i>Popularity metric
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="heading-font mb-0">
                        <i class="fas fa-chart-area me-2"></i>Game Performance Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3" style="opacity: 0.3;"></i>
                        <p class="text-muted mb-0">Performance charts will be available once games are played</p>
                        <small class="text-muted">Data visualization coming soon</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="heading-font mb-0">
                        <i class="fas fa-list me-2"></i>Game Status Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="status-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="status-dot bg-success me-2"></div>
                                <span>Active Games</span>
                            </div>
                            <span class="badge bg-success">{{ $activeGames }}</span>
                        </div>
                    </div>
                    <div class="status-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="status-dot bg-secondary me-2"></div>
                                <span>Inactive Games</span>
                            </div>
                            <span class="badge bg-secondary">{{ $totalGames - $activeGames }}</span>
                        </div>
                    </div>
                    <div class="status-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="status-dot bg-info me-2"></div>
                                <span>Running Configs</span>
                            </div>
                            <span class="badge bg-info">{{ $activeConfigs }}</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="status-dot bg-warning me-2"></div>
                                <span>Pending Configs</span>
                            </div>
                            <span class="badge bg-warning">{{ $totalConfigs - $activeConfigs }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="heading-font mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.games.index') }}" class="btn btn-primary w-100 quick-action-btn">
                                <i class="fas fa-gamepad mb-2"></i>
                                <div>Manage Games</div>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.game-configs.index') }}" class="btn btn-success w-100 quick-action-btn">
                                <i class="fas fa-cogs mb-2"></i>
                                <div>Game Configurations</div>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.games.create') }}" class="btn btn-warning w-100 quick-action-btn">
                                <i class="fas fa-plus-circle mb-2"></i>
                                <div>Create New Game</div>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.clients.index') }}" class="btn btn-info w-100 quick-action-btn">
                                <i class="fas fa-users mb-2"></i>
                                <div>Client Management</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Statistics specific styles */
.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(251, 207, 232, 0.3);
}

.status-item:last-child {
    border-bottom: none;
}

.quick-action-btn {
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    transform: translateY(-3px);
}

.quick-action-btn i {
    font-size: 1.5rem;
}
</style>
@endsection
