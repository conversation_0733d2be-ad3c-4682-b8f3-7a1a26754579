<!DOCTYPE html>
<html lang="en">

<head>
    <title>Login - Wedding Minigame System</title>
    <!-- [Meta] -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Wedding Minigame System - Beautiful login experience with pastel floral design" />
    <meta name="keywords" content="Wedding, Minigame, Login, Authentication" />
    <meta name="author" content="Wedding Minigame System" />
    <!-- [Favicon] icon -->
    <link rel="icon" href="{{ asset('vendor/dashboard') }}/assets/images/favicon.svg" type="image/x-icon" />
    <!-- [Font] Family -->
    <link
        href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Poppins:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <!-- [Font Awesome Icons] -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <!-- [Bootstrap] -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head><!-- [Head] end -->
<!-- [Body] Start -->

<body class="floral-login-bg">
    <!-- Floating Floral Elements -->
    <div class="floating-florals">
        <div class="floral-element floral-1">🌸</div>
        <div class="floral-element floral-2">🌺</div>
        <div class="floral-element floral-3">🌸</div>
        <div class="floral-element floral-4">🌺</div>
        <div class="floral-element floral-5">🌸</div>
        <div class="floral-element floral-6">🌺</div>
    </div>

    <div class="login-container">
        <div class="container-fluid h-100">
            <div class="row h-100 align-items-center justify-content-center">
                <div class="col-12 col-md-8 col-lg-6 col-xl-5">
                    <div class="login-card">
                        <!-- Logo Section -->
                        <div class="text-center mb-4">
                            <div class="logo-container">
                                <img src="{{ asset('logo.png') }}" alt="Logo" class="login-logo" />
                            </div>
                        </div>

                        <!-- Welcome Section -->
                        <div class="text-center mb-4">
                            <h1 class="login-title">Welcome Back</h1>
                            <p class="login-subtitle">Sign in to your account to continue</p>
                        </div>

                        <!-- Login Form -->
                        <form action="{{ route('auth.submit') }}" method="post" class="login-form">
                            @csrf

                            <!-- Username Field -->
                            <div class="form-group mb-4">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>Username
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" name="username" id="username" class="form-control login-input"
                                        placeholder="Enter your username" value="{{ old('username') }}" required />
                                    <div class="input-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                </div>
                                @error('username')
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                                </div>
                                @enderror
                            </div>

                            <!-- Password Field -->
                            <div class="form-group mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <div class="input-wrapper">
                                    <input type="password" name="password" id="password"
                                        class="form-control login-input" placeholder="Enter your password" required />
                                    <div class="input-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                    <button type="button" class="password-toggle" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="password-eye"></i>
                                    </button>
                                </div>
                                @error('password')
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                                </div>
                                @enderror
                            </div>

                            <!-- Remember Me & Forgot Password -->
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">
                                        Remember me
                                    </label>
                                </div>
                                <a href="#" class="forgot-password">
                                    Forgot Password?
                                </a>
                            </div>

                            <!-- Login Button -->
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn login-btn">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Sign In
                                </button>
                            </div>

                            <!-- Divider -->
                            <div class="divider">
                                <span>or</span>
                            </div>

                            <!-- Additional Info -->
                            <div class="text-center mt-4">
                                <p class="login-footer">
                                    <i class="fas fa-heart text-pink me-1"></i>
                                    Wedding Minigame System
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-pink: #ec4899;
            --secondary-pink: #f8d7da;
            --light-pink: #fdf2f8;
            --accent-pink: #be185d;
            --text-dark: #721c24;
            --text-light: #6b7280;
            --white: #ffffff;
            --shadow: 0 8px 32px rgba(251, 207, 232, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .floral-login-bg {
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #f3e8ff 100%);
            position: relative;
        }

        .floating-florals {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floral-element {
            position: absolute;
            font-size: 2rem;
            opacity: 0.3;
            animation: float 6s ease-in-out infinite;
        }

        .floral-1 {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floral-2 {
            top: 20%;
            right: 15%;
            animation-delay: 1s;
        }

        .floral-3 {
            top: 60%;
            left: 5%;
            animation-delay: 2s;
        }

        .floral-4 {
            bottom: 20%;
            right: 10%;
            animation-delay: 3s;
        }

        .floral-5 {
            bottom: 10%;
            left: 20%;
            animation-delay: 4s;
        }

        .floral-6 {
            top: 40%;
            right: 5%;
            animation-delay: 5s;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(10deg);
            }
        }

        .login-container {
            position: relative;
            z-index: 2;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(251, 207, 232, 0.3);
            border-radius: 25px;
            padding: 40px;
            box-shadow: var(--shadow);
            max-width: 450px;
            width: 100%;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-container {
            margin-bottom: 20px;
        }

        .login-logo {
            max-width: 200px;
            height: auto;
            filter: drop-shadow(0 4px 8px rgba(251, 207, 232, 0.3));
        }

        .login-title {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-pink);
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: var(--text-light);
            font-size: 1rem;
            margin-bottom: 0;
        }

        .form-label {
            color: var(--text-dark);
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 8px;
        }

        .input-wrapper {
            position: relative;
        }

        .login-input {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 15px;
            padding: 15px 50px 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .login-input:focus {
            outline: none;
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 0.2rem rgba(236, 72, 153, 0.25);
            background: rgba(255, 255, 255, 0.95);
        }

        .input-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-pink);
            opacity: 0.7;
        }

        .password-toggle {
            position: absolute;
            right: 45px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 5px;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--primary-pink);
        }

        .form-check-input:checked {
            background-color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .form-check-label {
            color: var(--text-dark);
            font-size: 0.9rem;
        }

        .forgot-password {
            color: var(--primary-pink);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: var(--accent-pink);
            text-decoration: underline;
        }

        .login-btn {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            border: none;
            border-radius: 15px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--white);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(236, 72, 153, 0.3);
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(236, 72, 153, 0.4);
            background: linear-gradient(135deg, var(--accent-pink) 0%, var(--primary-pink) 100%);
        }

        .divider {
            position: relative;
            text-align: center;
            margin: 20px 0;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--secondary-pink), transparent);
        }

        .divider span {
            background: rgba(255, 255, 255, 0.9);
            padding: 0 15px;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .login-footer {
            color: var(--text-light);
            font-size: 0.9rem;
            margin: 0;
        }

        .text-pink {
            color: var(--primary-pink) !important;
        }

        .error-message {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 5px;
            display: flex;
            align-items: center;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-card {
                padding: 30px 25px;
                margin: 20px;
            }

            .login-title {
                font-size: 2rem;
            }

            .floral-element {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .login-card {
                padding: 25px 20px;
            }

            .login-title {
                font-size: 1.8rem;
            }

            .login-logo {
                max-width: 150px;
            }
        }
    </style>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordEye = document.getElementById('password-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordEye.classList.remove('fa-eye');
                passwordEye.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordEye.classList.remove('fa-eye-slash');
                passwordEye.classList.add('fa-eye');
            }
        }

        // Form submission with loading state
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.login-btn');
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
        });

        // Add floating animation to floral elements
        document.addEventListener('DOMContentLoaded', function() {
            const floralElements = document.querySelectorAll('.floral-element');

            floralElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
            });
        });
    </script>
</body>

</html>