<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\TriviaQuestion;

class TestClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test user for the client
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Client User',
                'password' => bcrypt('password'),
                'role' => 'client'
            ]
        );

        // Create a test client with simple URL
        $client = Client::firstOrCreate(
            ['unique_url' => 'test-client'],
            [
                'user_id' => $user->id,
                'client_name' => 'Test Wedding Client',
                'package_id' => 1, // Use first available package
                'template_id' => 1, // Use first available template
                'is_have_minigame' => true
            ]
        );

        // Get or create trivia game
        $triviaGame = Game::firstOrCreate(
            ['minigame_name' => 'trivia'],
            [
                'display_name' => 'Wedding Trivia',
                'description' => 'Test your knowledge about the couple!',
                'is_active' => true
            ]
        );

        // Create game configuration
        $gameConfig = GameConfig::firstOrCreate(
            [
                'client_id' => $client->id,
                'game_id' => $triviaGame->id
            ],
            [
                'game_title' => 'Test Wedding Trivia',
                'top_players' => 10,
                'is_starting' => true,
                'is_done' => false
            ]
        );

        // Create sample trivia questions
        $questions = [
            [
                'question_text' => 'What is the bride\'s favorite color?',
                'option_a' => 'Pink',
                'option_b' => 'Blue',
                'option_c' => 'Purple',
                'option_d' => 'Green',
                'correct_answer' => 'Pink'
            ],
            [
                'question_text' => 'Where did the couple first meet?',
                'option_a' => 'At work',
                'option_b' => 'At school',
                'option_c' => 'At a coffee shop',
                'option_d' => 'Through friends',
                'correct_answer' => 'At a coffee shop'
            ],
            [
                'question_text' => 'What is the groom\'s hobby?',
                'option_a' => 'Photography',
                'option_b' => 'Gaming',
                'option_c' => 'Cooking',
                'option_d' => 'Sports',
                'correct_answer' => 'Photography'
            ],
            [
                'question_text' => 'What year did they start dating?',
                'correct_answer' => '2020'
            ],
            [
                'question_text' => 'What is their favorite vacation destination?',
                'correct_answer' => 'Bali'
            ]
        ];

        foreach ($questions as $questionData) {
            TriviaQuestion::firstOrCreate(
                [
                    'game_config_id' => $gameConfig->id,
                    'question_text' => $questionData['question_text']
                ],
                $questionData
            );
        }

        $this->command->info('Test client created successfully!');
        $this->command->info('Client ID: ' . $client->id);
        $this->command->info('Unique URL: ' . $client->unique_url);
        $this->command->info('Test URL: ' . url('/game/' . $client->id . '/' . $client->unique_url . '/trivia'));
    }
}
