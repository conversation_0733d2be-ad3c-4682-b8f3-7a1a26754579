<!doctype html><html lang="en">
<!-- Mirrored from ableproadmin.com/elements/bc_extra.html by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Oct 2024 04:55:27 GMT -->
<head><title>Utilities | Able Pro Dashboard Template</title><!-- [Meta] --><meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimal-ui"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="description" content="Able Pro is trending dashboard template made using Bootstrap 5 design framework. Able Pro is available in Bootstrap, React, CodeIgniter, Angular,  and .net Technologies."><meta name="keywords" content="Bootstrap admin template, Dashboard UI Kit, Dashboard Template, Backend Panel, react dashboard, angular dashboard"><meta name="author" content="Phoenixcoded"><!-- [Favicon] icon --><link rel="icon" href="../assets/images/favicon.svg" type="image/x-icon"><!-- [Page specific CSS] start --><link href="../assets/css/plugins/animate.min.css" rel="stylesheet" type="text/css"><!-- [Page specific CSS] end --><!-- [Font] Family --><link rel="stylesheet" href="../assets/fonts/inter/inter.css" id="main-font-link"><!-- [phosphor Icons] https://phosphoricons.com/ --><link rel="stylesheet" href="../assets/fonts/phosphor/duotone/style.css"><!-- [Tabler Icons] https://tablericons.com --><link rel="stylesheet" href="../assets/fonts/tabler-icons.min.css"><!-- [Feather Icons] https://feathericons.com --><link rel="stylesheet" href="../assets/fonts/feather.css"><!-- [Font Awesome Icons] https://fontawesome.com/icons --><link rel="stylesheet" href="../assets/fonts/fontawesome.css"><!-- [Material Icons] https://fonts.google.com/icons --><link rel="stylesheet" href="../assets/fonts/material.css"><!-- [Template CSS Files] --><link rel="stylesheet" href="../assets/css/style.css" id="main-style-link"><script src="../assets/js/tech-stack.js"></script><link rel="stylesheet" href="../assets/css/style-preset.css"><link rel="stylesheet" href="../assets/css/uikit.css"></head><body data-pc-preset="preset-1" data-pc-sidebar-caption="true" data-pc-layout="vertical" data-pc-direction="ltr" data-pc-theme_contrast="" data-pc-theme="light" class="component-page"><!-- [ Main Content ] start --><!-- [ Pre-loader ] start --><div class="loader-bg"><div class="loader-track"><div class="loader-fill"></div></div></div><!-- [ Pre-loader ] End --><!-- [ Nav ] start --><nav class="navbar navbar-expand-md navbar-light default"><div class="container"><a class="navbar-brand" href="../index-2.html"><img src="../assets/images/logo-dark.svg" alt="logo"> </a><button class="navbar-toggler rounded" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button><div class="collapse navbar-collapse" id="navbarTogglerDemo01"><ul class="navbar-nav ms-auto mb-2 mb-lg-0 align-items-center"><li class="nav-item px-1"><a class="nav-link" href="https://phoenixcoded.gitbook.io/able-pro/" target="_blank">Documentation</a></li><li class="nav-item px-1"><a class="nav-link" href="../dashboard/index.html">Live Preview</a></li><li class="nav-item px-1 me-2 mb-2 mb-md-0"><a class="btn btn-icon btn-light-dark" target="_blank" href="https://github.com/phoenixcoded/able-pro-free-admin-dashboard-template"><i class="ti ti-brand-github"></i></a></li><li class="nav-item"><a class="btn btn btn-success buynowlinks" target="_blank" href="https://1.envato.market/zNkqj6">Purchase Now <i class="ti ti-external-link"></i></a></li></ul></div></div></nav><!-- [ Nav ] start --><section class="component-block"><div class="container"><div class="row"><div class="col-xl-3"><div class="offcanvas-xl offcanvas-start component-offcanvas" tabindex="-1" id="offcanvas_component"><div class="offcanvas-header"><button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#offcanvas_component" aria-label="Close"></button></div><div class="offcanvas-body p-0"><div class="card component-list-card position-xl-fixed"><div class="card-header"><div class="form-search"><i class="ph-duotone ph-magnifying-glass icon-search"></i> <input type="search" class="form-control" placeholder="ex. alert, textbox... " id="compo-menu-search"></div></div><div class="card-body p-0"><ul class="list-group list-group-flush"><li class="list-group-item"><h5 class="mt-3">Basic Components</h5></li><li><a href="#" class="list-group-item list-group-item-action">All</a></li><li><a href="bc_alert.html" class="list-group-item list-group-item-action">Alert</a></li><li><a href="bc_button.html" class="list-group-item list-group-item-action">Button</a></li><li><a href="bc_badges.html" class="list-group-item list-group-item-action">Badges</a></li><li><a href="bc_breadcrumb.html" class="list-group-item list-group-item-action">Breadcrumb</a></li><li><a href="bc_card.html" class="list-group-item list-group-item-action">Cards</a></li><li><a href="bc_color.html" class="list-group-item list-group-item-action">Color</a></li><li><a href="bc_collapse.html" class="list-group-item list-group-item-action">Collapse</a></li><li><a href="bc_carousel.html" class="list-group-item list-group-item-action">Carousel</a></li><li><a href="bc_dropdowns.html" class="list-group-item list-group-item-action">Dropdowns</a></li><li><a href="bc_offcanvas.html" class="list-group-item list-group-item-action">Offcanvas</a></li><li><a href="bc_pagination.html" class="list-group-item list-group-item-action">Pagination</a></li><li><a href="bc_progress.html" class="list-group-item list-group-item-action">Progress</a></li><li><a href="bc_list-group.html" class="list-group-item list-group-item-action">List group</a></li><li><a href="bc_modal.html" class="list-group-item list-group-item-action">Modal</a></li><li><a href="bc_spinner.html" class="list-group-item list-group-item-action">Spinner</a></li><li><a href="bc_tabs.html" class="list-group-item list-group-item-action">Tabs & pills</a></li><li><a href="bc_tooltip-popover.html" class="list-group-item list-group-item-action">Tooltip</a></li><li><a href="bc_toasts.html" class="list-group-item list-group-item-action">Toasts</a></li><li><a href="bc_typography.html" class="list-group-item list-group-item-action">Typography</a></li><li><a href="bc_extra.html" class="list-group-item list-group-item-action">Other</a></li><li class="list-group-item"><h5 class="mt-3">Advance Components</h5></li><li><a href="ac_alert.html" class="list-group-item list-group-item-action">Sweet alert</a></li><li><a href="ac_datepicker-component.html" class="list-group-item list-group-item-action">Datepicker</a></li><li><a href="ac_lightbox.html" class="list-group-item list-group-item-action">Lightbox</a></li><li><a href="ac_modal.html" class="list-group-item list-group-item-action">Modal</a></li><li><a href="ac_notification.html" class="list-group-item list-group-item-action">Notification</a></li><li><a href="ac_rangeslider.html" class="list-group-item list-group-item-action">Rangeslider</a></li><li><a href="ac_slider.html" class="list-group-item list-group-item-action">Slider</a></li><li><a href="ac_syntax_highlighter.html" class="list-group-item list-group-item-action">Syntax Highlighter</a></li><li><a href="ac_tour.html" class="list-group-item list-group-item-action">Tour</a></li><li><a href="ac_treeview.html" class="list-group-item list-group-item-action">Tree view</a></li></ul></div></div></div></div></div><div class="col-xl-9"><div class="row"><!-- prettier-ignore --><div class="col-md-10 col-xxl-9 mb-4"><a href="#" class="d-inline-flex align-items-center d-xl-none btn btn-dark mb-3" data-bs-toggle="offcanvas" data-bs-target="#offcanvas_component"><i class="ti ti-menu-2 me-2"></i> Explore Components</a><h2>Utilities</h2><p class="text-muted">The utility API is a Sass-based tool to generate utility classes.</p><div><a class="btn btn-sm btn-light-dark rounded-pill px-2" role="button" target="_blank" href="https://getbootstrap.com/docs/5.2/utilities/background/"><i class="ti ti-external-link me-1"></i> Reference</a></div></div></div><!-- [ Main Content ] start --><div class="row"><!-- [ Colored links ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Colored links <span class="badge bg-danger">NEW</span></h5><p class="mb-0">You can use the <code>.link-*</code> classes to colorize links. these classes have a <code>:hover</code> and <code>:focus</code> state.</p></div><div class="card-body"><a href="#" class="me-3 link-primary">Primary link</a> <a href="#" class="me-3 link-secondary">Secondary link</a> <a href="#" class="me-3 link-success">Success link</a> <a href="#" class="me-3 link-danger">Danger link</a> <a href="#" class="me-3 link-warning">Warning link</a> <a href="#" class="me-3 link-info">Info link</a><div class="bg-white px-3 py-2 d-inline-block rounded"><a href="#" class="me-0 link-dark">Dark link</a></div><div class="bg-dark px-3 py-2 d-inline-block rounded"><a href="#" class="me-0 link-light">Light link</a></div></div></div></div><!-- [ Colored links ] end --><!-- [ Text selection ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Text selection <span class="badge bg-danger">NEW</span></h5><p class="mb-0">Change the way in which the content is selected when the user interacts with it.</p></div><div class="card-body"><div class="row"><label class="col-lg-2 col-sm-12 text-lg-end"><b>User select all</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p class="user-select-all">This paragraph will be entirely selected when clicked by the user.</p></div></div><div class="row"><label class="col-lg-2 col-sm-12 text-lg-end"><b>User select auto</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p class="user-select-auto">This paragraph has default select behavior.</p></div></div><div class="row"><label class="col-lg-2 col-sm-12 text-lg-end"><b>User select none</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p class="user-select-none">This paragraph will not be selectable when clicked by the user.</p></div></div></div></div></div><!-- [ Text selection ] end --><!-- [ Line height ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Line height <span class="badge bg-danger">NEW</span></h5><p class="mb-0">Change the line height with <code>.lh-*</code> utilities.</p></div><div class="card-body"><div class="row"><label class="col-lg-2 col-sm-12 text-lg-end"><b>Line height 1</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p class="lh-1">Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. Donec sed odio dui. Cras mattis pannenkoek purus sit amet fermentum.</p></div></div><div class="row"><label class="col-lg-2 col-sm-12 text-lg-end"><b>Line height small</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p class="lh-sm">Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. Donec sed odio dui. Cras mattis pannenkoek purus sit amet fermentum.</p></div></div><div class="row"><label class="col-lg-2 col-sm-12 text-lg-end"><b>Line height base</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p class="lh-base">Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. Donec sed odio dui. Cras mattis pannenkoek purus sit amet fermentum.</p></div></div><div class="row"><label class="col-lg-2 col-sm-12 text-lg-end"><b>Line height large</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p class="lh-lg">Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. Donec sed odio dui. Cras mattis pannenkoek purus sit amet fermentum.</p></div></div></div></div></div><!-- [ Line height ] end --><!-- [ Pointer events ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Pointer events <span class="badge bg-danger">NEW</span></h5><p class="mb-0">Bootstrap provides <code>pe-none</code> and <code>pe-auto</code> classes to prevent or add element interactions.</p></div><div class="card-body"><div class="row"><label class="col-lg-3 col-sm-12 text-lg-end"><b>Pointer events none</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p><a href="#" class="pe-none">This link</a> can not be clicked.</p></div></div><div class="row"><label class="col-lg-3 col-sm-12 text-lg-end"><b>Pointer events auto</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p><a href="#" class="pe-auto">This link</a> can be clicked (this is default behaviour).</p></div></div><div class="row"><label class="col-lg-3 col-sm-12 text-lg-end"><b>Pointer events combine</b></label><div class="col-lg-6 col-md-10 col-sm-12"><p class="pe-none"><a href="#">This link</a> can not be clicked because the <code>pointer-events</code> property is inherited from its parent. However, <a href="#" class="pe-auto">this link</a> has a <code>pe-auto</code> class and can be clicked.</p></div></div></div></div></div><!-- [ Pointer events ] end --><!-- [ Position values ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Position values <span class="badge bg-danger">NEW</span></h5><p class="mb-0">Arrange elements easily with the edge positioning utilities, The format is <code>{property}-{position}</code>.</p></div><div class="card-body"><div class="mb-3"><div class="position-relative bg-body" style="height: 250px"><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-0 start-0" data-bs-toggle="tooltip" title="position-absolute top-0 start-0"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-0 end-0" data-bs-toggle="tooltip" title="position-absolute top-0 end-0"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-50 start-50" data-bs-toggle="tooltip" title="position-absolute top-50 start-50"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute bottom-50 end-50" data-bs-toggle="tooltip" title="position-absolute bottom-50 end-50"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute bottom-0 start-0" data-bs-toggle="tooltip" title="position-absolute bottom-0 start-0"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute bottom-0 end-0" data-bs-toggle="tooltip" title="position-absolute bottom-0 end-0"></div></div></div><hr><h5>Center elements</h5><p class="mb-0">In addition, you can also center the elements with the transform utility class <code>.translate-middle</code>.</p><div class="p-4 my-3"><div class="position-relative bg-body" style="height: 250px"><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-0 start-0 translate-middle" data-bs-toggle="tooltip" title="position-absolute top-0 start-0 translate-middle"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-0 start-50 translate-middle" data-bs-toggle="tooltip" title="position-absolute top-0 start-50 translate-middle"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-0 start-100 translate-middle" data-bs-toggle="tooltip" title="position-absolute top-0 start-100 translate-middle"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-50 start-0 translate-middle" data-bs-toggle="tooltip" title="position-absolute top-50 start-0 translate-middle"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-50 start-50 translate-middle" data-bs-toggle="tooltip" title="position-absolute top-50 start-50 translate-middle"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-50 start-100 translate-middle" data-bs-toggle="tooltip" title="position-absolute top-50 start-100 translate-middle"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-100 start-0 translate-middle" data-bs-toggle="tooltip" title="position-absolute top-100 start-0 translate-middle"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-100 start-50 translate-middle" data-bs-toggle="tooltip" title="position-absolute top-100 start-50 translate-middle"></div><div class="hei-40 wid-40 rounded bg-secondary position-absolute top-100 start-100 translate-middle" data-bs-toggle="tooltip" title="position-absolute top-100 start-100 translate-middle"></div></div></div><hr><h5>Examples</h5><p class="mb-0">Here are some real life examples of these classes.</p><div class="p-3 p-sm-5 d-flex justify-content-between"><button type="button" class="btn btn-primary position-relative">Mails <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-secondary">+99 <span class="visually-hidden">unread messages</span></span></button> <button type="button" class="btn btn-dark position-relative">Marker <svg width="1em" height="1em" viewBox="0 0 16 16" class="position-absolute top-100 start-50 translate-middle mt-1 bi bi-caret-down-fill" fill="#343a40" xmlns="http://www.w3.org/2000/svg"><path d="M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"></path></svg></button> <button type="button" class="btn btn-primary position-relative">Alerts <span class="position-absolute top-0 start-100 translate-middle badge border border-light rounded-circle bg-danger p-2"><span class="visually-hidden">unread messages</span></span></button></div><hr><p class="mb-0">You can use these classes with existing components to create new ones. Remember that you can extend its functionality by adding entries to the <code>$position-values</code> variable.</p><div class="p-3 p-sm-5"><div class="position-relative"><div class="progress" style="height: 3px"><div class="progress-bar" role="progressbar" style="width: 50%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div></div><button type="button" class="position-absolute top-0 start-0 translate-middle btn btn-sm btn-primary rounded-pill" style="width: 2rem; height: 2rem">1</button> <button type="button" class="position-absolute top-0 start-50 translate-middle btn btn-sm btn-primary rounded-pill" style="width: 2rem; height: 2rem">2</button> <button type="button" class="position-absolute top-0 start-100 translate-middle btn btn-sm btn-secondary rounded-pill" style="width: 2rem; height: 2rem">3</button></div></div></div></div></div><!-- [ Position values ] end --><!-- [ border ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Border</h5></div><div class="card-body"><div class="row g-4"><div class="col-sm-12"><h5>Border color</h5><hr><span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-primary" data-bs-toggle="tooltip" title="border-primary"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-secondary" data-bs-toggle="tooltip" title="border-secondary"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-success" data-bs-toggle="tooltip" title="border-success"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-danger" data-bs-toggle="tooltip" title="border-danger"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-warning" data-bs-toggle="tooltip" title="border-warning"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-info" data-bs-toggle="tooltip" title="border-info"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-light" data-bs-toggle="tooltip" title="border-light"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark" data-bs-toggle="tooltip" title="border-dark"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-white" data-bs-toggle="tooltip" title="border-white"></span></div><div class="col-md-6 additive"><h5>Additive</h5><hr><span class="hei-75 wid-75 bg-body d-inline-block me-2 border-dark border" data-bs-toggle="tooltip" title="border"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border-dark border-top" data-bs-toggle="tooltip" title="border-top"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border-dark border-end" data-bs-toggle="tooltip" title="border-end"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border-dark border-bottom" data-bs-toggle="tooltip" title="border-bottom"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border-dark border-start" data-bs-toggle="tooltip" title="border-start"></span></div><div class="col-sm-6"><h5>Subtractive</h5><hr><span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-0" data-bs-toggle="tooltip" title="border border-0"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-top-0" data-bs-toggle="tooltip" title="border border-top-0"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-end-0" data-bs-toggle="tooltip" title="border border-end-0"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-bottom-0" data-bs-toggle="tooltip" title="border border-bottom-0"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-start-0" data-bs-toggle="tooltip" title="border border-start-0"></span></div><div class="col-sm-6"><h5>Border width</h5><hr><span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-1" data-bs-toggle="tooltip" title="border border-1"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-2" data-bs-toggle="tooltip" title="border border-2"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-3" data-bs-toggle="tooltip" title="border border-3"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-4" data-bs-toggle="tooltip" title="border border-4"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark border-5" data-bs-toggle="tooltip" title="border border-5"></span></div><div class="col-sm-6"><h5>Border radius Sizes</h5><hr><span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark rounded-sm" data-bs-toggle="tooltip" title="rounded-sm"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-dark rounded-lg" data-bs-toggle="tooltip" title="rounded-lg"></span></div><div class="col-sm-12"><h5>Border-radius</h5><hr><span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-secondary rounded" data-bs-toggle="tooltip" title="rounded"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-secondary rounded-top" data-bs-toggle="tooltip" title="rounded-top"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-secondary rounded-right" data-bs-toggle="tooltip" title="rounded-right"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-secondary rounded-bottom" data-bs-toggle="tooltip" title="rounded-bottom"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-secondary rounded-left" data-bs-toggle="tooltip" title="rounded-left"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-secondary rounded-circle" data-bs-toggle="tooltip" title="rounded-circle"></span> <span class="hei-75 wid-120 bg-body d-inline-block me-2 border border-secondary rounded-pill" data-bs-toggle="tooltip" title="rounded-pill"></span> <span class="hei-75 wid-75 bg-body d-inline-block me-2 border border-secondary rounded-0" data-bs-toggle="tooltip" title="rounded-0"></span></div></div></div></div></div><!-- [ border ] end --><!-- [ Utility class ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Utility class</h5></div><div class="card-body"><div class="row g-4"><div class="col-md-6"><h6 class="mb-0">Margin</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-0">.m-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-5">.m-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-10">.m-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-15">.m-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-20">.m-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-25">.m-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-30">.m-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-35">.m-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-40">.m-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-45">.m-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-50">.m-50</label><h6 class="mb-0 mt-3">Margin top</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-0">.m-t-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-5">.m-t-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-10">.m-t-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-15">.m-t-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-20">.m-t-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-25">.m-t-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-30">.m-t-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-35">.m-t-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-40">.m-t-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-45">.m-t-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-top-50">.m-t-50</label><h6 class="mb-0 mt-3">Margin bottom</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-0">.m-b-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-5">.m-b-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-10">.m-b-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-15">.m-b-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-20">.m-b-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-25">.m-b-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-30">.m-b-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-35">.m-b-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-40">.m-b-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-45">.m-b-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-bottom-50">.m-b-50</label><h6 class="mb-0 mt-3">Margin left</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-0">.m-l-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-5">.m-l-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-10">.m-l-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-15">.m-l-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-20">.m-l-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-25">.m-l-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-30">.m-l-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-35">.m-l-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-40">.m-l-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-45">.m-l-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-left-50">.m-l-50</label><h6 class="mb-0 mt-3">Margin right</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-0">.m-r-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-5">.m-r-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-10">.m-r-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-15">.m-r-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-20">.m-r-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-25">.m-r-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-30">.m-r-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-35">.m-r-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-40">.m-r-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-45">.m-r-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="margin-right-50">.m-r-50</label></div><div class="col-md-6"><h6 class="mb-0">Padding</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-0">.p-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-5">.p-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-10">.p-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-15">.p-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-20">.p-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-25">.p-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-30">.p-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-35">.p-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-40">.p-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-45">.p-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-50">.p-50</label><h6 class="mb-0 mt-3">Padding top</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-0">.p-t-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-5">.p-t-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-10">.p-t-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-15">.p-t-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-20">.p-t-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-25">.p-t-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-30">.p-t-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-35">.p-t-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-40">.p-t-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-45">.p-t-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-top-50">.p-t-50</label><h6 class="mb-0 mt-3">Padding bottom</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-0">.p-b-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-5">.p-b-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-10">.p-b-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-15">.p-b-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-20">.p-b-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-25">.p-b-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-30">.p-b-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-35">.p-b-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-40">.p-b-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-45">.p-b-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-bottom-50">.p-b-50</label><h6 class="mb-0 mt-3">Padding left</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-0">.p-l-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-5">.p-l-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-10">.p-l-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-15">.p-l-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-20">.p-l-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-25">.p-l-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-30">.p-l-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-35">.p-l-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-40">.p-l-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-45">.p-l-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-left-50">.p-l-50</label><h6 class="mb-0 mt-3">Padding right</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-0">.p-r-0</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-5">.p-r-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-10">.p-r-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-15">.p-r-15</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-20">.p-r-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-25">.p-r-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-30">.p-r-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-35">.p-r-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-40">.p-r-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-45">.p-r-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="padding-right-50">.p-r-50</label></div><div class="col-md-6"><h6 class="mb-0 mt-3">Width</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-20">.wid-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-25">.wid-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-30">.wid-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-35">.wid-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-40">.wid-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-45">.wid-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-50">.wid-50</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-55">.wid-55</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-60">.wid-60</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-65">.wid-65</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-70">.wid-70</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-75">.wid-75</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-80">.wid-80</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-85">.wid-85</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-90">.wid-90</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-95">.wid-95</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-100">.wid-100</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-105">.wid-105</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-110">.wid-110</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-115">.wid-115</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-120">.wid-120</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-125">.wid-125</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-130">.wid-130</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-135">.wid-135</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-140">.wid-140</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-145">.wid-145</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="width-150">.wid-150</label></div><div class="col-md-6"><h6 class="mb-0 mt-3">Height</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-20">.hei-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-25">.hei-25</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-30">.hei-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-35">.hei-35</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-40">.hei-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-45">.hei-45</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-50">.hei-50</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-55">.hei-55</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-60">.hei-60</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-65">.hei-65</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-70">.hei-70</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-75">.hei-75</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-80">.hei-80</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-85">.hei-85</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-90">.hei-90</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-95">.hei-95</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-100">.hei-100</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-105">.hei-105</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-110">.hei-110</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-115">.hei-115</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-120">.hei-120</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-125">.hei-125</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-130">.hei-130</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-135">.hei-135</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-140">.hei-140</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-145">.hei-145</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="height-150">.hei-150</label></div><div class="col-md-12"><h6 class="mb-0 mt-3">Font-size</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-10">.f-10</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-12">.f-12</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-14">.f-14</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-16">.f-16</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-18">.f-18</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-20">.f-20</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-22">.f-22</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-24">.f-24</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-26">.f-26</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-28">.f-28</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-30">.f-30</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-32">.f-32</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-34">.f-34</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-36">.f-36</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-38">.f-38</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-40">.f-40</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-42">.f-42</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-44">.f-44</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-46">.f-46</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-48">.f-48</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-50">.f-50</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-52">.f-52</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-54">.f-54</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-56">.f-56</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-58">.f-58</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-60">.f-60</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-62">.f-62</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-64">.f-64</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-66">.f-66</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-68">.f-68</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-70">.f-70</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-72">.f-72</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-74">.f-74</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-76">.f-76</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-78">.f-78</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-size-80">.f-80</label></div><div class="col-md-6"><h6 class="mb-0 mt-3">Font weight</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-weight-100">.f-w-100</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-weight-200">.f-w-200</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-weight-300">.f-w-300</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-weight-400">.f-w-400</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-weight-500">.f-w-500</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-weight-600">.f-w-600</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-weight-700">.f-w-700</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-weight-800">.f-w-800</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="font-weight-900">.f-w-900</label></div><div class="col-md-6"><h6 class="mb-0 mt-3">Border width</h6><hr class="my-2"><label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="border-width-1">.b-wid-1</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="border-width-2">.b-wid-2</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="border-width-3">.b-wid-3</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="border-width-4">.b-wid-4</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="border-width-5">.b-wid-5</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="border-width-6">.b-wid-6</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="border-width-7">.b-wid-7</label> <label data-bs-toggle="tooltip" class="badge bg-light-secondary p-1" title="border-width-8">.b-wid-8</label></div></div></div></div></div><!-- [ Utility class ] end --><!-- [ shadows ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Shadows</h5></div><div class="card-body pc-component"><div class="shadow-none p-3 mb-3 bg-body rounded">No shadow</div><div class="shadow-sm p-3 mb-3 rounded">Small shadow</div><div class="shadow p-3 mb-3 rounded">Regular shadow</div><div class="shadow-lg p-3 mb-3 rounded">Larger shadow</div></div></div></div><!-- [ shadows ] end --><!-- [ embeds ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Embeds</h5></div><div class="card-body pc-component"><div class="row justify-content-center"><div class="col-md-8"><div class="ratio ratio-16x9"><iframe class="embed-responsive-item" src="https://www.youtube.com/embed/1jWCXJfxHQM?rel=0" allowfullscreen></iframe></div></div></div></div></div></div><!-- [ embeds ] end --><!-- [ aspect-ratios ] start --><div class="col-sm-12"><div class="card"><div class="card-header"><h5>Aspect Ratios</h5></div><div class="card-body pc-component"><div class="row g-4"><div class="col-md-6"><h5>Aspect Ratios 21 by 9</h5><hr><div class="ratio ratio-21x9"><iframe class="embed-responsive-item" src="https://www.youtube.com/embed/1jWCXJfxHQM?rel=0" allowfullscreen></iframe></div></div><div class="col-md-6"><h5>Aspect Ratios 16 by 9</h5><hr><div class="ratio ratio-16x9"><iframe class="embed-responsive-item" src="https://www.youtube.com/embed/1jWCXJfxHQM?rel=0" allowfullscreen></iframe></div></div><div class="col-md-6"><h5>Aspect Ratios 4 by 3</h5><hr><div class="ratio ratio-4x3"><iframe class="embed-responsive-item" src="https://www.youtube.com/embed/1jWCXJfxHQM?rel=0" allowfullscreen></iframe></div></div><div class="col-md-6"><h5>Aspect Ratios 1 by 1</h5><hr><div class="ratio ratio-1x1"><iframe class="embed-responsive-item" src="https://www.youtube.com/embed/1jWCXJfxHQM?rel=0" allowfullscreen></iframe></div></div></div></div></div></div><!-- [ aspect-ratios ] end --></div><!-- [ Main Content ] end --></div></div></div></section><!-- [ Main Content ] end --><!-- Required Js --><script src="../assets/js/plugins/popper.min.js"></script><script src="../assets/js/plugins/simplebar.min.js"></script><script src="../assets/js/plugins/bootstrap.min.js"></script><script src="../assets/js/fonts/custom-font.js"></script><script src="../assets/js/pcoded.js"></script><script src="../assets/js/plugins/feather.min.js"></script><div class="floting-button"><a href="https://1.envato.market/zNkqj6" class="btn btn btn-danger buynowlinks d-inline-flex align-items-center gap-2" data-bs-toggle="tooltip" title="Buy Now"><i class="ph-duotone ph-shopping-cart"></i> <span>Buy Now</span></a></div><script>layout_change('light');</script><script>change_box_container('false');</script><script>layout_caption_change('true');</script><script>layout_rtl_change('false');</script><script>preset_change('preset-1');</script><script>main_layout_change('vertical');</script><!-- [ footer apps ] start --><footer class="footer"><div class="container title mb-0"><div class="row align-items-center"><div class="col-md-8"><h2 class="mb-3">Be the first to know</h2><p class="mb-4 mb-md-0">Simply submit your email, we share you the top news related to Able Pro feature updates, roadmap, and news.</p></div><div class="col-md-4"><div class="row"><div class="col"><input type="email" class="form-control" placeholder="Enter your email"></div><div class="col-auto"><button class="btn btn-primary">Subscribe</button></div></div></div></div></div><div class="footer-top"><div class="container"><div class="row"><div class="col-md-4"><img src="../assets/images/logo-dark.svg" alt="image" class="footer-logo img-fluid mb-3"><p class="mb-4">Since 2017, More than 50K+ Developers trust the Phoenixcoded Digital Product. Mantis React is Manage under their Experienced Team Players.</p></div><div class="col-md-8"><div class="row"><div class="col-sm-4 my-3 my-sm-0"><h5 class="mb-4">Company</h5><ul class="list-unstyled footer-link"><li><a href="#" target="_blank">About</a></li><li><a href="#" target="_blank">Blog</a></li><li><a href="#" target="_blank">Team</a></li><li><a href="#" target="_blank">Free Version</a></li></ul></div><div class="col-sm-4 my-3 my-sm-0"><h5 class="mb-4">Help & Support</h5><ul class="list-unstyled footer-link"><li><a href="#" target="_blank">Documentation</a></li><li><a href="#" target="_blank">Contact us</a></li><li><a href="#" target="_blank">Support</a></li><li><a href="#" target="_blank">RoadMap</a></li></ul></div><div class="col-sm-4 my-3 my-sm-0"><h5 class="mb-4">Useful Resources</h5><ul class="list-unstyled footer-link"><li><a href="#" target="_blank">Terms & Condition</a></li><li><a href="#" target="_blank">Privacy Policy</a></li><li><a href="#" target="_blank">Licencse</a></li><li><a href="#" target="_blank">FAQ</a></li></ul></div></div></div></div></div></div><div class="footer-bottom"><div class="container"><div class="row align-items-center"><div class="col my-1"><p class="mb-0">© Handcrafted by Team <a href="https://themeforest.net/user/phoenixcoded" target="_blank">Phoenixcoded</a></p></div><div class="col-auto my-1"><ul class="list-inline footer-sos-link mb-0"><li class="list-inline-item"><a href="https://fb.com/phoenixcoded"><i class="ph-duotone ph-facebook-logo f-20"></i></a></li></ul></div></div></div></div></footer><!-- [ footer apps ] End --><script src="../../cdnjs.cloudflare.com/ajax/libs/highlight.js/9.12.0/highlight.min.js"></script><script src="../assets/js/plugins/clipboard.min.js"></script><script src="../assets/js/component.js"></script><script>// pc-component
  var elem = document.querySelectorAll('.component-list-card a');
  for (var l = 0; l < elem.length; l++) {
    var pageUrl = window.location.href.split(/[?#]/)[0];
    if (elem[l].href == pageUrl && elem[l].getAttribute('href') != '') {
      elem[l].classList.add('active');
    }
  }
  document.querySelector('#compo-menu-search').addEventListener('keyup', function () {
    var tval = document.querySelector('#compo-menu-search').value.toLowerCase();
    var elem = document.querySelectorAll('.component-list-card a');
    for (var l = 0; l < elem.length; l++) {
      var aval = elem[l].innerHTML.toLowerCase();
      var n = aval.indexOf(tval);
      if (n !== -1) {
        elem[l].style.display = 'block';
      } else {
        elem[l].style.display = 'none';
      }
    }
  });</script><div class="pct-c-btn"><a href="#" data-bs-toggle="offcanvas" data-bs-target="#offcanvas_pc_layout"><i class="ph-duotone ph-gear-six"></i></a></div><div class="offcanvas border-0 pct-offcanvas offcanvas-end" tabindex="-1" id="offcanvas_pc_layout"><div class="offcanvas-header"><h5 class="offcanvas-title">Settings</h5><button type="button" class="btn btn-icon btn-link-danger ms-auto" data-bs-dismiss="offcanvas" aria-label="Close"><i class="ti ti-x"></i></button></div><div class="pct-body customizer-body"><div class="offcanvas-body py-0"><ul class="list-group list-group-flush"><li class="list-group-item"><div class="pc-dark"><h6 class="mb-1">Theme Mode</h6><p class="text-muted text-sm">Choose light or dark mode or Auto</p><div class="row theme-color theme-layout"><div class="col-4"><div class="d-grid"><button class="preset-btn btn active" data-value="true" onclick="layout_change('light');" data-bs-toggle="tooltip" title="Light"><svg class="pc-icon text-warning"><use xlink:href="#custom-sun-1"></use></svg></button></div></div><div class="col-4"><div class="d-grid"><button class="preset-btn btn" data-value="false" onclick="layout_change('dark');" data-bs-toggle="tooltip" title="Dark"><svg class="pc-icon"><use xlink:href="#custom-moon"></use></svg></button></div></div><div class="col-4"><div class="d-grid"><button class="preset-btn btn" data-value="default" onclick="layout_change_default();" data-bs-toggle="tooltip" title="Automatically sets the theme based on user's operating system's color scheme."><span class="pc-lay-icon d-flex align-items-center justify-content-center"><i class="ph-duotone ph-cpu"></i></span></button></div></div></div></div></li><li class="list-group-item"><h6 class="mb-1">Theme Contrast</h6><p class="text-muted text-sm">Choose theme contrast</p><div class="row theme-contrast"><div class="col-6"><div class="d-grid"><button class="preset-btn btn" data-value="true" onclick="layout_theme_contrast_change('true');" data-bs-toggle="tooltip" title="True"><svg class="pc-icon"><use xlink:href="#custom-mask"></use></svg></button></div></div><div class="col-6"><div class="d-grid"><button class="preset-btn btn active" data-value="false" onclick="layout_theme_contrast_change('false');" data-bs-toggle="tooltip" title="False"><svg class="pc-icon"><use xlink:href="#custom-mask-1-outline"></use></svg></button></div></div></div></li><li class="list-group-item"><h6 class="mb-1">Custom Theme</h6><p class="text-muted text-sm">Choose your primary theme color</p><div class="theme-color preset-color"><a href="#!" data-bs-toggle="tooltip" title="Blue" class="active" data-value="preset-1"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Indigo" data-value="preset-2"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Purple" data-value="preset-3"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Pink" data-value="preset-4"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Red" data-value="preset-5"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Orange" data-value="preset-6"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Yellow" data-value="preset-7"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Green" data-value="preset-8"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Teal" data-value="preset-9"><i class="ti ti-checks"></i></a> <a href="#!" data-bs-toggle="tooltip" title="Cyan" data-value="preset-10"><i class="ti ti-checks"></i></a></div></li><li class="list-group-item"><h6 class="mb-1">Theme layout</h6><p class="text-muted text-sm">Choose your layout</p><div class="theme-main-layout d-flex align-center gap-1 w-100"><a href="#!" data-bs-toggle="tooltip" title="Vertical" class="active" data-value="vertical"><img src="../assets/images/customizer/caption-on.svg" alt="img" class="img-fluid"> </a><a href="#!" data-bs-toggle="tooltip" title="Horizontal" data-value="horizontal"><img src="../assets/images/customizer/horizontal.svg" alt="img" class="img-fluid"> </a><a href="#!" data-bs-toggle="tooltip" title="Color Header" data-value="color-header"><img src="../assets/images/customizer/color-header.svg" alt="img" class="img-fluid"> </a><a href="#!" data-bs-toggle="tooltip" title="Compact" data-value="compact"><img src="../assets/images/customizer/compact.svg" alt="img" class="img-fluid"> </a><a href="#!" data-bs-toggle="tooltip" title="Tab" data-value="tab"><img src="../assets/images/customizer/tab.svg" alt="img" class="img-fluid"></a></div></li><li class="list-group-item"><h6 class="mb-1">Sidebar Caption</h6><p class="text-muted text-sm">Sidebar Caption Hide/Show</p><div class="row theme-color theme-nav-caption"><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn active" data-value="true" onclick="layout_caption_change('true');" data-bs-toggle="tooltip" title="Caption Show"><img src="../assets/images/customizer/caption-on.svg" alt="img" class="img-fluid"></button></div></div><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn" data-value="false" onclick="layout_caption_change('false');" data-bs-toggle="tooltip" title="Caption Hide"><img src="../assets/images/customizer/caption-off.svg" alt="img" class="img-fluid"></button></div></div></div></li><li class="list-group-item"><div class="pc-rtl"><h6 class="mb-1">Theme Layout</h6><p class="text-muted text-sm">LTR/RTL</p><div class="row theme-color theme-direction"><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn active" data-value="false" onclick="layout_rtl_change('false');" data-bs-toggle="tooltip" title="LTR"><img src="../assets/images/customizer/ltr.svg" alt="img" class="img-fluid"></button></div></div><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn" data-value="true" onclick="layout_rtl_change('true');" data-bs-toggle="tooltip" title="RTL"><img src="../assets/images/customizer/rtl.svg" alt="img" class="img-fluid"></button></div></div></div></div></li><li class="list-group-item pc-box-width"><div class="pc-container-width"><h6 class="mb-1">Layout Width</h6><p class="text-muted text-sm">Choose Full or Container Layout</p><div class="row theme-color theme-container"><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn active" data-value="false" onclick="change_box_container('false')" data-bs-toggle="tooltip" title="Full Width"><img src="../assets/images/customizer/full.svg" alt="img" class="img-fluid"></button></div></div><div class="col-6"><div class="d-grid"><button class="preset-btn btn-img btn" data-value="true" onclick="change_box_container('true')" data-bs-toggle="tooltip" title="Fixed Width"><img src="../assets/images/customizer/fixed.svg" alt="img" class="img-fluid"></button></div></div></div></div></li><li class="list-group-item"><div class="d-grid"><button class="btn btn-light-danger" id="layoutreset">Reset Layout</button></div></li></ul></div></div></div></body>
<!-- Mirrored from ableproadmin.com/elements/bc_extra.html by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Oct 2024 04:55:27 GMT -->
</html>