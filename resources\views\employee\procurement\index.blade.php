@extends('employee.layout.master')

@section('content')
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ url('/') }}">Home</a>
                        </li>
                        <li class="breadcrumb-item" aria-current="page">
                            Procurement
                        </li>
                    </ul>
                </div>
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Procurement</h2>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <!-- [ sample-page ] start -->
        <div class="col-sm-12">
            <div class="card table-card">
                <div class="card-body">
                    <div class="text-end p-4 pb-sm-2">
                        @if ($role == 'Employee')
                            <a href="{{ url('/employee/procurement' . '/create') }}"
                                class="btn btn-primary d-inline-flex align-items-center gap-2"><i
                                    class="ti ti-plus f-18"></i>
                                Add New</a>
                        @endif
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="pc-dt-simple">
                            <thead>
                                <tr>
                                    <th>No.</th>
                                    <th>Product Name</th>
                                    <th>Product Category</th>
                                    <th>Requester</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                    <th>Supervisor Approval</th>
                                    <th>Manager Approval</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($procurement as $i => $item)
                                    <tr>
                                        <td>{{ $i + 1 }}</td>
                                        <td>{{ $item->product_name }}</td>
                                        <td>{{ $item->product_category }}</td>
                                        <td>{{ $item->user->name }}</td>
                                        <td>{{ $item->price }}</td>
                                        <td>{{ $item->quantity }}</td>
                                        <td>{{ $item->price * $item->quantity }}</td>
                                        <td>{{ $item->supervisor_approval }}</td>
                                        <td>{{ $item->manager_approval }}</td>
                                        <td>{{ $item->status }}</td>
                                        <td class="text-center">
                                            <ul class="list-inline me-auto mb-0">
                                                @if ($item->status == 'Waiting for Supervisor Approval' && $role == 'Employee')
                                                    <li class="list-inline-item align-bottom" data-bs-toggle="tooltip"
                                                        title="Edit">
                                                        <a href="{{ url('/employee/procurement/' . $item->id . '/edit') }}"
                                                            class="avtar avtar-xs btn-link-success btn-pc-default"><i
                                                                class="ti ti-edit-circle f-18"></i></a>
                                                    </li>
                                                    <li class="list-inline-item align-bottom" data-bs-toggle="tooltip"
                                                        title="Delete">
                                                        <a href="#" onclick="confirmDelete({{ $item->id }})"
                                                            class="avtar avtar-xs btn-link-danger btn-pc-default"><i
                                                                class="ti ti-trash f-18"></i></a>
                                                        {!! Form::open([
                                                            'method' => 'DELETE',
                                                            'url' => ['/employee/procurement', $item->id],
                                                            'style' => 'display:inline',
                                                            'id' => 'deleteForm' . $item->id,
                                                        ]) !!}
                                                        {!! Form::close() !!}
                                                    </li>
                                                @endif

                                                @if ($role == 'Supervisor' && $item->status == 'Waiting for Supervisor Approval')
                                                    <li class="list-inline-item align-bottom" data-bs-toggle="tooltip"
                                                        title="Approve">
                                                        <a href="{{ url('/employee/procurement/approve/' . $item->id) }}"
                                                            class="avtar avtar-xs btn-link-success btn-pc-default"><i
                                                                class="ti ti-check f-18"></i></a>
                                                    </li>
                                                    <li class="list-inline-item align-bottom" data-bs-toggle="tooltip"
                                                        title="Deny">
                                                        <a href="{{ url('/employee/procurement/deny/' . $item->id) }}"
                                                            class="avtar avtar-xs btn-link-danger btn-pc-default"><i
                                                                class="ti ti-x f-18"></i></a>
                                                    </li>
                                                @endif

                                                @if ($role == 'Manager' && $item->status == 'Waiting for Manager Approval')
                                                    <li class="list-inline-item align-bottom" data-bs-toggle="tooltip"
                                                        title="Approve">
                                                        <a href="{{ url('/employee/procurement/approve/' . $item->id) }}"
                                                            class="avtar avtar-xs btn-link-success btn-pc-default"><i
                                                                class="ti ti-check f-18"></i></a>
                                                    </li>
                                                    <li class="list-inline-item align-bottom" data-bs-toggle="tooltip"
                                                        title="Deny">
                                                        <a href="{{ url('/employee/procurement/deny/' . $item->id) }}"
                                                            class="avtar avtar-xs btn-link-danger btn-pc-default"><i
                                                                class="ti ti-x f-18"></i></a>
                                                    </li>
                                                @endif
                                            </ul>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('scripts')
    <script>
        function confirmDelete(itemId) {
            // Use browser's built-in confirm dialog
            var result = confirm("Are you sure you want to delete this record?");
            if (result) {
                // If user confirms, submit the form
                var form = document.getElementById('deleteForm' + itemId); // Find the corresponding form by ID
                if (form) {
                    form.submit(); // Submit the form
                }
            }
        }
    </script>
@stop
