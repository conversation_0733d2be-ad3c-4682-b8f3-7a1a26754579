<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Game;
use App\Models\GameConfig;
use App\Models\GuestScore;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GameConfigController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'admin') {
                abort(403, 'Access Denied. Only admin role is allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $gameConfigs = GameConfig::with(['client', 'game', 'guestScores'])
                                ->paginate(15);

        // Get clients and games for filtering
        $clients = Client::where('is_have_minigame', true)
                        ->orderBy('client_name')
                        ->get();

        $games = Game::where('is_active', true)
                    ->orderBy('display_name')
                    ->get();

        return view('admin.game-configs.index', compact('gameConfigs', 'clients', 'games'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $clients = Client::where('is_have_minigame', true)->get();
        $games = Game::where('is_active', true)->get();

        return view('admin.game-configs.create', compact('clients', 'games'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'client_id' => 'required|exists:clients,id',
            'game_id' => 'required|exists:games,id',
            'game_title' => 'required|string|max:255',
            'top_players' => 'required|integer|min:1|max:50',
            'config_data' => 'nullable|array',
        ]);

        // Check if configuration already exists
        $existingConfig = GameConfig::where('client_id', $request->client_id)
                                  ->where('game_id', $request->game_id)
                                  ->first();

        if ($existingConfig) {
            alert()->error('Error', 'Game configuration already exists for this client and game combination.');
            return redirect()->back()->withInput();
        }

        GameConfig::create($request->all());

        alert()->success('Success', 'Game configuration created successfully!');
        return redirect()->route('admin.game-configs.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(GameConfig $gameConfig)
    {
        $gameConfig->load(['client', 'game', 'guestScores.guest']);
        $leaderboard = $gameConfig->leaderboard($gameConfig->top_players);

        return view('admin.game-configs.show', compact('gameConfig', 'leaderboard'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(GameConfig $gameConfig)
    {
        $clients = Client::where('is_have_minigame', true)->get();
        $games = Game::where('is_active', true)->get();

        return view('admin.game-configs.edit', compact('gameConfig', 'clients', 'games'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, GameConfig $gameConfig)
    {
        $request->validate([
            'game_title' => 'required|string|max:255',
            'top_players' => 'required|integer|min:1|max:50',
            'config_data' => 'nullable|array',
        ]);

        $gameConfig->update($request->all());

        alert()->success('Success', 'Game configuration updated successfully!');
        return redirect()->route('admin.game-configs.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(GameConfig $gameConfig)
    {
        $gameConfig->delete();

        alert()->success('Success', 'Game configuration deleted successfully!');
        return redirect()->route('admin.game-configs.index');
    }

    /**
     * Start a game configuration
     */
    public function start(GameConfig $gameConfig)
    {
        $gameConfig->update([
            'is_starting' => true,
            'is_done' => false,
        ]);

        alert()->success('Success', 'Game started successfully!');
        return redirect()->back();
    }

    /**
     * Stop a game configuration
     */
    public function stop(GameConfig $gameConfig)
    {
        $gameConfig->update([
            'is_starting' => false,
            'is_done' => true,
        ]);

        // Update ranks for all completed scores
        $this->updateGameRanks($gameConfig);

        alert()->success('Success', 'Game stopped successfully!');
        return redirect()->back();
    }

    /**
     * Reset a game configuration (clear all scores)
     */
    public function reset(GameConfig $gameConfig)
    {
        // Delete all existing scores for this game
        GuestScore::where('game_config_id', $gameConfig->id)->delete();

        $gameConfig->update([
            'is_starting' => false,
            'is_done' => false,
        ]);

        alert()->success('Success', 'Game reset successfully! All scores have been cleared.');
        return redirect()->back();
    }

    /**
     * Update ranks for all completed scores in a game
     */
    private function updateGameRanks(GameConfig $gameConfig)
    {
        $scores = GuestScore::where('game_config_id', $gameConfig->id)
                           ->where('is_completed', true)
                           ->orderBy('score', 'desc')
                           ->orderBy('completed_at', 'asc')
                           ->get();

        foreach ($scores as $index => $score) {
            $score->update(['rank' => $index + 1]);
        }
    }
}
