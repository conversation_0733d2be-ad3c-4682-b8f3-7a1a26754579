<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leaderboard - {{ $triviaConfig->game_title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-pink: #ec4899;
            --secondary-pink: #f8d7da;
            --light-pink: #fdf2f8;
            --accent-pink: #be185d;
            --text-dark: #721c24;
            --text-light: #9ca3af;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--light-pink) 0%, rgba(251, 207, 232, 0.3) 100%);
            min-height: 100vh;
            color: var(--text-dark);
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        .header-card {
            text-align: center;
            margin-bottom: 30px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .game-title {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-pink);
            margin-bottom: 10px;
        }

        .client-name {
            font-size: 1.2rem;
            color: var(--text-light);
            margin-bottom: 20px;
        }

        .trophy-icon {
            font-size: 3rem;
            color: #ffd700;
            margin-bottom: 15px;
        }

        .user-score-card {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            color: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
        }

        .user-score-card h4 {
            margin-bottom: 10px;
        }

        .user-score {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .leaderboard-card {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(251, 207, 232, 0.5);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
        }

        .leaderboard-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .leaderboard-header h3 {
            color: var(--primary-pink);
            font-weight: 700;
            margin-bottom: 10px;
        }

        .leaderboard-item {
            display: flex;
            align-items: center;
            padding: 20px;
            margin-bottom: 15px;
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(251, 207, 232, 0.3);
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .leaderboard-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.15);
        }

        .leaderboard-item.winner {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
            border-color: rgba(255, 193, 7, 0.5);
        }

        .leaderboard-item.user-highlight {
            background: linear-gradient(135deg, rgba(236, 72, 153, 0.1) 0%, rgba(190, 24, 93, 0.1) 100%);
            border-color: var(--primary-pink);
        }

        .rank-badge {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.2rem;
            margin-right: 20px;
            background: var(--light-pink);
            color: var(--text-dark);
        }

        .rank-badge.gold {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #b8860b;
            font-size: 1.5rem;
        }

        .rank-badge.silver {
            background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
            color: #696969;
            font-size: 1.5rem;
        }

        .rank-badge.bronze {
            background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
            color: #8b4513;
            font-size: 1.5rem;
        }

        .player-info {
            flex: 1;
            margin-right: 20px;
        }

        .player-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-dark);
        }

        .player-time {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .score-display {
            text-align: right;
        }

        .score-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-pink);
            display: block;
        }

        .score-label {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .empty-leaderboard {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-light);
        }

        .empty-leaderboard i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 0 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
            color: white;
            text-decoration: none;
        }

        .btn-outline {
            background: rgba(255, 255, 255, 0.9);
            color: var(--primary-pink);
            border: 2px solid var(--primary-pink);
        }

        .btn-outline:hover {
            background: var(--primary-pink);
            color: white;
            text-decoration: none;
        }

        .refresh-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--accent-pink) 100%);
            color: white;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(236, 72, 153, 0.3);
        }

        .refresh-button:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .game-title {
                font-size: 2rem;
            }
            
            .leaderboard-item {
                padding: 15px;
            }
            
            .rank-badge {
                width: 50px;
                height: 50px;
                margin-right: 15px;
            }
            
            .player-name {
                font-size: 1.1rem;
            }
            
            .score-value {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header-card">
            <div class="trophy-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <h1 class="game-title">{{ $triviaConfig->game_title }}</h1>
            <p class="client-name">{{ $client->client_name }}</p>
            <p class="text-muted">Top {{ $triviaConfig->top_players }} Players</p>
        </div>

        <!-- User Score (if available) -->
        @if($userScore)
        <div class="user-score-card">
            <h4><i class="fas fa-user me-2"></i>Your Score</h4>
            <div class="user-score">{{ number_format($userScore->score, 1) }}%</div>
            <p class="mb-0">Completed {{ $userScore->created_at->diffForHumans() }}</p>
        </div>
        @endif

        <!-- Leaderboard -->
        <div class="leaderboard-card">
            <div class="leaderboard-header">
                <h3><i class="fas fa-crown me-2"></i>Leaderboard</h3>
                <p class="text-muted">Live rankings updated in real-time</p>
            </div>

            @if($leaderboard->count() > 0)
                @foreach($leaderboard as $index => $score)
                <div class="leaderboard-item {{ $index < 3 ? 'winner' : '' }} {{ $userScore && $userScore->id === $score->id ? 'user-highlight' : '' }}">
                    <div class="rank-badge {{ $index === 0 ? 'gold' : ($index === 1 ? 'silver' : ($index === 2 ? 'bronze' : '')) }}">
                        @if($index === 0)
                            🥇
                        @elseif($index === 1)
                            🥈
                        @elseif($index === 2)
                            🥉
                        @else
                            {{ $index + 1 }}
                        @endif
                    </div>
                    <div class="player-info">
                        <div class="player-name">
                            {{ $score->guest->name }}
                            @if($userScore && $userScore->id === $score->id)
                                <i class="fas fa-star text-warning ms-2" title="Your score"></i>
                            @endif
                        </div>
                        <div class="player-time">{{ $score->created_at->format('M d, Y H:i') }}</div>
                    </div>
                    <div class="score-display">
                        <span class="score-value">{{ number_format($score->score, 1) }}%</span>
                        <div class="score-label">Score</div>
                    </div>
                </div>
                @endforeach
            @else
                <div class="empty-leaderboard">
                    <i class="fas fa-trophy"></i>
                    <h4>No Scores Yet</h4>
                    <p>Be the first to play and claim the top spot!</p>
                </div>
            @endif
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            @if(!$userScore)
                <a href="{{ route('guest.minigame.trivia', [$client->id, $client->unique_url]) }}" class="btn btn-primary">
                    <i class="fas fa-play"></i> Play Now
                </a>
            @else
                <p class="text-muted mb-3">
                    <i class="fas fa-info-circle me-1"></i>
                    You have already played this game. Thank you for participating!
                </p>
            @endif
            
            <a href="{{ route('guest.minigame.index', [$client->id, $client->unique_url]) }}" class="btn btn-outline">
                <i class="fas fa-gamepad"></i> Other Games
            </a>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-button" onclick="refreshLeaderboard()" title="Refresh Leaderboard">
        <i class="fas fa-sync-alt" id="refresh-icon"></i>
    </button>

    <script>
        // Auto-refresh leaderboard every 30 seconds
        setInterval(refreshLeaderboard, 30000);

        function refreshLeaderboard() {
            const refreshIcon = document.getElementById('refresh-icon');
            refreshIcon.classList.add('fa-spin');
            
            // Simulate refresh (in a real app, you'd make an AJAX call)
            setTimeout(() => {
                refreshIcon.classList.remove('fa-spin');
                // You could update the leaderboard data here via AJAX
                location.reload();
            }, 1000);
        }

        // Show success/error messages
        @if(session('success'))
            setTimeout(() => {
                alert('{{ session('success') }}');
            }, 500);
        @endif

        @if(session('error'))
            setTimeout(() => {
                alert('{{ session('error') }}');
            }, 500);
        @endif
    </script>
</body>
</html>
