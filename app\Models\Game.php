<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Game extends Model
{
    use HasFactory;

    protected $fillable = [
        'minigame_name',
        'view_path',
        'display_name',
        'description',
        'game_thumbnail',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function gameConfigs()
    {
        return $this->hasMany(GameConfig::class);
    }

    public function clients()
    {
        return $this->belongsToMany(Client::class, 'game_configs')
                    ->withPivot(['game_title', 'top_players', 'is_starting', 'is_done', 'config_data'])
                    ->withTimestamps();
    }
}
