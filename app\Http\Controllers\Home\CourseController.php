<?php

namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use App\Models\Course;
use Illuminate\Http\Request;

class Course<PERSON>ontroller extends Controller
{
    public function index(Request $request){
        $data['course_name'] = isset($request->name) ? $request->name : '';
        $data['courses'] = Course::where([
            ['is_active', '=', true],
            ['course_name', 'like', '%'.$data['course_name'].'%']
        ])->get();

        return view('home.courses.index', $data);
    }

    public function show(Request $request, $id){
        $data['course'] = Course::where([
            'id' => $id,
            'is_active' => true,
        ])->with(['instructor.detailInstructor', 'reviews.user', 'enrolled', 'category', 'lessons.lectures'])->first();

        if($data['course'] == null){
            return abort(404);
        }

        return view('home.courses.detail', $data);
    }
}
