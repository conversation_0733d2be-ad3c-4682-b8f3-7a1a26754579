<div class="form-group{{ $errors->has('product_name') ? 'has-error' : '' }}">
    {!! Form::label('product_name', 'Product Name', ['class' => 'control-label']) !!}
    {!! Form::text(
        'product_name',
        null,
        '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('product_name', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('product_category') ? 'has-error' : '' }}">
    {!! Form::label('product_category', 'Product Category', ['class' => 'control-label']) !!}
    {!! Form::select(
        'product_category',
        json_decode('{"Office Stationery":"Office Stationery","Electronic":"Electronic","Other":"Other"}', true),
        null,
        '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('product_category', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('price') ? 'has-error' : '' }}">
    {!! Form::label('price', 'Price', ['class' => 'control-label']) !!}
    {!! Form::number(
        'price',
        null,
        '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('price', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('quantity') ? 'has-error' : '' }}">
    {!! Form::label('quantity', 'Quantity', ['class' => 'control-label']) !!}
    {!! Form::number(
        'quantity',
        null,
        '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('quantity', '<p class="help-block">:message</p>') !!}
</div>
<br>

<div class="form-group" align="right">
    {!! Form::submit($formMode === 'edit' ? 'Update' : 'Create', ['class' => 'btn btn-primary']) !!}
    {!! Form::reset('Reset', ['class' => 'btn btn-warning']) !!}
    <a href="#" onClick="javascript:history.go(-1)" class="btn btn-danger">Cancel and Back</a>
</div>
