/* Jakarta Sans Font Family */
@font-face {
    font-family: 'Jakarta Sans';
    src: url('../fonts/Jakarta Sans.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Apply Jakarta Sans to all admin and client interfaces */
body,
.sidebar,
.navbar,
.card,
.btn,
.form-control,
.form-select,
.form-label,
.table,
.modal,
.alert,
.badge,
.breadcrumb,
.nav,
.dropdown-menu,
h1, h2, h3, h4, h5, h6,
p, span, div, a, li, td, th,
.heading-font,
.text-primary,
.text-secondary,
.text-success,
.text-danger,
.text-warning,
.text-info,
.text-light,
.text-dark,
.text-muted {
    font-family: 'Jakarta Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Specific weight variations for Jakarta Sans */
.fw-light {
    font-weight: 300 !important;
}

.fw-normal {
    font-weight: 400 !important;
}

.fw-medium {
    font-weight: 500 !important;
}

.fw-semibold {
    font-weight: 600 !important;
}

.fw-bold {
    font-weight: 700 !important;
}

.fw-bolder {
    font-weight: 800 !important;
}

/* Ensure consistent font rendering */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Admin specific elements */
.admin-layout,
.admin-sidebar,
.admin-navbar,
.admin-content {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Client dashboard specific elements */
.client-layout,
.client-sidebar,
.client-navbar,
.client-content {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Form elements */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
textarea,
select {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Button elements */
.btn,
button,
input[type="button"],
input[type="submit"],
input[type="reset"] {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Navigation elements */
.nav-link,
.navbar-nav,
.navbar-brand,
.breadcrumb-item {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Table elements */
.table th,
.table td,
.table-responsive {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Modal elements */
.modal-title,
.modal-body,
.modal-footer {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Card elements */
.card-title,
.card-subtitle,
.card-text,
.card-header,
.card-body,
.card-footer {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Alert elements */
.alert,
.alert-heading {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Badge elements */
.badge {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Dropdown elements */
.dropdown-item,
.dropdown-header,
.dropdown-divider {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Pagination elements */
.pagination .page-link {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Toast elements */
.toast,
.toast-header,
.toast-body {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Progress elements */
.progress,
.progress-bar {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* List group elements */
.list-group-item {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Accordion elements */
.accordion-button,
.accordion-body {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Tab elements */
.nav-tabs .nav-link,
.nav-pills .nav-link,
.tab-content {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Offcanvas elements */
.offcanvas-title,
.offcanvas-body {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Tooltip and Popover elements */
.tooltip,
.popover {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Custom minigame elements */
.minigame-container,
.trivia-question,
.answer-btn,
.score-display,
.leaderboard {
    font-family: 'Jakarta Sans', sans-serif !important;
}

/* Ensure font loads properly on all devices */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    body {
        font-family: 'Jakarta Sans', sans-serif !important;
    }
}

/* Print styles */
@media print {
    * {
        font-family: 'Jakarta Sans', sans-serif !important;
    }
}
